class Flutter {
  constructor() {
    console.log('flutter初始化')
    // 获取adjust渠道源
    setTimeout(() => {
      window.flutter_inappwebview && window.flutter_inappwebview.callHandler('JsAndroid', JSON.stringify({ method: 'adjustData' }))
    }, 2800)
  }
  // registerEvent() {
  //   // 获取归因渠道
  //     // const getDdJustDataResult = (data) => {
  //     //   appStore.adjustAttribution = data
  //     // }
  //   // 根 flutter webview 获取返回提示
  //   window.getBackToastTip = () => {
  //     return JSON.stringify({
  //       back_tip: '再按一次退出应用'
  //     })
  //   }
  // }
  // 1.加载激励广告
  // 2.展示激励广告
  // 3.加载插屏广告
  // 4.自动加载插屏广告
  // 5.展示插屏广告
  // 6.展示自动加载的插屏广告
  // 7自动加载激励视频
  // 8展示自动加载的激励视频
  initTopOnRvAds() {
    if (!window.flutter_inappwebview) return Promise.resolve()
    console.log('load ad')
    window.flutter_inappwebview.callHandler('JsAndroid', JSON.stringify({ method: 'topOnAds', type: '1', data: 'tysolutionadvertiseuniquekey' }))
    window.loadAdData = (data) => {
      console.log(data, 'loadAdData')
    }

    return new Promise((resolve, reject) => {
      window.loadAdSuccess = (status) => {
        console.log(status)
        if(status === 5) {
          resolve()
        }
      }
      window.loadAdStatus = (status) => {
        if(status === 0) {
          reject()
          this.initTopOnRvAds()
        }
      }
    })
  }

  initTopOnInterstitialAds() {
    if (!window.flutter_inappwebview) return Promise.resolve()
    window.flutter_inappwebview.callHandler('JsAndroid', JSON.stringify({ method: 'topOnAds', type: '3', data: 'tysolutionadvertiseuniquekey' }))
    window.interstitialAdData = (data) => {
      console.log(data, 'loadAdData')
    }
    return new Promise((resolve, reject) => {
      window.interstitialAdStatus = (i) => {
        if(i === 5) {
          resolve()
        } else if(i === 0) {
          reject()
          this.initTopOnInterstitialAds()
        }
      }
    })
  }
  getTopOnRvAds(type) {
    let location = ''
    switch (type) {
      case 1:
        location = 'rare_gem'
        break
      case 2:
        location = 'epic_gem'
        break
      case 3:
        location = 'extra_coins'
        break
      case 4:
        location = 'skill_level_up'
        break
      case 5:
        location = 'extra_physical'
        break
      case 6:
        location = 'level_double_reward'
        break
      case 7:
        location = 'random_skill_change'
        break
      case 11:
        location = 'battle_sweep'
        break
      case 12:
        location = 'spirit_animal'
        break
    }
    logReport('ugd_rv_should', {
      location: location,
      ad_id: 'testid'
    })
    return new Promise((resolve, reject) => {
      window.loadAdStatus = (status) => {
        console.log('广告状态', status)
        //  广告加载失败 0
        //  广告开始播放 1
        //  激励成功，建议在此回调中下发奖励 2
        //  广告被关闭 3 (完播与否 关闭广告时都会触发)
        //  广告播放失败 4
        //  广告加载成功 5
        //  广告完播 6 中途关闭广告不会触发
        //  点击广告 7
        if (status === 1) {
          logReport('ugd_rv_play', {
            location: location,
            ad_id: 'testid'
          })
        }
        // 如果看完了广告，status为2，关闭后还会继续触发status为3
        else if (status === 2) {
          resolve()
          console.log(window.advObj, 'load again')
          window.advObj.initAd()
        }
        // 如果中途关闭广告，status为3，且不会触发status为2
        else if (status === 3 || status === 4) {
          reject()
          console.log(window.advObj, 'load again')
          window.advObj.initAd()
        }
        else if (status === 6) {
          resolve()
          console.log(window.advObj, 'load again')
          logReport('ugd_rv_over', {
            location: location,
            ad_id: 'testid'
          })
          window.advObj.initAd()
        } else if (status === 7) {
          logReport('ugd_rv_click', {
            location: location,
            ad_id: 'testid'
          })
          console.log('click')
        }
      }
      window.flutter_inappwebview.callHandler('JsAndroid', JSON.stringify({ method: 'topOnAds', type: '2', data: 'tysolutionadvertiseuniquekey' }))
    })
  }
  getTopOnInterstitialAds(type) {
    let location = 'level_pass'
    logReport('ugd_Interstitial_should', {
      location: location,
      ad_id: 'testid'
    })
    return new Promise((resolve, reject) => {
      window.interstitialAdStatus = (i) => {
        console.log('广告状态', i)
        // 0加载失败
        // 1结束播放
        // 2 展示成功
        // 3点击广告
        // 4关闭广告
        if (i === 1) {
          resolve()
          logReport('ugd_Interstitial_over', {
            location: location,
            ad_id: 'testid'
          })
          window.advObj.initAd()
        }
        else if (i === 2) {
          logReport('ugd_Interstitial_play', {
            location: location,
            ad_id: 'testid'
          })
        }
        else if (i === 3) {
          logReport('ugd_Interstitial_click', {
            location: location,
            ad_id: 'testid'
          })
          console.log('click')
        }
        else if (i === 4) {
          resolve()
          console.log(window.advObj, 'load again')
          window.advObj.initAd()
        }
      }
      window.flutter_inappwebview.callHandler('JsAndroid', JSON.stringify({ method: 'topOnAds', type: '5', data: 'tysolutionadvertiseuniquekey' }))
    })
  }

  iOSAppTracking() {
    return new Promise((resolve) => {
      window.iosAppTrack = (res) => {
        resolve(res)
      }
      window.flutter_inappwebview.callHandler('JsAndroid', JSON.stringify({ method: 'iOSAppTracking' }))
    })
  }
  applePay(product_id, orderId) {
    console.log('applePayParams', product_id, orderId)

    return new Promise((resolve, reject) => {
      window.applePaySuccess = (res) => {
        console.log('applePaySuccess', res)
        resolve(JSON.parse(res))
      }
      window.applePayFail = (res) => {
        console.log('applePayFail', res)
        reject(res)
      }
      window.flutter_inappwebview.callHandler('JsAndroid', JSON.stringify({ method: 'applePay', data: product_id, orderId }))
    })
  }
  googlePay(product_id, orderId) {
    console.log('googlePayParams', product_id, orderId)
    return new Promise((resolve, reject) => {
      window.googlePaySuccess = (res) => {
        console.log('googlePaySuccess', res)
        resolve(res)
      }
      window.googlePayFail = (res) => {
        console.log('googlePayFail', res)
        reject(res)
      }
      window.flutter_inappwebview.callHandler('JsAndroid', JSON.stringify({ method: 'googlePay', data: product_id, orderId }))
    })
  }
  // trackEvent(data) {
  //   if (window.flutter_inappwebview) {
  //     window.flutter_inappwebview.callHandler('JsAndroid', JSON.stringify({ method: 'adjustEvent', data: data.eventToken }))
  //   }
  // }
  getInfoSystemInfo = () => {
    return new Promise((resolve) => {
      window.infoDataResult = (info) => {
        console.log(info, 'kkkk')
        // const appStore = useAppStore()
        // appStore.setSystemInfo({ ...info, windowHeight: info.screenHeight, windowWidth: info.screenWidth })
        resolve(info)
      }
      setTimeout(() => {
        window.flutter_inappwebview?.callHandler('JsAndroid', JSON.stringify({ method: 'infoData' }))
      }, 2000)
    })
  }
}

window.flutterObj = new Flutter()
