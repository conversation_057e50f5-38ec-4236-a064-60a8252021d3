class Advertisement {
  static state = {};

  constructor(state) {
    // init
    this.state = {
      isShowAd: false,
      advType: "", // 拉起广告位置，用于上传埋点事件。
      flutterRvAdvStatus: false,
      flutterInterstitialAdvStatus: false,
      ugAdvStatus: 0, // 1是加载完成 其他为没有完成
      playCompleted: false, // 是否完成播放
      battleRv: false // 是否在战斗后拉起激励广告
    }
    state && (this.state = { ...this.state, ...state });
  }

  initAd() {
    console.log('init ad')
    window.flutterObj.initTopOnRvAds().then(()=>{
      console.log('rv ad success')
      this.state.flutterAdvStatus = true;
    }).catch(() => {
      console.log('rv ad fail')
    });
    window.flutterObj.initTopOnInterstitialAds().then(()=>{
      console.log('interstitial ad success')
      this.state.flutterInterstitialAdvStatus = true;
    }).catch(() => {
      console.log('interstitial ad fail')
    });
  }

  playRvAd(type) {
    console.log(type, 'playRvAd')
    if(type === 6) {
      this.state.battleRv = true
    }
    return new Promise((resolve, reject) => {
      if (window.JsAndroid) {
        // 1. 检查是否正在显示广告
        if (this.state.isShowAd || window.advsParams.status !== 1) {
          resolve(false);
          return;
        }
        window.OG_H5_GAME_SDK?.showAds();
        // 3. 设置广告显示状态
        this.state.isShowAd = true;
        // 4. 显示你的广告
        new Promise((resolveType, rj) => {
          window.ug_showAds = ({ type }) => {
            console.log("showTest");
            // 激励成功 1
            // 广告播放中点击了广告内容 2
            // 被关闭 3
            // 已完播 6
            // 开始播放 7
            // 播放失败 8
            if (type === 1) {
              console.log("showTest1");
              resolveType(type);
            } else if (type === 3 || type === 8) {
              console.log("showTest3");
              // 被关闭
              resolveType(type);
              if (type === 8) {
                console.log("showTest8");
                window.OG_H5_GAME_SDK.loadAds({ id: "n66cd31be71e14" });
              }
            } else if (type === 6) {
              // 已完播
              console.log("showTest6");
            } else if (type === 7) {
              // 开始播放
              console.log("showTest7");
            }
          };
        }).then((type) => {
          if (type === 1) {
            resolve(true);
            this.state.isShowAd = false;
          }
        });
      } else if (window.flutter_inappwebview) {
        window.flutterObj.getTopOnRvAds(type).then(()=>{
          resolve();
          this.state.isShowAd = false;
        }).catch(() => {
          reject();
        });
      } else {
        alert("点击确定获取奖励");
        resolve(true);
      }
    }).catch((err) => {
      console.error(err);
    });
  }

  playInterstitialAd() {
    if(this.state.battleRv) {
      this.state.battleRv = false
      return Promise.resolve()
    }
    return new Promise((resolve, reject) => {
      if (window.flutter_inappwebview) {
        window.flutterObj.getTopOnInterstitialAds().then(()=>{
          resolve();
          this.state.isShowAd = false;
        }).catch(() => {
          reject();
        });
      } else {
        resolve(true);
      }
    }).catch((err) => {
      console.error(err);
    });
  }
}

window.advObj = new Advertisement()