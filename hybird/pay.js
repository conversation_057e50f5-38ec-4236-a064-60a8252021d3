console.log('pay')
class Pay {
  static isPaying = false;
  constructor() {
    console.log('pay初始化')
  }
  async pay(product_id) {
    if (this.isPaying) {
      return
    }
    this.isPaying = true
    console.log('pay', product_id)
    logReport('ugd_purchase_should', {
      product_id
    })
    const res = await fetch('https://logs.uggamer.com/order/createOrderId?productId=' + product_id, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json'
      }
    }).then((res) => {
      return res.json()
    }).catch((err) => {
      console.log(err)
    })
    console.log('pay', res)
    if (res.code === 200) {
      return new Promise((resolve, reject) => {
        if (window.flutter_inappwebview) {
          // window.flutterObj.googlePay('com.ol.fishstorm.survival.io.shelltype6', res.data[0].orderId).then(async (googleRes) => {
          window.flutterObj.googlePay(res.data[0].googleProductId, res.data[0].orderId).then(async (googleRes) => {
            console.log(googleRes, 'googleRes')
            logReport('ugd_purchase', {
              product_id
            })

            const res1 = await fetch('https://logs.uggamer.com/product/checkProductPayResult?orderId=' + res.data[0].orderId, {
              method: 'get',
              headers: {
                'Content-Type': 'application/json'
              }
            }).then((resLog) => {
              return resLog.json()
            }).catch((err) => {
              console.log(err)
            })
            console.log(res1, 'res1')
            if(res1.data[0].orderStatus === 2) {
              logReport('ugd_purchase_suc')
            }
          }).catch((err) => {
            logReport('ugd_purchase_fail')
            reject(err)
          }).finally(() => {
            this.isPaying = false
          })
        } else {
          resolve(true)
        }
      })
    }
  }
}

window.payObj = new Pay()