(()=>{var e={713:e=>{e.exports=function(e){function t(e,t){return e<<t|e>>>32-t}function n(e,t){var n,o,i,r,s;return i=2147483648&e,r=2147483648&t,s=(1073741823&e)+(1073741823&t),(n=1073741824&e)&(o=1073741824&t)?2147483648^s^i^r:n|o?1073741824&s?3221225472^s^i^r:1073741824^s^i^r:s^i^r}function o(e,o,i,r,s,a,c){return e=n(e,n(n(function(e,t,n){return e&t|~e&n}(o,i,r),s),c)),n(t(e,a),o)}function i(e,o,i,r,s,a,c){return e=n(e,n(n(function(e,t,n){return e&n|t&~n}(o,i,r),s),c)),n(t(e,a),o)}function r(e,o,i,r,s,a,c){return e=n(e,n(n(function(e,t,n){return e^t^n}(o,i,r),s),c)),n(t(e,a),o)}function s(e,o,i,r,s,a,c){return e=n(e,n(n(function(e,t,n){return t^(e|~n)}(o,i,r),s),c)),n(t(e,a),o)}function a(e){var t,n="",o="";for(t=0;t<=3;t++)n+=(o="0"+(e>>>8*t&255).toString(16)).substr(o.length-2,2);return n}var c,l,h,d,f,u,g,p,_,y=Array();for(e=function(e){e=e.replace(/\r\n/g,"\n");for(var t="",n=0;n<e.length;n++){var o=e.charCodeAt(n);o<128?t+=String.fromCharCode(o):o>127&&o<2048?(t+=String.fromCharCode(o>>6|192),t+=String.fromCharCode(63&o|128)):(t+=String.fromCharCode(o>>12|224),t+=String.fromCharCode(o>>6&63|128),t+=String.fromCharCode(63&o|128))}return t}(e),y=function(e){for(var t,n=e.length,o=n+8,i=16*((o-o%64)/64+1),r=Array(i-1),s=0,a=0;a<n;)s=a%4*8,r[t=(a-a%4)/4]=r[t]|e.charCodeAt(a)<<s,a++;return s=a%4*8,r[t=(a-a%4)/4]=r[t]|128<<s,r[i-2]=n<<3,r[i-1]=n>>>29,r}(e),u=1732584193,g=4023233417,p=2562383102,_=271733878,c=0;c<y.length;c+=16)l=u,h=g,d=p,f=_,u=o(u,g,p,_,y[c+0],7,3614090360),_=o(_,u,g,p,y[c+1],12,3905402710),p=o(p,_,u,g,y[c+2],17,606105819),g=o(g,p,_,u,y[c+3],22,3250441966),u=o(u,g,p,_,y[c+4],7,4118548399),_=o(_,u,g,p,y[c+5],12,1200080426),p=o(p,_,u,g,y[c+6],17,2821735955),g=o(g,p,_,u,y[c+7],22,4249261313),u=o(u,g,p,_,y[c+8],7,1770035416),_=o(_,u,g,p,y[c+9],12,2336552879),p=o(p,_,u,g,y[c+10],17,4294925233),g=o(g,p,_,u,y[c+11],22,2304563134),u=o(u,g,p,_,y[c+12],7,1804603682),_=o(_,u,g,p,y[c+13],12,4254626195),p=o(p,_,u,g,y[c+14],17,2792965006),u=i(u,g=o(g,p,_,u,y[c+15],22,1236535329),p,_,y[c+1],5,4129170786),_=i(_,u,g,p,y[c+6],9,3225465664),p=i(p,_,u,g,y[c+11],14,643717713),g=i(g,p,_,u,y[c+0],20,3921069994),u=i(u,g,p,_,y[c+5],5,3593408605),_=i(_,u,g,p,y[c+10],9,38016083),p=i(p,_,u,g,y[c+15],14,3634488961),g=i(g,p,_,u,y[c+4],20,3889429448),u=i(u,g,p,_,y[c+9],5,568446438),_=i(_,u,g,p,y[c+14],9,3275163606),p=i(p,_,u,g,y[c+3],14,4107603335),g=i(g,p,_,u,y[c+8],20,1163531501),u=i(u,g,p,_,y[c+13],5,2850285829),_=i(_,u,g,p,y[c+2],9,4243563512),p=i(p,_,u,g,y[c+7],14,1735328473),u=r(u,g=i(g,p,_,u,y[c+12],20,2368359562),p,_,y[c+5],4,4294588738),_=r(_,u,g,p,y[c+8],11,2272392833),p=r(p,_,u,g,y[c+11],16,1839030562),g=r(g,p,_,u,y[c+14],23,4259657740),u=r(u,g,p,_,y[c+1],4,2763975236),_=r(_,u,g,p,y[c+4],11,1272893353),p=r(p,_,u,g,y[c+7],16,4139469664),g=r(g,p,_,u,y[c+10],23,3200236656),u=r(u,g,p,_,y[c+13],4,681279174),_=r(_,u,g,p,y[c+0],11,3936430074),p=r(p,_,u,g,y[c+3],16,3572445317),g=r(g,p,_,u,y[c+6],23,76029189),u=r(u,g,p,_,y[c+9],4,3654602809),_=r(_,u,g,p,y[c+12],11,3873151461),p=r(p,_,u,g,y[c+15],16,530742520),u=s(u,g=r(g,p,_,u,y[c+2],23,3299628645),p,_,y[c+0],6,4096336452),_=s(_,u,g,p,y[c+7],10,1126891415),p=s(p,_,u,g,y[c+14],15,2878612391),g=s(g,p,_,u,y[c+5],21,4237533241),u=s(u,g,p,_,y[c+12],6,1700485571),_=s(_,u,g,p,y[c+3],10,2399980690),p=s(p,_,u,g,y[c+10],15,4293915773),g=s(g,p,_,u,y[c+1],21,2240044497),u=s(u,g,p,_,y[c+8],6,1873313359),_=s(_,u,g,p,y[c+15],10,4264355552),p=s(p,_,u,g,y[c+6],15,2734768916),g=s(g,p,_,u,y[c+13],21,1309151649),u=s(u,g,p,_,y[c+4],6,4149444226),_=s(_,u,g,p,y[c+11],10,3174756917),p=s(p,_,u,g,y[c+2],15,718787259),g=s(g,p,_,u,y[c+9],21,3951481745),u=n(u,l),g=n(g,h),p=n(p,d),_=n(_,f);return(a(u)+a(g)+a(p)+a(_)).toLowerCase()}},21:function(e,t,n){e.exports=function(){var e,t,o,i,r,s,a,c,l,h,d,f,u,g,p,_,y,m,v,S,k,w,I,x,C,b,A,B,L,D,E,H,N,T,O,P,R,U,z,F,X,K,M,Y,q,W,j,G,J,V,Z,Q,$,ee,te,ne,oe,ie,re,se,ae,ce,le,he,de,fe,ue,ge,pe,_e,ye,me,ve,Se,ke,we,Ie,xe=xe||function(e){var t;if("undefined"!=typeof window&&window.crypto&&(t=window.crypto),!t&&"undefined"!=typeof window&&window.msCrypto&&(t=window.msCrypto),!t&&void 0!==n.g&&n.g.crypto&&(t=n.g.crypto),!t)try{t=n(Object(function(){var e=new Error("Cannot find module 'crypto'");throw e.code="MODULE_NOT_FOUND",e}()))}catch(t){}function o(){if(t){if("function"==typeof t.getRandomValues)try{return t.getRandomValues(new Uint32Array(1))[0]}catch(e){}if("function"==typeof t.randomBytes)try{return t.randomBytes(4).readInt32LE()}catch(e){}}throw new Error("Native crypto module could not be used to get secure random number.")}var i=Object.create||function(e){var t;return r.prototype=e,t=new r,r.prototype=null,t};function r(){}var s={},a=s.lib={},c=a.Base={extend:function(e){var t=i(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),(t.init.prototype=t).$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},l=a.WordArray=c.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=null!=t?t:4*e.length},toString:function(e){return(e||d).stringify(this)},concat:function(e){var t=this.words,n=e.words,o=this.sigBytes,i=e.sigBytes;if(this.clamp(),o%4)for(var r=0;r<i;r++){var s=n[r>>>2]>>>24-r%4*8&255;t[o+r>>>2]|=s<<24-(o+r)%4*8}else for(r=0;r<i;r+=4)t[o+r>>>2]=n[r>>>2];return this.sigBytes+=i,this},clamp:function(){var t=this.words,n=this.sigBytes;t[n>>>2]&=4294967295<<32-n%4*8,t.length=e.ceil(n/4)},clone:function(){var e=c.clone.call(this);return e.words=this.words.slice(0),e},random:function(e){for(var t=[],n=0;n<e;n+=4)t.push(o());return new l.init(t,e)}}),h=s.enc={},d=h.Hex={stringify:function(e){for(var t=e.words,n=e.sigBytes,o=[],i=0;i<n;i++){var r=t[i>>>2]>>>24-i%4*8&255;o.push((r>>>4).toString(16)),o.push((15&r).toString(16))}return o.join("")},parse:function(e){for(var t=e.length,n=[],o=0;o<t;o+=2)n[o>>>3]|=parseInt(e.substr(o,2),16)<<24-o%8*4;return new l.init(n,t/2)}},f=h.Latin1={stringify:function(e){for(var t=e.words,n=e.sigBytes,o=[],i=0;i<n;i++){var r=t[i>>>2]>>>24-i%4*8&255;o.push(String.fromCharCode(r))}return o.join("")},parse:function(e){for(var t=e.length,n=[],o=0;o<t;o++)n[o>>>2]|=(255&e.charCodeAt(o))<<24-o%4*8;return new l.init(n,t)}},u=h.Utf8={stringify:function(e){try{return decodeURIComponent(escape(f.stringify(e)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(e){return f.parse(unescape(encodeURIComponent(e)))}},g=a.BufferedBlockAlgorithm=c.extend({reset:function(){this._data=new l.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=u.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var n,o=this._data,i=o.words,r=o.sigBytes,s=this.blockSize,a=r/(4*s),c=(a=t?e.ceil(a):e.max((0|a)-this._minBufferSize,0))*s,h=e.min(4*c,r);if(c){for(var d=0;d<c;d+=s)this._doProcessBlock(i,d);n=i.splice(0,c),o.sigBytes-=h}return new l.init(n,h)},clone:function(){var e=c.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0}),p=(a.Hasher=g.extend({cfg:c.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){g.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,n){return new e.init(n).finalize(t)}},_createHmacHelper:function(e){return function(t,n){return new p.HMAC.init(e,n).finalize(t)}}}),s.algo={});return s}(Math);function Ce(e,t,n){return e^t^n}function be(e,t,n){return e&t|~e&n}function Ae(e,t,n){return(e|~t)^n}function Be(e,t,n){return e&n|t&~n}function Le(e,t,n){return e^(t|~n)}function De(e,t){return e<<t|e>>>32-t}function Ee(e,t,n,o){var i,r=this._iv;r?(i=r.slice(0),this._iv=void 0):i=this._prevBlock,o.encryptBlock(i,0);for(var s=0;s<n;s++)e[t+s]^=i[s]}function He(e){if(255==(e>>24&255)){var t=e>>16&255,n=e>>8&255,o=255&e;255===t?(t=0,255===n?(n=0,255===o?o=0:++o):++n):++t,e=0,e+=t<<16,e+=n<<8,e+=o}else e+=1<<24;return e}function Ne(){for(var e=this._X,t=this._C,n=0;n<8;n++)fe[n]=t[n];for(t[0]=t[0]+1295307597+this._b|0,t[1]=t[1]+3545052371+(t[0]>>>0<fe[0]>>>0?1:0)|0,t[2]=t[2]+886263092+(t[1]>>>0<fe[1]>>>0?1:0)|0,t[3]=t[3]+1295307597+(t[2]>>>0<fe[2]>>>0?1:0)|0,t[4]=t[4]+3545052371+(t[3]>>>0<fe[3]>>>0?1:0)|0,t[5]=t[5]+886263092+(t[4]>>>0<fe[4]>>>0?1:0)|0,t[6]=t[6]+1295307597+(t[5]>>>0<fe[5]>>>0?1:0)|0,t[7]=t[7]+3545052371+(t[6]>>>0<fe[6]>>>0?1:0)|0,this._b=t[7]>>>0<fe[7]>>>0?1:0,n=0;n<8;n++){var o=e[n]+t[n],i=65535&o,r=o>>>16,s=((i*i>>>17)+i*r>>>15)+r*r,a=((4294901760&o)*o|0)+((65535&o)*o|0);ue[n]=s^a}e[0]=ue[0]+(ue[7]<<16|ue[7]>>>16)+(ue[6]<<16|ue[6]>>>16)|0,e[1]=ue[1]+(ue[0]<<8|ue[0]>>>24)+ue[7]|0,e[2]=ue[2]+(ue[1]<<16|ue[1]>>>16)+(ue[0]<<16|ue[0]>>>16)|0,e[3]=ue[3]+(ue[2]<<8|ue[2]>>>24)+ue[1]|0,e[4]=ue[4]+(ue[3]<<16|ue[3]>>>16)+(ue[2]<<16|ue[2]>>>16)|0,e[5]=ue[5]+(ue[4]<<8|ue[4]>>>24)+ue[3]|0,e[6]=ue[6]+(ue[5]<<16|ue[5]>>>16)+(ue[4]<<16|ue[4]>>>16)|0,e[7]=ue[7]+(ue[6]<<8|ue[6]>>>24)+ue[5]|0}function Te(){for(var e=this._X,t=this._C,n=0;n<8;n++)ke[n]=t[n];for(t[0]=t[0]+1295307597+this._b|0,t[1]=t[1]+3545052371+(t[0]>>>0<ke[0]>>>0?1:0)|0,t[2]=t[2]+886263092+(t[1]>>>0<ke[1]>>>0?1:0)|0,t[3]=t[3]+1295307597+(t[2]>>>0<ke[2]>>>0?1:0)|0,t[4]=t[4]+3545052371+(t[3]>>>0<ke[3]>>>0?1:0)|0,t[5]=t[5]+886263092+(t[4]>>>0<ke[4]>>>0?1:0)|0,t[6]=t[6]+1295307597+(t[5]>>>0<ke[5]>>>0?1:0)|0,t[7]=t[7]+3545052371+(t[6]>>>0<ke[6]>>>0?1:0)|0,this._b=t[7]>>>0<ke[7]>>>0?1:0,n=0;n<8;n++){var o=e[n]+t[n],i=65535&o,r=o>>>16,s=((i*i>>>17)+i*r>>>15)+r*r,a=((4294901760&o)*o|0)+((65535&o)*o|0);we[n]=s^a}e[0]=we[0]+(we[7]<<16|we[7]>>>16)+(we[6]<<16|we[6]>>>16)|0,e[1]=we[1]+(we[0]<<8|we[0]>>>24)+we[7]|0,e[2]=we[2]+(we[1]<<16|we[1]>>>16)+(we[0]<<16|we[0]>>>16)|0,e[3]=we[3]+(we[2]<<8|we[2]>>>24)+we[1]|0,e[4]=we[4]+(we[3]<<16|we[3]>>>16)+(we[2]<<16|we[2]>>>16)|0,e[5]=we[5]+(we[4]<<8|we[4]>>>24)+we[3]|0,e[6]=we[6]+(we[5]<<16|we[5]>>>16)+(we[4]<<16|we[4]>>>16)|0,e[7]=we[7]+(we[6]<<8|we[6]>>>24)+we[5]|0}return e=xe.lib.WordArray,xe.enc.Base64={stringify:function(e){var t=e.words,n=e.sigBytes,o=this._map;e.clamp();for(var i=[],r=0;r<n;r+=3)for(var s=(t[r>>>2]>>>24-r%4*8&255)<<16|(t[r+1>>>2]>>>24-(r+1)%4*8&255)<<8|t[r+2>>>2]>>>24-(r+2)%4*8&255,a=0;a<4&&r+.75*a<n;a++)i.push(o.charAt(s>>>6*(3-a)&63));var c=o.charAt(64);if(c)for(;i.length%4;)i.push(c);return i.join("")},parse:function(t){var n=t.length,o=this._map,i=this._reverseMap;if(!i){i=this._reverseMap=[];for(var r=0;r<o.length;r++)i[o.charCodeAt(r)]=r}var s=o.charAt(64);if(s){var a=t.indexOf(s);-1!==a&&(n=a)}return function(t,n,o){for(var i=[],r=0,s=0;s<n;s++)if(s%4){var a=o[t.charCodeAt(s-1)]<<s%4*2|o[t.charCodeAt(s)]>>>6-s%4*2;i[r>>>2]|=a<<24-r%4*8,r++}return e.create(i,r)}(t,n,i)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},function(e){var t=xe,n=t.lib,o=n.WordArray,i=n.Hasher,r=t.algo,s=[];!function(){for(var t=0;t<64;t++)s[t]=4294967296*e.abs(e.sin(t+1))|0}();var a=r.MD5=i.extend({_doReset:function(){this._hash=new o.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var n=0;n<16;n++){var o=t+n,i=e[o];e[o]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8)}var r=this._hash.words,a=e[t+0],f=e[t+1],u=e[t+2],g=e[t+3],p=e[t+4],_=e[t+5],y=e[t+6],m=e[t+7],v=e[t+8],S=e[t+9],k=e[t+10],w=e[t+11],I=e[t+12],x=e[t+13],C=e[t+14],b=e[t+15],A=r[0],B=r[1],L=r[2],D=r[3];A=c(A,B,L,D,a,7,s[0]),D=c(D,A,B,L,f,12,s[1]),L=c(L,D,A,B,u,17,s[2]),B=c(B,L,D,A,g,22,s[3]),A=c(A,B,L,D,p,7,s[4]),D=c(D,A,B,L,_,12,s[5]),L=c(L,D,A,B,y,17,s[6]),B=c(B,L,D,A,m,22,s[7]),A=c(A,B,L,D,v,7,s[8]),D=c(D,A,B,L,S,12,s[9]),L=c(L,D,A,B,k,17,s[10]),B=c(B,L,D,A,w,22,s[11]),A=c(A,B,L,D,I,7,s[12]),D=c(D,A,B,L,x,12,s[13]),L=c(L,D,A,B,C,17,s[14]),A=l(A,B=c(B,L,D,A,b,22,s[15]),L,D,f,5,s[16]),D=l(D,A,B,L,y,9,s[17]),L=l(L,D,A,B,w,14,s[18]),B=l(B,L,D,A,a,20,s[19]),A=l(A,B,L,D,_,5,s[20]),D=l(D,A,B,L,k,9,s[21]),L=l(L,D,A,B,b,14,s[22]),B=l(B,L,D,A,p,20,s[23]),A=l(A,B,L,D,S,5,s[24]),D=l(D,A,B,L,C,9,s[25]),L=l(L,D,A,B,g,14,s[26]),B=l(B,L,D,A,v,20,s[27]),A=l(A,B,L,D,x,5,s[28]),D=l(D,A,B,L,u,9,s[29]),L=l(L,D,A,B,m,14,s[30]),A=h(A,B=l(B,L,D,A,I,20,s[31]),L,D,_,4,s[32]),D=h(D,A,B,L,v,11,s[33]),L=h(L,D,A,B,w,16,s[34]),B=h(B,L,D,A,C,23,s[35]),A=h(A,B,L,D,f,4,s[36]),D=h(D,A,B,L,p,11,s[37]),L=h(L,D,A,B,m,16,s[38]),B=h(B,L,D,A,k,23,s[39]),A=h(A,B,L,D,x,4,s[40]),D=h(D,A,B,L,a,11,s[41]),L=h(L,D,A,B,g,16,s[42]),B=h(B,L,D,A,y,23,s[43]),A=h(A,B,L,D,S,4,s[44]),D=h(D,A,B,L,I,11,s[45]),L=h(L,D,A,B,b,16,s[46]),A=d(A,B=h(B,L,D,A,u,23,s[47]),L,D,a,6,s[48]),D=d(D,A,B,L,m,10,s[49]),L=d(L,D,A,B,C,15,s[50]),B=d(B,L,D,A,_,21,s[51]),A=d(A,B,L,D,I,6,s[52]),D=d(D,A,B,L,g,10,s[53]),L=d(L,D,A,B,k,15,s[54]),B=d(B,L,D,A,f,21,s[55]),A=d(A,B,L,D,v,6,s[56]),D=d(D,A,B,L,b,10,s[57]),L=d(L,D,A,B,y,15,s[58]),B=d(B,L,D,A,x,21,s[59]),A=d(A,B,L,D,p,6,s[60]),D=d(D,A,B,L,w,10,s[61]),L=d(L,D,A,B,u,15,s[62]),B=d(B,L,D,A,S,21,s[63]),r[0]=r[0]+A|0,r[1]=r[1]+B|0,r[2]=r[2]+L|0,r[3]=r[3]+D|0},_doFinalize:function(){var t=this._data,n=t.words,o=8*this._nDataBytes,i=8*t.sigBytes;n[i>>>5]|=128<<24-i%32;var r=e.floor(o/4294967296),s=o;n[15+(64+i>>>9<<4)]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8),n[14+(64+i>>>9<<4)]=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),t.sigBytes=4*(n.length+1),this._process();for(var a=this._hash,c=a.words,l=0;l<4;l++){var h=c[l];c[l]=16711935&(h<<8|h>>>24)|4278255360&(h<<24|h>>>8)}return a},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}});function c(e,t,n,o,i,r,s){var a=e+(t&n|~t&o)+i+s;return(a<<r|a>>>32-r)+t}function l(e,t,n,o,i,r,s){var a=e+(t&o|n&~o)+i+s;return(a<<r|a>>>32-r)+t}function h(e,t,n,o,i,r,s){var a=e+(t^n^o)+i+s;return(a<<r|a>>>32-r)+t}function d(e,t,n,o,i,r,s){var a=e+(n^(t|~o))+i+s;return(a<<r|a>>>32-r)+t}t.MD5=i._createHelper(a),t.HmacMD5=i._createHmacHelper(a)}(Math),o=(t=xe).lib,i=o.WordArray,r=o.Hasher,s=t.algo,a=[],c=s.SHA1=r.extend({_doReset:function(){this._hash=new i.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var n=this._hash.words,o=n[0],i=n[1],r=n[2],s=n[3],c=n[4],l=0;l<80;l++){if(l<16)a[l]=0|e[t+l];else{var h=a[l-3]^a[l-8]^a[l-14]^a[l-16];a[l]=h<<1|h>>>31}var d=(o<<5|o>>>27)+c+a[l];d+=l<20?1518500249+(i&r|~i&s):l<40?1859775393+(i^r^s):l<60?(i&r|i&s|r&s)-1894007588:(i^r^s)-899497514,c=s,s=r,r=i<<30|i>>>2,i=o,o=d}n[0]=n[0]+o|0,n[1]=n[1]+i|0,n[2]=n[2]+r|0,n[3]=n[3]+s|0,n[4]=n[4]+c|0},_doFinalize:function(){var e=this._data,t=e.words,n=8*this._nDataBytes,o=8*e.sigBytes;return t[o>>>5]|=128<<24-o%32,t[14+(64+o>>>9<<4)]=Math.floor(n/4294967296),t[15+(64+o>>>9<<4)]=n,e.sigBytes=4*t.length,this._process(),this._hash},clone:function(){var e=r.clone.call(this);return e._hash=this._hash.clone(),e}}),t.SHA1=r._createHelper(c),t.HmacSHA1=r._createHmacHelper(c),function(e){var t=xe,n=t.lib,o=n.WordArray,i=n.Hasher,r=t.algo,s=[],a=[];!function(){function t(t){for(var n=e.sqrt(t),o=2;o<=n;o++)if(!(t%o))return;return 1}function n(e){return 4294967296*(e-(0|e))|0}for(var o=2,i=0;i<64;)t(o)&&(i<8&&(s[i]=n(e.pow(o,.5))),a[i]=n(e.pow(o,1/3)),i++),o++}();var c=[],l=r.SHA256=i.extend({_doReset:function(){this._hash=new o.init(s.slice(0))},_doProcessBlock:function(e,t){for(var n=this._hash.words,o=n[0],i=n[1],r=n[2],s=n[3],l=n[4],h=n[5],d=n[6],f=n[7],u=0;u<64;u++){if(u<16)c[u]=0|e[t+u];else{var g=c[u-15],p=(g<<25|g>>>7)^(g<<14|g>>>18)^g>>>3,_=c[u-2],y=(_<<15|_>>>17)^(_<<13|_>>>19)^_>>>10;c[u]=p+c[u-7]+y+c[u-16]}var m=o&i^o&r^i&r,v=(o<<30|o>>>2)^(o<<19|o>>>13)^(o<<10|o>>>22),S=f+((l<<26|l>>>6)^(l<<21|l>>>11)^(l<<7|l>>>25))+(l&h^~l&d)+a[u]+c[u];f=d,d=h,h=l,l=s+S|0,s=r,r=i,i=o,o=S+(v+m)|0}n[0]=n[0]+o|0,n[1]=n[1]+i|0,n[2]=n[2]+r|0,n[3]=n[3]+s|0,n[4]=n[4]+l|0,n[5]=n[5]+h|0,n[6]=n[6]+d|0,n[7]=n[7]+f|0},_doFinalize:function(){var t=this._data,n=t.words,o=8*this._nDataBytes,i=8*t.sigBytes;return n[i>>>5]|=128<<24-i%32,n[14+(64+i>>>9<<4)]=e.floor(o/4294967296),n[15+(64+i>>>9<<4)]=o,t.sigBytes=4*n.length,this._process(),this._hash},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}});t.SHA256=i._createHelper(l),t.HmacSHA256=i._createHmacHelper(l)}(Math),function(){var e=xe.lib.WordArray,t=xe.enc;function n(e){return e<<8&4278255360|e>>>8&16711935}t.Utf16=t.Utf16BE={stringify:function(e){for(var t=e.words,n=e.sigBytes,o=[],i=0;i<n;i+=2){var r=t[i>>>2]>>>16-i%4*8&65535;o.push(String.fromCharCode(r))}return o.join("")},parse:function(t){for(var n=t.length,o=[],i=0;i<n;i++)o[i>>>1]|=t.charCodeAt(i)<<16-i%2*16;return e.create(o,2*n)}},t.Utf16LE={stringify:function(e){for(var t=e.words,o=e.sigBytes,i=[],r=0;r<o;r+=2){var s=n(t[r>>>2]>>>16-r%4*8&65535);i.push(String.fromCharCode(s))}return i.join("")},parse:function(t){for(var o=t.length,i=[],r=0;r<o;r++)i[r>>>1]|=n(t.charCodeAt(r)<<16-r%2*16);return e.create(i,2*o)}}}(),function(){if("function"==typeof ArrayBuffer){var e=xe.lib.WordArray,t=e.init;(e.init=function(e){if(e instanceof ArrayBuffer&&(e=new Uint8Array(e)),(e instanceof Int8Array||"undefined"!=typeof Uint8ClampedArray&&e instanceof Uint8ClampedArray||e instanceof Int16Array||e instanceof Uint16Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Float32Array||e instanceof Float64Array)&&(e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength)),e instanceof Uint8Array){for(var n=e.byteLength,o=[],i=0;i<n;i++)o[i>>>2]|=e[i]<<24-i%4*8;t.call(this,o,n)}else t.apply(this,arguments)}).prototype=e}}(),Math,h=(l=xe).lib,d=h.WordArray,f=h.Hasher,u=l.algo,g=d.create([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13]),p=d.create([5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11]),_=d.create([11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6]),y=d.create([8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]),m=d.create([0,1518500249,1859775393,2400959708,2840853838]),v=d.create([1352829926,1548603684,1836072691,2053994217,0]),S=u.RIPEMD160=f.extend({_doReset:function(){this._hash=d.create([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var n=0;n<16;n++){var o=t+n,i=e[o];e[o]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8)}var r,s,a,c,l,h,d,f,u,S,k,w=this._hash.words,I=m.words,x=v.words,C=g.words,b=p.words,A=_.words,B=y.words;for(h=r=w[0],d=s=w[1],f=a=w[2],u=c=w[3],S=l=w[4],n=0;n<80;n+=1)k=r+e[t+C[n]]|0,k+=n<16?Ce(s,a,c)+I[0]:n<32?be(s,a,c)+I[1]:n<48?Ae(s,a,c)+I[2]:n<64?Be(s,a,c)+I[3]:Le(s,a,c)+I[4],k=(k=De(k|=0,A[n]))+l|0,r=l,l=c,c=De(a,10),a=s,s=k,k=h+e[t+b[n]]|0,k+=n<16?Le(d,f,u)+x[0]:n<32?Be(d,f,u)+x[1]:n<48?Ae(d,f,u)+x[2]:n<64?be(d,f,u)+x[3]:Ce(d,f,u)+x[4],k=(k=De(k|=0,B[n]))+S|0,h=S,S=u,u=De(f,10),f=d,d=k;k=w[1]+a+u|0,w[1]=w[2]+c+S|0,w[2]=w[3]+l+h|0,w[3]=w[4]+r+d|0,w[4]=w[0]+s+f|0,w[0]=k},_doFinalize:function(){var e=this._data,t=e.words,n=8*this._nDataBytes,o=8*e.sigBytes;t[o>>>5]|=128<<24-o%32,t[14+(64+o>>>9<<4)]=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8),e.sigBytes=4*(t.length+1),this._process();for(var i=this._hash,r=i.words,s=0;s<5;s++){var a=r[s];r[s]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8)}return i},clone:function(){var e=f.clone.call(this);return e._hash=this._hash.clone(),e}}),l.RIPEMD160=f._createHelper(S),l.HmacRIPEMD160=f._createHmacHelper(S),k=xe.lib.Base,w=xe.enc.Utf8,xe.algo.HMAC=k.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=w.parse(t));var n=e.blockSize,o=4*n;t.sigBytes>o&&(t=e.finalize(t)),t.clamp();for(var i=this._oKey=t.clone(),r=this._iKey=t.clone(),s=i.words,a=r.words,c=0;c<n;c++)s[c]^=1549556828,a[c]^=909522486;i.sigBytes=r.sigBytes=o,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,n=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(n))}}),C=(x=(I=xe).lib).Base,b=x.WordArray,B=(A=I.algo).SHA1,L=A.HMAC,D=A.PBKDF2=C.extend({cfg:C.extend({keySize:4,hasher:B,iterations:1}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var n=this.cfg,o=L.create(n.hasher,e),i=b.create(),r=b.create([1]),s=i.words,a=r.words,c=n.keySize,l=n.iterations;s.length<c;){var h=o.update(t).finalize(r);o.reset();for(var d=h.words,f=d.length,u=h,g=1;g<l;g++){u=o.finalize(u),o.reset();for(var p=u.words,_=0;_<f;_++)d[_]^=p[_]}i.concat(h),a[0]++}return i.sigBytes=4*c,i}}),I.PBKDF2=function(e,t,n){return D.create(n).compute(e,t)},N=(H=(E=xe).lib).Base,T=H.WordArray,P=(O=E.algo).MD5,R=O.EvpKDF=N.extend({cfg:N.extend({keySize:4,hasher:P,iterations:1}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var n,o=this.cfg,i=o.hasher.create(),r=T.create(),s=r.words,a=o.keySize,c=o.iterations;s.length<a;){n&&i.update(n),n=i.update(e).finalize(t),i.reset();for(var l=1;l<c;l++)n=i.finalize(n),i.reset();r.concat(n)}return r.sigBytes=4*a,r}}),E.EvpKDF=function(e,t,n){return R.create(n).compute(e,t)},z=(U=xe).lib.WordArray,F=U.algo,X=F.SHA256,K=F.SHA224=X.extend({_doReset:function(){this._hash=new z.init([3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428])},_doFinalize:function(){var e=X._doFinalize.call(this);return e.sigBytes-=4,e}}),U.SHA224=X._createHelper(K),U.HmacSHA224=X._createHmacHelper(K),M=xe.lib,Y=M.Base,q=M.WordArray,(W=xe.x64={}).Word=Y.extend({init:function(e,t){this.high=e,this.low=t}}),W.WordArray=Y.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=null!=t?t:8*e.length},toX32:function(){for(var e=this.words,t=e.length,n=[],o=0;o<t;o++){var i=e[o];n.push(i.high),n.push(i.low)}return q.create(n,this.sigBytes)},clone:function(){for(var e=Y.clone.call(this),t=e.words=this.words.slice(0),n=t.length,o=0;o<n;o++)t[o]=t[o].clone();return e}}),function(e){var t=xe,n=t.lib,o=n.WordArray,i=n.Hasher,r=t.x64.Word,s=t.algo,a=[],c=[],l=[];!function(){for(var e=1,t=0,n=0;n<24;n++){a[e+5*t]=(n+1)*(n+2)/2%64;var o=(2*e+3*t)%5;e=t%5,t=o}for(e=0;e<5;e++)for(t=0;t<5;t++)c[e+5*t]=t+(2*e+3*t)%5*5;for(var i=1,s=0;s<24;s++){for(var h=0,d=0,f=0;f<7;f++){if(1&i){var u=(1<<f)-1;u<32?d^=1<<u:h^=1<<u-32}128&i?i=i<<1^113:i<<=1}l[s]=r.create(h,d)}}();var h=[];!function(){for(var e=0;e<25;e++)h[e]=r.create()}();var d=s.SHA3=i.extend({cfg:i.cfg.extend({outputLength:512}),_doReset:function(){for(var e=this._state=[],t=0;t<25;t++)e[t]=new r.init;this.blockSize=(1600-2*this.cfg.outputLength)/32},_doProcessBlock:function(e,t){for(var n=this._state,o=this.blockSize/2,i=0;i<o;i++){var r=e[t+2*i],s=e[t+2*i+1];r=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8),s=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),(B=n[i]).high^=s,B.low^=r}for(var d=0;d<24;d++){for(var f=0;f<5;f++){for(var u=0,g=0,p=0;p<5;p++)u^=(B=n[f+5*p]).high,g^=B.low;var _=h[f];_.high=u,_.low=g}for(f=0;f<5;f++){var y=h[(f+4)%5],m=h[(f+1)%5],v=m.high,S=m.low;for(u=y.high^(v<<1|S>>>31),g=y.low^(S<<1|v>>>31),p=0;p<5;p++)(B=n[f+5*p]).high^=u,B.low^=g}for(var k=1;k<25;k++){var w=(B=n[k]).high,I=B.low,x=a[k];g=x<32?(u=w<<x|I>>>32-x,I<<x|w>>>32-x):(u=I<<x-32|w>>>64-x,w<<x-32|I>>>64-x);var C=h[c[k]];C.high=u,C.low=g}var b=h[0],A=n[0];for(b.high=A.high,b.low=A.low,f=0;f<5;f++)for(p=0;p<5;p++){var B=n[k=f+5*p],L=h[k],D=h[(f+1)%5+5*p],E=h[(f+2)%5+5*p];B.high=L.high^~D.high&E.high,B.low=L.low^~D.low&E.low}B=n[0];var H=l[d];B.high^=H.high,B.low^=H.low}},_doFinalize:function(){var t=this._data,n=t.words,i=(this._nDataBytes,8*t.sigBytes),r=32*this.blockSize;n[i>>>5]|=1<<24-i%32,n[(e.ceil((1+i)/r)*r>>>5)-1]|=128,t.sigBytes=4*n.length,this._process();for(var s=this._state,a=this.cfg.outputLength/8,c=a/8,l=[],h=0;h<c;h++){var d=s[h],f=d.high,u=d.low;f=16711935&(f<<8|f>>>24)|4278255360&(f<<24|f>>>8),u=16711935&(u<<8|u>>>24)|4278255360&(u<<24|u>>>8),l.push(u),l.push(f)}return new o.init(l,a)},clone:function(){for(var e=i.clone.call(this),t=e._state=this._state.slice(0),n=0;n<25;n++)t[n]=t[n].clone();return e}});t.SHA3=i._createHelper(d),t.HmacSHA3=i._createHmacHelper(d)}(Math),function(){var e=xe,t=e.lib.Hasher,n=e.x64,o=n.Word,i=n.WordArray,r=e.algo;function s(){return o.create.apply(o,arguments)}var a=[s(1116352408,3609767458),s(1899447441,602891725),s(3049323471,3964484399),s(3921009573,2173295548),s(961987163,4081628472),s(1508970993,3053834265),s(2453635748,2937671579),s(2870763221,3664609560),s(3624381080,2734883394),s(310598401,1164996542),s(607225278,1323610764),s(1426881987,3590304994),s(1925078388,4068182383),s(2162078206,991336113),s(2614888103,633803317),s(3248222580,3479774868),s(3835390401,2666613458),s(4022224774,944711139),s(264347078,2341262773),s(604807628,2007800933),s(770255983,1495990901),s(1249150122,1856431235),s(1555081692,3175218132),s(1996064986,2198950837),s(2554220882,3999719339),s(2821834349,766784016),s(2952996808,2566594879),s(3210313671,3203337956),s(3336571891,1034457026),s(3584528711,2466948901),s(113926993,3758326383),s(338241895,168717936),s(666307205,1188179964),s(773529912,1546045734),s(1294757372,1522805485),s(1396182291,2643833823),s(1695183700,2343527390),s(1986661051,1014477480),s(2177026350,1206759142),s(2456956037,344077627),s(2730485921,1290863460),s(2820302411,3158454273),s(3259730800,3505952657),s(3345764771,106217008),s(3516065817,3606008344),s(3600352804,1432725776),s(4094571909,1467031594),s(275423344,851169720),s(430227734,3100823752),s(506948616,1363258195),s(659060556,3750685593),s(883997877,3785050280),s(958139571,3318307427),s(1322822218,3812723403),s(1537002063,2003034995),s(1747873779,3602036899),s(1955562222,1575990012),s(2024104815,1125592928),s(2227730452,2716904306),s(2361852424,442776044),s(2428436474,593698344),s(2756734187,3733110249),s(3204031479,2999351573),s(3329325298,3815920427),s(3391569614,3928383900),s(3515267271,566280711),s(3940187606,3454069534),s(4118630271,4000239992),s(116418474,1914138554),s(174292421,2731055270),s(289380356,3203993006),s(460393269,320620315),s(685471733,587496836),s(852142971,1086792851),s(1017036298,365543100),s(1126000580,2618297676),s(1288033470,3409855158),s(1501505948,4234509866),s(1607167915,987167468),s(1816402316,1246189591)],c=[];!function(){for(var e=0;e<80;e++)c[e]=s()}();var l=r.SHA512=t.extend({_doReset:function(){this._hash=new i.init([new o.init(1779033703,4089235720),new o.init(3144134277,2227873595),new o.init(1013904242,4271175723),new o.init(2773480762,1595750129),new o.init(1359893119,2917565137),new o.init(2600822924,725511199),new o.init(528734635,4215389547),new o.init(1541459225,327033209)])},_doProcessBlock:function(e,t){for(var n=this._hash.words,o=n[0],i=n[1],r=n[2],s=n[3],l=n[4],h=n[5],d=n[6],f=n[7],u=o.high,g=o.low,p=i.high,_=i.low,y=r.high,m=r.low,v=s.high,S=s.low,k=l.high,w=l.low,I=h.high,x=h.low,C=d.high,b=d.low,A=f.high,B=f.low,L=u,D=g,E=p,H=_,N=y,T=m,O=v,P=S,R=k,U=w,z=I,F=x,X=C,K=b,M=A,Y=B,q=0;q<80;q++){var W,j,G=c[q];if(q<16)j=G.high=0|e[t+2*q],W=G.low=0|e[t+2*q+1];else{var J=c[q-15],V=J.high,Z=J.low,Q=(V>>>1|Z<<31)^(V>>>8|Z<<24)^V>>>7,$=(Z>>>1|V<<31)^(Z>>>8|V<<24)^(Z>>>7|V<<25),ee=c[q-2],te=ee.high,ne=ee.low,oe=(te>>>19|ne<<13)^(te<<3|ne>>>29)^te>>>6,ie=(ne>>>19|te<<13)^(ne<<3|te>>>29)^(ne>>>6|te<<26),re=c[q-7],se=re.high,ae=re.low,ce=c[q-16],le=ce.high,he=ce.low;j=(j=(j=Q+se+((W=$+ae)>>>0<$>>>0?1:0))+oe+((W+=ie)>>>0<ie>>>0?1:0))+le+((W+=he)>>>0<he>>>0?1:0),G.high=j,G.low=W}var de,fe=R&z^~R&X,ue=U&F^~U&K,ge=L&E^L&N^E&N,pe=D&H^D&T^H&T,_e=(L>>>28|D<<4)^(L<<30|D>>>2)^(L<<25|D>>>7),ye=(D>>>28|L<<4)^(D<<30|L>>>2)^(D<<25|L>>>7),me=(R>>>14|U<<18)^(R>>>18|U<<14)^(R<<23|U>>>9),ve=(U>>>14|R<<18)^(U>>>18|R<<14)^(U<<23|R>>>9),Se=a[q],ke=Se.high,we=Se.low,Ie=M+me+((de=Y+ve)>>>0<Y>>>0?1:0),xe=ye+pe;M=X,Y=K,X=z,K=F,z=R,F=U,R=O+(Ie=(Ie=(Ie=Ie+fe+((de+=ue)>>>0<ue>>>0?1:0))+ke+((de+=we)>>>0<we>>>0?1:0))+j+((de+=W)>>>0<W>>>0?1:0))+((U=P+de|0)>>>0<P>>>0?1:0)|0,O=N,P=T,N=E,T=H,E=L,H=D,L=Ie+(_e+ge+(xe>>>0<ye>>>0?1:0))+((D=de+xe|0)>>>0<de>>>0?1:0)|0}g=o.low=g+D,o.high=u+L+(g>>>0<D>>>0?1:0),_=i.low=_+H,i.high=p+E+(_>>>0<H>>>0?1:0),m=r.low=m+T,r.high=y+N+(m>>>0<T>>>0?1:0),S=s.low=S+P,s.high=v+O+(S>>>0<P>>>0?1:0),w=l.low=w+U,l.high=k+R+(w>>>0<U>>>0?1:0),x=h.low=x+F,h.high=I+z+(x>>>0<F>>>0?1:0),b=d.low=b+K,d.high=C+X+(b>>>0<K>>>0?1:0),B=f.low=B+Y,f.high=A+M+(B>>>0<Y>>>0?1:0)},_doFinalize:function(){var e=this._data,t=e.words,n=8*this._nDataBytes,o=8*e.sigBytes;return t[o>>>5]|=128<<24-o%32,t[30+(128+o>>>10<<5)]=Math.floor(n/4294967296),t[31+(128+o>>>10<<5)]=n,e.sigBytes=4*t.length,this._process(),this._hash.toX32()},clone:function(){var e=t.clone.call(this);return e._hash=this._hash.clone(),e},blockSize:32});e.SHA512=t._createHelper(l),e.HmacSHA512=t._createHmacHelper(l)}(),G=(j=xe).x64,J=G.Word,V=G.WordArray,Z=j.algo,Q=Z.SHA512,$=Z.SHA384=Q.extend({_doReset:function(){this._hash=new V.init([new J.init(3418070365,3238371032),new J.init(1654270250,914150663),new J.init(2438529370,812702999),new J.init(355462360,4144912697),new J.init(1731405415,4290775857),new J.init(2394180231,1750603025),new J.init(3675008525,1694076839),new J.init(1203062813,3204075428)])},_doFinalize:function(){var e=Q._doFinalize.call(this);return e.sigBytes-=16,e}}),j.SHA384=Q._createHelper($),j.HmacSHA384=Q._createHmacHelper($),xe.lib.Cipher||function(){var e=xe,t=e.lib,n=t.Base,o=t.WordArray,i=t.BufferedBlockAlgorithm,r=e.enc,s=(r.Utf8,r.Base64),a=e.algo.EvpKDF,c=t.Cipher=i.extend({cfg:n.extend(),createEncryptor:function(e,t){return this.create(this._ENC_XFORM_MODE,e,t)},createDecryptor:function(e,t){return this.create(this._DEC_XFORM_MODE,e,t)},init:function(e,t,n){this.cfg=this.cfg.extend(n),this._xformMode=e,this._key=t,this.reset()},reset:function(){i.reset.call(this),this._doReset()},process:function(e){return this._append(e),this._process()},finalize:function(e){return e&&this._append(e),this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(e){return{encrypt:function(t,n,o){return l(n).encrypt(e,t,n,o)},decrypt:function(t,n,o){return l(n).decrypt(e,t,n,o)}}}});function l(e){return"string"==typeof e?S:m}t.StreamCipher=c.extend({_doFinalize:function(){return this._process(!0)},blockSize:1});var h,d=e.mode={},f=t.BlockCipherMode=n.extend({createEncryptor:function(e,t){return this.Encryptor.create(e,t)},createDecryptor:function(e,t){return this.Decryptor.create(e,t)},init:function(e,t){this._cipher=e,this._iv=t}}),u=d.CBC=((h=f.extend()).Encryptor=h.extend({processBlock:function(e,t){var n=this._cipher,o=n.blockSize;g.call(this,e,t,o),n.encryptBlock(e,t),this._prevBlock=e.slice(t,t+o)}}),h.Decryptor=h.extend({processBlock:function(e,t){var n=this._cipher,o=n.blockSize,i=e.slice(t,t+o);n.decryptBlock(e,t),g.call(this,e,t,o),this._prevBlock=i}}),h);function g(e,t,n){var o,i=this._iv;i?(o=i,this._iv=void 0):o=this._prevBlock;for(var r=0;r<n;r++)e[t+r]^=o[r]}var p=(e.pad={}).Pkcs7={pad:function(e,t){for(var n=4*t,i=n-e.sigBytes%n,r=i<<24|i<<16|i<<8|i,s=[],a=0;a<i;a+=4)s.push(r);var c=o.create(s,i);e.concat(c)},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},_=(t.BlockCipher=c.extend({cfg:c.cfg.extend({mode:u,padding:p}),reset:function(){var e;c.reset.call(this);var t=this.cfg,n=t.iv,o=t.mode;this._xformMode==this._ENC_XFORM_MODE?e=o.createEncryptor:(e=o.createDecryptor,this._minBufferSize=1),this._mode&&this._mode.__creator==e?this._mode.init(this,n&&n.words):(this._mode=e.call(o,this,n&&n.words),this._mode.__creator=e)},_doProcessBlock:function(e,t){this._mode.processBlock(e,t)},_doFinalize:function(){var e,t=this.cfg.padding;return this._xformMode==this._ENC_XFORM_MODE?(t.pad(this._data,this.blockSize),e=this._process(!0)):(e=this._process(!0),t.unpad(e)),e},blockSize:4}),t.CipherParams=n.extend({init:function(e){this.mixIn(e)},toString:function(e){return(e||this.formatter).stringify(this)}})),y=(e.format={}).OpenSSL={stringify:function(e){var t=e.ciphertext,n=e.salt;return(n?o.create([1398893684,1701076831]).concat(n).concat(t):t).toString(s)},parse:function(e){var t,n=s.parse(e),i=n.words;return 1398893684==i[0]&&1701076831==i[1]&&(t=o.create(i.slice(2,4)),i.splice(0,4),n.sigBytes-=16),_.create({ciphertext:n,salt:t})}},m=t.SerializableCipher=n.extend({cfg:n.extend({format:y}),encrypt:function(e,t,n,o){o=this.cfg.extend(o);var i=e.createEncryptor(n,o),r=i.finalize(t),s=i.cfg;return _.create({ciphertext:r,key:n,iv:s.iv,algorithm:e,mode:s.mode,padding:s.padding,blockSize:e.blockSize,formatter:o.format})},decrypt:function(e,t,n,o){return o=this.cfg.extend(o),t=this._parse(t,o.format),e.createDecryptor(n,o).finalize(t.ciphertext)},_parse:function(e,t){return"string"==typeof e?t.parse(e,this):e}}),v=(e.kdf={}).OpenSSL={execute:function(e,t,n,i){i=i||o.random(8);var r=a.create({keySize:t+n}).compute(e,i),s=o.create(r.words.slice(t),4*n);return r.sigBytes=4*t,_.create({key:r,iv:s,salt:i})}},S=t.PasswordBasedCipher=m.extend({cfg:m.cfg.extend({kdf:v}),encrypt:function(e,t,n,o){var i=(o=this.cfg.extend(o)).kdf.execute(n,e.keySize,e.ivSize);o.iv=i.iv;var r=m.encrypt.call(this,e,t,i.key,o);return r.mixIn(i),r},decrypt:function(e,t,n,o){o=this.cfg.extend(o),t=this._parse(t,o.format);var i=o.kdf.execute(n,e.keySize,e.ivSize,t.salt);return o.iv=i.iv,m.decrypt.call(this,e,t,i.key,o)}})}(),xe.mode.CFB=((ee=xe.lib.BlockCipherMode.extend()).Encryptor=ee.extend({processBlock:function(e,t){var n=this._cipher,o=n.blockSize;Ee.call(this,e,t,o,n),this._prevBlock=e.slice(t,t+o)}}),ee.Decryptor=ee.extend({processBlock:function(e,t){var n=this._cipher,o=n.blockSize,i=e.slice(t,t+o);Ee.call(this,e,t,o,n),this._prevBlock=i}}),ee),xe.mode.ECB=((te=xe.lib.BlockCipherMode.extend()).Encryptor=te.extend({processBlock:function(e,t){this._cipher.encryptBlock(e,t)}}),te.Decryptor=te.extend({processBlock:function(e,t){this._cipher.decryptBlock(e,t)}}),te),xe.pad.AnsiX923={pad:function(e,t){var n=e.sigBytes,o=4*t,i=o-n%o,r=n+i-1;e.clamp(),e.words[r>>>2]|=i<<24-r%4*8,e.sigBytes+=i},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},xe.pad.Iso10126={pad:function(e,t){var n=4*t,o=n-e.sigBytes%n;e.concat(xe.lib.WordArray.random(o-1)).concat(xe.lib.WordArray.create([o<<24],1))},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},xe.pad.Iso97971={pad:function(e,t){e.concat(xe.lib.WordArray.create([2147483648],1)),xe.pad.ZeroPadding.pad(e,t)},unpad:function(e){xe.pad.ZeroPadding.unpad(e),e.sigBytes--}},xe.mode.OFB=(oe=(ne=xe.lib.BlockCipherMode.extend()).Encryptor=ne.extend({processBlock:function(e,t){var n=this._cipher,o=n.blockSize,i=this._iv,r=this._keystream;i&&(r=this._keystream=i.slice(0),this._iv=void 0),n.encryptBlock(r,0);for(var s=0;s<o;s++)e[t+s]^=r[s]}}),ne.Decryptor=oe,ne),xe.pad.NoPadding={pad:function(){},unpad:function(){}},ie=xe.lib.CipherParams,re=xe.enc.Hex,xe.format.Hex={stringify:function(e){return e.ciphertext.toString(re)},parse:function(e){var t=re.parse(e);return ie.create({ciphertext:t})}},function(){var e=xe,t=e.lib.BlockCipher,n=e.algo,o=[],i=[],r=[],s=[],a=[],c=[],l=[],h=[],d=[],f=[];!function(){for(var e=[],t=0;t<256;t++)e[t]=t<128?t<<1:t<<1^283;var n=0,u=0;for(t=0;t<256;t++){var g=u^u<<1^u<<2^u<<3^u<<4;g=g>>>8^255&g^99,o[n]=g;var p=e[i[g]=n],_=e[p],y=e[_],m=257*e[g]^16843008*g;r[n]=m<<24|m>>>8,s[n]=m<<16|m>>>16,a[n]=m<<8|m>>>24,c[n]=m,m=16843009*y^65537*_^257*p^16843008*n,l[g]=m<<24|m>>>8,h[g]=m<<16|m>>>16,d[g]=m<<8|m>>>24,f[g]=m,n?(n=p^e[e[e[y^p]]],u^=e[e[u]]):n=u=1}}();var u=[0,1,2,4,8,16,32,64,128,27,54],g=n.AES=t.extend({_doReset:function(){if(!this._nRounds||this._keyPriorReset!==this._key){for(var e=this._keyPriorReset=this._key,t=e.words,n=e.sigBytes/4,i=4*(1+(this._nRounds=6+n)),r=this._keySchedule=[],s=0;s<i;s++)s<n?r[s]=t[s]:(g=r[s-1],s%n?6<n&&s%n==4&&(g=o[g>>>24]<<24|o[g>>>16&255]<<16|o[g>>>8&255]<<8|o[255&g]):(g=o[(g=g<<8|g>>>24)>>>24]<<24|o[g>>>16&255]<<16|o[g>>>8&255]<<8|o[255&g],g^=u[s/n|0]<<24),r[s]=r[s-n]^g);for(var a=this._invKeySchedule=[],c=0;c<i;c++){if(s=i-c,c%4)var g=r[s];else g=r[s-4];a[c]=c<4||s<=4?g:l[o[g>>>24]]^h[o[g>>>16&255]]^d[o[g>>>8&255]]^f[o[255&g]]}}},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._keySchedule,r,s,a,c,o)},decryptBlock:function(e,t){var n=e[t+1];e[t+1]=e[t+3],e[t+3]=n,this._doCryptBlock(e,t,this._invKeySchedule,l,h,d,f,i),n=e[t+1],e[t+1]=e[t+3],e[t+3]=n},_doCryptBlock:function(e,t,n,o,i,r,s,a){for(var c=this._nRounds,l=e[t]^n[0],h=e[t+1]^n[1],d=e[t+2]^n[2],f=e[t+3]^n[3],u=4,g=1;g<c;g++){var p=o[l>>>24]^i[h>>>16&255]^r[d>>>8&255]^s[255&f]^n[u++],_=o[h>>>24]^i[d>>>16&255]^r[f>>>8&255]^s[255&l]^n[u++],y=o[d>>>24]^i[f>>>16&255]^r[l>>>8&255]^s[255&h]^n[u++],m=o[f>>>24]^i[l>>>16&255]^r[h>>>8&255]^s[255&d]^n[u++];l=p,h=_,d=y,f=m}p=(a[l>>>24]<<24|a[h>>>16&255]<<16|a[d>>>8&255]<<8|a[255&f])^n[u++],_=(a[h>>>24]<<24|a[d>>>16&255]<<16|a[f>>>8&255]<<8|a[255&l])^n[u++],y=(a[d>>>24]<<24|a[f>>>16&255]<<16|a[l>>>8&255]<<8|a[255&h])^n[u++],m=(a[f>>>24]<<24|a[l>>>16&255]<<16|a[h>>>8&255]<<8|a[255&d])^n[u++],e[t]=p,e[t+1]=_,e[t+2]=y,e[t+3]=m},keySize:8});e.AES=t._createHelper(g)}(),function(){var e=xe,t=e.lib,n=t.WordArray,o=t.BlockCipher,i=e.algo,r=[57,49,41,33,25,17,9,1,58,50,42,34,26,18,10,2,59,51,43,35,27,19,11,3,60,52,44,36,63,55,47,39,31,23,15,7,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,28,20,12,4],s=[14,17,11,24,1,5,3,28,15,6,21,10,23,19,12,4,26,8,16,7,27,20,13,2,41,52,31,37,47,55,30,40,51,45,33,48,44,49,39,56,34,53,46,42,50,36,29,32],a=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28],c=[{0:8421888,268435456:32768,536870912:8421378,805306368:2,1073741824:512,1342177280:8421890,1610612736:8389122,1879048192:8388608,2147483648:514,2415919104:8389120,2684354560:33280,2952790016:8421376,3221225472:32770,3489660928:8388610,3758096384:0,4026531840:33282,134217728:0,402653184:8421890,671088640:33282,939524096:32768,1207959552:8421888,1476395008:512,1744830464:8421378,2013265920:2,2281701376:8389120,2550136832:33280,2818572288:8421376,3087007744:8389122,3355443200:8388610,3623878656:32770,3892314112:514,4160749568:8388608,1:32768,268435457:2,536870913:8421888,805306369:8388608,1073741825:8421378,1342177281:33280,1610612737:512,1879048193:8389122,2147483649:8421890,2415919105:8421376,2684354561:8388610,2952790017:33282,3221225473:514,3489660929:8389120,3758096385:32770,4026531841:0,134217729:8421890,402653185:8421376,671088641:8388608,939524097:512,1207959553:32768,1476395009:8388610,1744830465:2,2013265921:33282,2281701377:32770,2550136833:8389122,2818572289:514,3087007745:8421888,3355443201:8389120,3623878657:0,3892314113:33280,4160749569:8421378},{0:1074282512,16777216:16384,33554432:524288,50331648:1074266128,67108864:1073741840,83886080:1074282496,100663296:1073758208,117440512:16,134217728:540672,150994944:1073758224,167772160:1073741824,184549376:540688,201326592:524304,218103808:0,234881024:16400,251658240:1074266112,8388608:1073758208,25165824:540688,41943040:16,58720256:1073758224,75497472:1074282512,92274688:1073741824,109051904:524288,125829120:1074266128,142606336:524304,159383552:0,176160768:16384,192937984:1074266112,209715200:1073741840,226492416:540672,243269632:1074282496,260046848:16400,268435456:0,285212672:1074266128,301989888:1073758224,318767104:1074282496,335544320:1074266112,352321536:16,369098752:540688,385875968:16384,402653184:16400,419430400:524288,436207616:524304,452984832:1073741840,469762048:540672,486539264:1073758208,503316480:1073741824,520093696:1074282512,276824064:540688,293601280:524288,310378496:1074266112,327155712:16384,343932928:1073758208,360710144:1074282512,377487360:16,394264576:1073741824,411041792:1074282496,427819008:1073741840,444596224:1073758224,461373440:524304,478150656:0,494927872:16400,511705088:1074266128,528482304:540672},{0:260,1048576:0,2097152:67109120,3145728:65796,4194304:65540,5242880:67108868,6291456:67174660,7340032:67174400,8388608:67108864,9437184:67174656,10485760:65792,11534336:67174404,12582912:67109124,13631488:65536,14680064:4,15728640:256,524288:67174656,1572864:67174404,2621440:0,3670016:67109120,4718592:67108868,5767168:65536,6815744:65540,7864320:260,8912896:4,9961472:256,11010048:67174400,12058624:65796,13107200:65792,14155776:67109124,15204352:67174660,16252928:67108864,16777216:67174656,17825792:65540,18874368:65536,19922944:67109120,20971520:256,22020096:67174660,23068672:67108868,24117248:0,25165824:67109124,26214400:67108864,27262976:4,28311552:65792,29360128:67174400,30408704:260,31457280:65796,32505856:67174404,17301504:67108864,18350080:260,19398656:67174656,20447232:0,21495808:65540,22544384:67109120,23592960:256,24641536:67174404,25690112:65536,26738688:67174660,27787264:65796,28835840:67108868,29884416:67109124,30932992:67174400,31981568:4,33030144:65792},{0:2151682048,65536:2147487808,131072:4198464,196608:2151677952,262144:0,327680:4198400,393216:2147483712,458752:4194368,524288:2147483648,589824:4194304,655360:64,720896:2147487744,786432:2151678016,851968:4160,917504:4096,983040:2151682112,32768:2147487808,98304:64,163840:2151678016,229376:2147487744,294912:4198400,360448:2151682112,425984:0,491520:2151677952,557056:4096,622592:2151682048,688128:4194304,753664:4160,819200:2147483648,884736:4194368,950272:4198464,1015808:2147483712,1048576:4194368,1114112:4198400,1179648:2147483712,1245184:0,1310720:4160,1376256:2151678016,1441792:2151682048,1507328:2147487808,1572864:2151682112,1638400:2147483648,1703936:2151677952,1769472:4198464,1835008:2147487744,1900544:4194304,1966080:64,2031616:4096,1081344:2151677952,1146880:2151682112,1212416:0,1277952:4198400,1343488:4194368,1409024:2147483648,1474560:2147487808,1540096:64,1605632:2147483712,1671168:4096,1736704:2147487744,1802240:2151678016,1867776:4160,1933312:2151682048,1998848:4194304,2064384:4198464},{0:128,4096:17039360,8192:262144,12288:536870912,16384:537133184,20480:16777344,24576:553648256,28672:262272,32768:16777216,36864:537133056,40960:536871040,45056:553910400,49152:553910272,53248:0,57344:17039488,61440:553648128,2048:17039488,6144:553648256,10240:128,14336:17039360,18432:262144,22528:537133184,26624:553910272,30720:536870912,34816:537133056,38912:0,43008:553910400,47104:16777344,51200:536871040,55296:553648128,59392:16777216,63488:262272,65536:262144,69632:128,73728:536870912,77824:553648256,81920:16777344,86016:553910272,90112:537133184,94208:16777216,98304:553910400,102400:553648128,106496:17039360,110592:537133056,114688:262272,118784:536871040,122880:0,126976:17039488,67584:553648256,71680:16777216,75776:17039360,79872:537133184,83968:536870912,88064:17039488,92160:128,96256:553910272,100352:262272,104448:553910400,108544:0,112640:553648128,116736:16777344,120832:262144,124928:537133056,129024:536871040},{0:268435464,256:8192,512:270532608,768:270540808,1024:268443648,1280:2097152,1536:2097160,1792:268435456,2048:0,2304:268443656,2560:2105344,2816:8,3072:270532616,3328:2105352,3584:8200,3840:270540800,128:270532608,384:270540808,640:8,896:2097152,1152:2105352,1408:268435464,1664:268443648,1920:8200,2176:2097160,2432:8192,2688:268443656,2944:270532616,3200:0,3456:270540800,3712:2105344,3968:268435456,4096:268443648,4352:270532616,4608:270540808,4864:8200,5120:2097152,5376:268435456,5632:268435464,5888:2105344,6144:2105352,6400:0,6656:8,6912:270532608,7168:8192,7424:268443656,7680:270540800,7936:2097160,4224:8,4480:2105344,4736:2097152,4992:268435464,5248:268443648,5504:8200,5760:270540808,6016:270532608,6272:270540800,6528:270532616,6784:8192,7040:2105352,7296:2097160,7552:0,7808:268435456,8064:268443656},{0:1048576,16:33555457,32:1024,48:1049601,64:34604033,80:0,96:1,112:34603009,128:33555456,144:1048577,160:33554433,176:34604032,192:34603008,208:1025,224:1049600,240:33554432,8:34603009,24:0,40:33555457,56:34604032,72:1048576,88:33554433,104:33554432,120:1025,136:1049601,152:33555456,168:34603008,184:1048577,200:1024,216:34604033,232:1,248:1049600,256:33554432,272:1048576,288:33555457,304:34603009,320:1048577,336:33555456,352:34604032,368:1049601,384:1025,400:34604033,416:1049600,432:1,448:0,464:34603008,480:33554433,496:1024,264:1049600,280:33555457,296:34603009,312:1,328:33554432,344:1048576,360:1025,376:34604032,392:33554433,408:34603008,424:0,440:34604033,456:1049601,472:1024,488:33555456,504:1048577},{0:134219808,1:131072,2:134217728,3:32,4:131104,5:134350880,6:134350848,7:2048,8:134348800,9:134219776,10:133120,11:134348832,12:2080,13:0,14:134217760,15:133152,2147483648:2048,2147483649:134350880,2147483650:134219808,2147483651:134217728,2147483652:134348800,2147483653:133120,2147483654:133152,2147483655:32,2147483656:134217760,2147483657:2080,2147483658:131104,2147483659:134350848,2147483660:0,2147483661:134348832,2147483662:134219776,2147483663:131072,16:133152,17:134350848,18:32,19:2048,20:134219776,21:134217760,22:134348832,23:131072,24:0,25:131104,26:134348800,27:134219808,28:134350880,29:133120,30:2080,31:134217728,2147483664:131072,2147483665:2048,2147483666:134348832,2147483667:133152,2147483668:32,2147483669:134348800,2147483670:134217728,2147483671:134219808,2147483672:134350880,2147483673:134217760,2147483674:134219776,2147483675:0,2147483676:133120,2147483677:2080,2147483678:131104,2147483679:134350848}],l=[4160749569,528482304,33030144,2064384,129024,8064,504,2147483679],h=i.DES=o.extend({_doReset:function(){for(var e=this._key.words,t=[],n=0;n<56;n++){var o=r[n]-1;t[n]=e[o>>>5]>>>31-o%32&1}for(var i=this._subKeys=[],c=0;c<16;c++){var l=i[c]=[],h=a[c];for(n=0;n<24;n++)l[n/6|0]|=t[(s[n]-1+h)%28]<<31-n%6,l[4+(n/6|0)]|=t[28+(s[n+24]-1+h)%28]<<31-n%6;for(l[0]=l[0]<<1|l[0]>>>31,n=1;n<7;n++)l[n]=l[n]>>>4*(n-1)+3;l[7]=l[7]<<5|l[7]>>>27}var d=this._invSubKeys=[];for(n=0;n<16;n++)d[n]=i[15-n]},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._subKeys)},decryptBlock:function(e,t){this._doCryptBlock(e,t,this._invSubKeys)},_doCryptBlock:function(e,t,n){this._lBlock=e[t],this._rBlock=e[t+1],d.call(this,4,252645135),d.call(this,16,65535),f.call(this,2,858993459),f.call(this,8,16711935),d.call(this,1,1431655765);for(var o=0;o<16;o++){for(var i=n[o],r=this._lBlock,s=this._rBlock,a=0,h=0;h<8;h++)a|=c[h][((s^i[h])&l[h])>>>0];this._lBlock=s,this._rBlock=r^a}var u=this._lBlock;this._lBlock=this._rBlock,this._rBlock=u,d.call(this,1,1431655765),f.call(this,8,16711935),f.call(this,2,858993459),d.call(this,16,65535),d.call(this,4,252645135),e[t]=this._lBlock,e[t+1]=this._rBlock},keySize:2,ivSize:2,blockSize:2});function d(e,t){var n=(this._lBlock>>>e^this._rBlock)&t;this._rBlock^=n,this._lBlock^=n<<e}function f(e,t){var n=(this._rBlock>>>e^this._lBlock)&t;this._lBlock^=n,this._rBlock^=n<<e}e.DES=o._createHelper(h);var u=i.TripleDES=o.extend({_doReset:function(){var e=this._key.words;if(2!==e.length&&4!==e.length&&e.length<6)throw new Error("Invalid key length - 3DES requires the key length to be 64, 128, 192 or >192.");var t=e.slice(0,2),o=e.length<4?e.slice(0,2):e.slice(2,4),i=e.length<6?e.slice(0,2):e.slice(4,6);this._des1=h.createEncryptor(n.create(t)),this._des2=h.createEncryptor(n.create(o)),this._des3=h.createEncryptor(n.create(i))},encryptBlock:function(e,t){this._des1.encryptBlock(e,t),this._des2.decryptBlock(e,t),this._des3.encryptBlock(e,t)},decryptBlock:function(e,t){this._des3.decryptBlock(e,t),this._des2.encryptBlock(e,t),this._des1.decryptBlock(e,t)},keySize:6,ivSize:2,blockSize:2});e.TripleDES=o._createHelper(u)}(),function(){var e=xe,t=e.lib.StreamCipher,n=e.algo,o=n.RC4=t.extend({_doReset:function(){for(var e=this._key,t=e.words,n=e.sigBytes,o=this._S=[],i=0;i<256;i++)o[i]=i;i=0;for(var r=0;i<256;i++){var s=i%n,a=t[s>>>2]>>>24-s%4*8&255;r=(r+o[i]+a)%256;var c=o[i];o[i]=o[r],o[r]=c}this._i=this._j=0},_doProcessBlock:function(e,t){e[t]^=i.call(this)},keySize:8,ivSize:0});function i(){for(var e=this._S,t=this._i,n=this._j,o=0,i=0;i<4;i++){n=(n+e[t=(t+1)%256])%256;var r=e[t];e[t]=e[n],e[n]=r,o|=e[(e[t]+e[n])%256]<<24-8*i}return this._i=t,this._j=n,o}e.RC4=t._createHelper(o);var r=n.RC4Drop=o.extend({cfg:o.cfg.extend({drop:192}),_doReset:function(){o._doReset.call(this);for(var e=this.cfg.drop;0<e;e--)i.call(this)}});e.RC4Drop=t._createHelper(r)}(),xe.mode.CTRGladman=(ae=(se=xe.lib.BlockCipherMode.extend()).Encryptor=se.extend({processBlock:function(e,t){var n,o=this._cipher,i=o.blockSize,r=this._iv,s=this._counter;r&&(s=this._counter=r.slice(0),this._iv=void 0),0===((n=s)[0]=He(n[0]))&&(n[1]=He(n[1]));var a=s.slice(0);o.encryptBlock(a,0);for(var c=0;c<i;c++)e[t+c]^=a[c]}}),se.Decryptor=ae,se),le=(ce=xe).lib.StreamCipher,he=ce.algo,de=[],fe=[],ue=[],ge=he.Rabbit=le.extend({_doReset:function(){for(var e=this._key.words,t=this.cfg.iv,n=0;n<4;n++)e[n]=16711935&(e[n]<<8|e[n]>>>24)|4278255360&(e[n]<<24|e[n]>>>8);var o=this._X=[e[0],e[3]<<16|e[2]>>>16,e[1],e[0]<<16|e[3]>>>16,e[2],e[1]<<16|e[0]>>>16,e[3],e[2]<<16|e[1]>>>16],i=this._C=[e[2]<<16|e[2]>>>16,4294901760&e[0]|65535&e[1],e[3]<<16|e[3]>>>16,4294901760&e[1]|65535&e[2],e[0]<<16|e[0]>>>16,4294901760&e[2]|65535&e[3],e[1]<<16|e[1]>>>16,4294901760&e[3]|65535&e[0]];for(n=this._b=0;n<4;n++)Ne.call(this);for(n=0;n<8;n++)i[n]^=o[n+4&7];if(t){var r=t.words,s=r[0],a=r[1],c=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),l=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),h=c>>>16|4294901760&l,d=l<<16|65535&c;for(i[0]^=c,i[1]^=h,i[2]^=l,i[3]^=d,i[4]^=c,i[5]^=h,i[6]^=l,i[7]^=d,n=0;n<4;n++)Ne.call(this)}},_doProcessBlock:function(e,t){var n=this._X;Ne.call(this),de[0]=n[0]^n[5]>>>16^n[3]<<16,de[1]=n[2]^n[7]>>>16^n[5]<<16,de[2]=n[4]^n[1]>>>16^n[7]<<16,de[3]=n[6]^n[3]>>>16^n[1]<<16;for(var o=0;o<4;o++)de[o]=16711935&(de[o]<<8|de[o]>>>24)|4278255360&(de[o]<<24|de[o]>>>8),e[t+o]^=de[o]},blockSize:4,ivSize:2}),ce.Rabbit=le._createHelper(ge),xe.mode.CTR=(_e=(pe=xe.lib.BlockCipherMode.extend()).Encryptor=pe.extend({processBlock:function(e,t){var n=this._cipher,o=n.blockSize,i=this._iv,r=this._counter;i&&(r=this._counter=i.slice(0),this._iv=void 0);var s=r.slice(0);n.encryptBlock(s,0),r[o-1]=r[o-1]+1|0;for(var a=0;a<o;a++)e[t+a]^=s[a]}}),pe.Decryptor=_e,pe),me=(ye=xe).lib.StreamCipher,ve=ye.algo,Se=[],ke=[],we=[],Ie=ve.RabbitLegacy=me.extend({_doReset:function(){for(var e=this._key.words,t=this.cfg.iv,n=this._X=[e[0],e[3]<<16|e[2]>>>16,e[1],e[0]<<16|e[3]>>>16,e[2],e[1]<<16|e[0]>>>16,e[3],e[2]<<16|e[1]>>>16],o=this._C=[e[2]<<16|e[2]>>>16,4294901760&e[0]|65535&e[1],e[3]<<16|e[3]>>>16,4294901760&e[1]|65535&e[2],e[0]<<16|e[0]>>>16,4294901760&e[2]|65535&e[3],e[1]<<16|e[1]>>>16,4294901760&e[3]|65535&e[0]],i=this._b=0;i<4;i++)Te.call(this);for(i=0;i<8;i++)o[i]^=n[i+4&7];if(t){var r=t.words,s=r[0],a=r[1],c=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),l=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),h=c>>>16|4294901760&l,d=l<<16|65535&c;for(o[0]^=c,o[1]^=h,o[2]^=l,o[3]^=d,o[4]^=c,o[5]^=h,o[6]^=l,o[7]^=d,i=0;i<4;i++)Te.call(this)}},_doProcessBlock:function(e,t){var n=this._X;Te.call(this),Se[0]=n[0]^n[5]>>>16^n[3]<<16,Se[1]=n[2]^n[7]>>>16^n[5]<<16,Se[2]=n[4]^n[1]>>>16^n[7]<<16,Se[3]=n[6]^n[3]>>>16^n[1]<<16;for(var o=0;o<4;o++)Se[o]=16711935&(Se[o]<<8|Se[o]>>>24)|4278255360&(Se[o]<<24|Se[o]>>>8),e[t+o]^=Se[o]},blockSize:4,ivSize:2}),ye.RabbitLegacy=me._createHelper(Ie),xe.pad.ZeroPadding={pad:function(e,t){var n=4*t;e.clamp(),e.sigBytes+=n-(e.sigBytes%n||n)},unpad:function(e){var t=e.words,n=e.sigBytes-1;for(n=e.sigBytes-1;0<=n;n--)if(t[n>>>2]>>>24-n%4*8&255){e.sigBytes=n+1;break}}},xe}()},815:(e,t,n)=>{var o=n(21),i=n(713),r={base_url:"",app_id:"",package_name:"",company_id:"",sign_key:"",idfa:"",caid:"",user_id:"",wx_open_id:"",wx_union_id:"",is_show_log:0,refer:"",user_flag:0,XingYun_Resp_Success_Code:7,XingYun_Resp_Fail_code:4,public_config:{open:{type:"every_time",score:0,disabled:!1},login:{type:"every_time",score:0,disabled:!0},create_account:{type:"one_time",score:0,disabled:!0},create_role:{type:"one_time",score:0,disabled:!0},enter_game:{type:"every_time",score:0,disabled:!0},post_pay:{type:"every_time",score:0,disabled:!0},pay:{type:"every_time",score:0,disabled:!0},bind_mobile:{type:"one_time",score:0,disabled:!0},bind_idcard:{type:"one_time",score:0,disabled:!0},d1:{type:"one_time",score:0,disabled:!0},kbi:{type:"one_time",score:0,disabled:!0},online:{type:"every_time",score:0,disabled:!1},upgrade:{type:"every_time",score:0,disabled:!1},ad:{type:"every_time",score:0,disabled:!1}},TFEventType:{OPEN:"open",ENTER:"enter_game",POST_PAY:"post_pay",PAY:"pay",CREATE_ROLE:"create_role",CREATE_ACCOUNT:"create_account",LOGIN:"login",UPGRADE:"upgrade",ONLINE:"online",BIND_PHONE:"bind_mobile",BIND_IDCARD:"bind_idcard",D1:"d1",KBI:"kbi",AD:"ad"},attributionSearch:function(e,t,n){e&&e.length>5?r.XingYunGetAsync(e+"/sdk/search?open_id="+t,(function(e,t){if(t===r.XingYun_Resp_Success_Code){var o=JSON.parse(e).app_id;if(o)return void n({app_id:o})}n({app_id:0})})):(console.log("attributionSearch base_url is empty"),n({app_id:0}))},adClickReport:function(){if(null==this.base_url||this.base_url.length<11)console.log("no report url,no use function");else{var e=wx.getLaunchOptionsSync().query;console.log("launchQuery",e);var t=void 0,n=void 0;if(e&&e.gdt_vid&&(t="gdt_vid="+e.gdt_vid+"&weixinadinfo="+e.weixinadinfo+"&weixinadkey="+e.weixinadkey+"&scene_type="+e.scene_type,n="guangdiantong"),e&&e.clue_token){var o=e.clue_token,i=e.ad_id,s=e.creative_id,a=e.req_id;t="callback="+o+"&ad_id="+i+"&ttCreativeId="+s,a&&(t+="&req_id="+a),n="toutiao"}e&&e.channel&&e.trackid&&"bili"==e.channel&&(t="callback="+e.trackid+"&ad_id="+e.ad_id+"&account_id="+e.account_id,n="bilibili"),e&&e.callback&&e.ksUnitId&&(t="callback="+e.callback+"&ad_id="+e.ksUnitId,n="kuaishou");var c=this;if(n&&t&&t.length>10){c.user_flag=1;var l=this.base_url+"/sdk/ad/click/"+this.app_id+"/"+n+"/"+this.company_id;t+="&open_id="+this.wx_open_id,l+="?"+(t+="&union_id="+this.wx_union_id),r.XingYunGetAsync(l,(function(e,t){c.tfRegister()}))}else this.tfRegister()}},d1Report:function(){var e=new Date,t=12*e.getMonth()+e.getDate(),n=r.XingYunLocalStorage.getItem("XingYun"+this.TFEventType.D1);1e3!=n&&(n&&t-n==1?(this.tfEvent(this.TFEventType.D1,"",""),r.XingYunLocalStorage.setItem("XingYun"+this.TFEventType.D1,1e3)):r.XingYunLocalStorage.setItem("XingYun"+this.TFEventType.D1,t))},tfInit:function(e,t,n,o,i,s,a,c,l){r.tfShowLog("tfInit init"),r.base_url=e,r.app_id=t,r.package_name=n,r.company_id=o,r.sign_key=i,r.wx_open_id=a,r.wx_union_id=c,r.idfa="",r.caid="",r.user_id=s;const h=wx.getSystemInfoSync();h&&(r.refer=h.platform);var d=r.XingYunLocalStorage.getItem("openCount");d=d&&"undefined"!=d&&"NaN"!=d?parseInt(d)+1:1,r.XingYunLocalStorage.setItem("openCount",d),r.openCount=d,!r.XingYunLocalStorage.getItem("kbi_config")&&l&&r.XingYunGetAsync(l,(function(e,t){t===r.XingYun_Resp_Success_Code&&r.XingYunLocalStorage.setItem("kbi_config",e)})),this.adClickReport();var f=setTimeout((function(){setInterval((function(){clearTimeout(f),r.tfShowLog("heartbeat"),r.tfEvent("online","5")}),3e5)}),6e4)},tfSetUserId:function(e){r.user_id=e},tfShowLog:function(...e){1==this.is_show_log&&console.log(e)},tfInitEvent:function(){r.tfEvent(r.TFEventType.OPEN,""),r.tfCreateAccountEvent(r.TFEventType.CREATE_ACCOUNT,r.user_id,""),r.tfEvent(r.TFEventType.LOGIN,r.user_id,""),r.d1Report()},tfRegister:function(){try{if(null==r.base_url||r.base_url.length<5)return void console.log("没填参数，不接入此功能");if(r.tfShowLog("register"),r.XingYunLocalStorage.getItem("device_id"))r.tfShowLog("device_id存在，直接进行open上报"),r.device_id=r.XingYunLocalStorage.getItem("device_id"),r.click_id=r.XingYunLocalStorage.getItem("click_id"),this.tfInitEvent();else{r.tfShowLog("device_id=undefined");var e=r.base_url+"/sdk/regirest",t=new Map;t.set("app_id",r.app_id),t.set("package_name",r.package_name),t.set("company_id",r.company_id),t.set("refer",r.refer),t.set("open_id",r.wx_open_id),t.set("union_id",r.wx_union_id);var n=!0;t.forEach((function(t,o){n?(n=!1,e+="?"+o+"="+t):e+="&"+o+"="+t})),r.tfShowLog("url="+e);var o=this;r.XingYunGetAsync(e,(function(e,t){if(t===r.XingYun_Resp_Success_Code)try{var n=JSON.parse(e);r.device_id=n.device_id,r.click_id=n.click_id,r.XingYunLocalStorage.setItem("device_id",n.device_id),r.XingYunLocalStorage.setItem("click_id",n.click_id),o.tfInitEvent()}catch(t){console.log("xingyun init request error:"+e)}}))}console.log("device_id:"+r.device_id)}catch(e){console.log(e)}},tfCreateAccountEvent(e,t,n){var o=r.XingYunLocalStorage.getItem(e+t);t&&t!=o?this.tfEvent(e,t,n):r.tfShowLog("this account already report:"+t)},tfEvent:function(e,t,n){try{var o=r.XingYunLocalStorage.getItem("kbi_config");r.tfShowLog("localKbiConfig"+o+"end");var s;s=o?JSON.parse(o):this.public_config;var a=parseInt(r.XingYunLocalStorage.getItem(e+"_time"))||0;if(!s[e])return void r.tfShowLog("事件类型不存在："+e);if(r.device_id)if(s[e].type){if(a++,r.XingYunLocalStorage.setItem(e+"_time",a),"one_time"===s[e].type&&a>1)return;var c=parseInt(r.XingYunLocalStorage.getItem("kbi_score"))||0;c+=parseInt(s[e].score);var l={},h=new Array;l.action_name=e,l.log_time=this.XingYunGetFormatTime(),l.user_id=r.user_id,l.value=t,l.device_id=r.device_id,l.click_id=r.click_id,l.ua=navigator.userAgent,l.open_id=r.wx_open_id,l.union_id=r.wx_union_id,l.unique=n||"",l.index=r.openCount,l.refer=r.refer,l.t=parseInt((new Date).getTime()/1e3),h.push(l);var d=r.XingYunLocalStorage.getItem("cacheEvent");r.XingYunLocalStorage.removeItem("cacheEvent"),d&&"undefined"!=d&&"NaN"!=d&&(h=JSON.parse(d).concat(h));var f=JSON.stringify(h),u={},g={};g.appId=r.app_id,g.packageName=r.package_name,g.companyId=r.company_id,g.sign=i(f+r.sign_key),u.headers=g,u.content=f;var p=void 0;try{p=this.XingYunEncryptByDES(JSON.stringify(u),"asdf1234")}catch(t){return console.log("eventType:"+e,t),void r.saveEventToLocal(f)}var _=r.base_url+"/sdk/v3/event";r.XingYunPostAsync(_,p,(function(n,o){200===n?(r.TFEventType.CREATE_ACCOUNT==e&&r.XingYunLocalStorage.setItem(r.TFEventType.CREATE_ACCOUNT+t),r.tfShowLog("success: "+n+"-eventType:"+e)):(console.log("failed: "+n+"-eventType:"+e),r.saveEventToLocal(f))})),r.XingYunLocalStorage.setItem("kbi_score",c),c>=100&&r.tfEvent("kbi","")}else console.log("config not report this event:"+e);else console.log("device_id是空，无法上报："+e)}catch(e){console.log(e),r.XingYunLocalStorage.removeItem("kbi_config")}},XingYunGetAsync:function(e,t){r.tfShowLog("XingYunGetAsync",e);var n=new XMLHttpRequest;n.open("GET",e,!0),n.send(),n.onreadystatechange=function(){if(4==n.readyState)if(n.status>=200&&n.status<300){let e=n.response,o=n.responseText;e&&"object"==typeof e?o=JSON.stringify(e):e&&"string"==typeof e&&(o=e),t(o,r.XingYun_Resp_Success_Code)}else t("network error",r.XingYun_Resp_Fail_code)}},XingYunPostAsync:function(e,t,n){r.tfShowLog("XingYunPostAsync",e,t);var o=new XMLHttpRequest;o.open("POST",e,!0),o.send(t),o.onreadystatechange=function(){if(4==o.readyState){var e=o.status,t="response error";if(200==e){t=o.responseText;let e=o.response;e&&"object"==typeof e&&(t=JSON.stringify(e))}n(e,t)}}},XingYunGetFormatTime:function(){var e=new Date,t=e.getFullYear(),n=e.getMonth()+1,o=e.getDate();return n>=1&&n<=9&&(n="0"+n),o>=0&&o<=9&&(o="0"+o),t+"-"+n+"-"+o+" "+e.getHours()+":"+e.getMinutes()+":"+e.getSeconds()},XingYunEncryptByDES:function(e,t){r.tfShowLog(e);var n=o.enc.Utf8.parse(t),i=o.enc.Utf8.parse("12345678"),s=o.DES.encrypt(e,n,{iv:i,mode:o.mode.CBC,padding:o.pad.Pkcs7});return this.XingYunHexToBase644(s.ciphertext.toString())},XingYunDecryptByDES:function(e,t){var n=o.enc.Utf8.parse(t),i=o.enc.Utf8.parse("12345678");return o.DES.decrypt({ciphertext:o.enc.Hex.parse(e)},n,{iv:i,mode:o.mode.CBC,padding:o.pad.Pkcs7}).toString(o.enc.Utf8)},XingYunHexToBase644:function(e){for(var t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",n="",o=0,i=0,r=0;r<e.length;++r)o=o<<4|(e[r]>="A"&&e[r]<="Z"?e.charCodeAt(r)-55:e[r]>="a"&&e[r]<="z"?e.charCodeAt(r)-87:e.charCodeAt(r)-48),(i+=4)>=6&&(n+=t[o>>>(i-=6)],o&=~(-1<<i));i>0&&(n+=t[o<<=6-i]);var s=n.length%4;if(s>0)for(r=0;r<4-s;++r)n+="=";return n},XingYunLocalStorage:{getItem:function(e){r.tfShowLog("getItem",e);try{return wx.getStorageSync(e)}catch(t){console.log("getItem error:"+e,t)}return""},setItem:function(e,t){try{r.tfShowLog("setItem",e,t),wx.setStorageSync(e,t)}catch(t){console.log("strong set item error:"+e)}},removeItem:function(e){try{wx.removeStorageSync(e)}catch(t){console.log("removeItem error:"+e,t)}}},saveEventToLocal:function(e){var t=r.XingYunLocalStorage.getItem("cacheEvent"),n=JSON.parse(e);if(t&&"undefined"!=t&&"NaN"!=t){var o=JSON.parse(t);n=n.concat(o)}n.length<=10?r.XingYunLocalStorage.setItem("cacheEvent",JSON.stringify(n)):r.XingYunLocalStorage.removeItem("cacheEvent")}};e.exports=r},19:e=>{function t(){return wx.getStorageSync("common_sdk_unique_id")}function n(e,n,o,i,r){f("requestUrl:"+e),f("requestData:",n);var s=e.includes("?",1);e+=s?"&device_id="+t():"?device_id="+t(),wx.request({url:e,data:n,header:{"content-type":i},method:r||"POST",success(e){f(e.data),o&&o(1,e.data)},fail(e){console.log(e),o&&o(0,"network request error")}})}function o(){var e=wx.getStorageSync("channel_sdk_api_host");return e&&e.length>12?e:"https://sdk.xibanq.com"}function i(e,t,i,r,s){n(o()+e,t,i,r,s)}var r=0;function s(e,t){var n=e;if(0==n.code){var o=n.msg,i=o.open_id,s=o.login_key,c=o.token,l=o.pay_channel_id,h=o.wechat,d=h.openid,u=h.unionid,g=h.session_key,p=o.share_str,_=o.share_pic,y=o.timestamp,m=o.notify_url,v=o.login_count,S=o.server_state;r=o.showDebugLog,f("serverState",S),S||(S=0),r||(r=0),f("serverState2",S);try{wx.setStorageSync("common_sdkOpenId",i+""),wx.setStorageSync("common_wxOpenId",d),wx.setStorageSync("common_wxUnionid",u),wx.setStorageSync("common_sdkLoginKey",s),wx.setStorageSync("common_sdkPayChannelId",l),wx.setStorageSync("common_wxSessionKey",g),wx.setStorageSync("common_wxShareTitle",p),wx.setStorageSync("common_wxShareImage",_),wx.setStorageSync("common_sdk_notify_url",m)}catch(e){}t(1,{openId:i,loginKey:s,token:c,wxOpenId:d,timestamp:y,serverState:S,loginCount:v}),function(e){if(e&&e.length>0){e[0].length<2&&console.log("ke fu item 0 is empty");for(var t="",n=0;n<e.length;n++)t+=e[n]+"\n";wx.showModal({title:"游戏公告",content:t,confirmText:"已知悉",showCancel:!1})}}(o.kefu_info)}else 2==n.code?a(e.msg):(console.log("loginResult:",e),t(0,"server callback error"))}function a(e){wx.showModal({title:"账号登录异常",content:e,confirmText:"好的",confirmColor:"#C7C7C8",showCancel:!1,success:function(t){t.confirm&&a(e)}})}function c(e,t,n){wx.showShareMenu({withShareTicket:!0,menus:["shareAppMessage","shareTimeline"]}),wx.onShareAppMessage((function(){return{title:e,imageUrl:t,query:n}})),wx.onShareTimeline((function(){return{title:e,imageUrl:t,query:n}}))}function l(e,t,n){wx.onShareTimeline((function(){return{title:e,imageUrl:t,query:n}}))}function h(e,t,n){wx.showModal({title:"温馨提示",content:e||"请您发送“充值”或“CZ”获取订单链接。",showCancel:!1,success(e){e.confirm?wx.openCustomerServiceConversation({showMessageCard:!0,sendMessagePath:t,sendMessageImg:n}):e.cancel&&console.log("用户点击取消")}})}function d(e){var t=wx.getStorageSync("common_sdk_notify_url");if(t){var o="app_id="+wx.getStorageSync("common_sdkAppId");o+="&open_id="+wx.getStorageSync("common_sdkOpenId"),o+="&query_id="+e,o={app_id:wx.getStorageSync("common_sdkAppId"),open_id:wx.getStorageSync("common_sdkOpenId"),query_id:e},n(t,o=JSON.stringify(o),(function(e,t){console.log("confirm result:"+t)}),"application/x-www-form-urlencoded")}else wx.showToast({icon:"none",title:"确认订单地址错误"})}function f(...e){1==r&&console.log(e)}function u(e,t,n,o){console.log("aaamount:"+e),wx.requestMidasPayment({mode:"game",env:o?1:0,offerId:t,currencyType:"CNY",platform:"android",zoneId:1,buyQuantity:e,success:function(){f("pay success"),d(n)},fail:function(e){console.log("pay error:",e)}})}var g=void 0;function p(){console.log("closeClickHandler"),g&&Laya.stage.removeChild(g)}e.exports={init:function(e){try{wx.setStorageSync("channel_sdk_api_host",e+"")}catch(e){console.log("init api host error")}},setUniqueId:function(e){try{e&&e.length>15&&wx.setStorageSync("common_sdk_unique_id",e+"")}catch(e){}},login:function(e,t,n){wx.setStorageSync("common_sdkAppId",e),f("commonSdkLogin"),wx.login({success(o){if(o.code){const a=wx.getSystemInfoSync();var r="";a&&(r=a.platform),f("currentPlat",r),i("/sdk/login?format=json&type=7&mk=","code="+o.code+"&app_id="+e+"&version="+n+"&scene="+r,(function(e,n){1==e?s(n,t):t(0,"login fail")}),"application/x-www-form-urlencoded")}else t&&t(0,o.errMsg)}})},menuShareInit:function(e){c(wx.getStorageSync("common_wxShareTitle"),wx.getStorageSync("common_wxShareImage"),e)},menuShareInit2:c,setWxMenuShareContent:function(e){var t=wx.getStorageSync("common_wxShareTitle"),n=wx.getStorageSync("common_wxShareImage");wx.onShareAppMessage((function(){return{title:t,imageUrl:n,query:e}})),wx.onShareTimeline((function(){return{title:t,imageUrl:n,query:e}}))},sdkShareContent:function(e){!function(e,t,n){wx.shareAppMessage({title:e,imageUrl:t,query:n})}(wx.getStorageSync("common_wxShareTitle"),wx.getStorageSync("common_wxShareImage"),e)},menuShareTimeline:function(e){l(wx.getStorageSync("common_wxShareTitle"),wx.getStorageSync("common_wxShareImage"),e)},menuShareTimeline2:l,sdkUserCloudStorage:function(e,t){var n={wxgame:{score:parseInt(t),update_time:Date.parse(new Date)/1e3}},o={key:e,value:JSON.stringify(n)};wx.setUserCloudStorage({KVDataList:[o]})},sdkPay:function(e,t,n){!function(e,t){var n="role_name="+e.playerName;n+="&encode=0",n+="&login_key="+wx.getStorageSync("common_sdkLoginKey"),n+="&pay_channel_id="+wx.getStorageSync("common_sdkPayChannelId"),n+="&productid="+e.productId,n+="&money="+e.postAmount,n+="&open_id="+wx.getStorageSync("common_sdkOpenId"),n+="&role_id="+e.playerId,n+="&callback="+e.queryId,n+="&game_amount="+10*e.postAmount,n+="&serverid="+e.serverId,n+="&app_id="+wx.getStorageSync("common_sdkAppId"),f("sdkCreateOrderRequest:",n+="&wx_open_id="+wx.getStorageSync("common_wxOpenId")),i("/sdk/pay?format=json",n,t,"application/x-www-form-urlencoded")}(e,(function(i,r){if(f("create order result:"+r),1==i)if(0===r.code){var s=r.msg.pay_param;if(s){var a=s.pay_type;if("kefu"==a){const e={action:"pay"};return void h("请您发送“充值”或“CZ”获取订单链接。",JSON.stringify(e),o()+"/static/img/pay_card.jpeg")}if("qrcode"==a){if(s)var c=s.next_url;return void(c&&function(e){if(Laya){var t=new Laya.Sprite,n=canvas.width,o=canvas.height,i=2*n/3,r=(n-i)/2,s=new Laya.Image(e);s.x=r,s.y=o/2-i/2,s.width=i,s.height=i,t.addChild(s);var a=new Laya.Label("扫码支付");a.fontSize=50,a.font="Arial",a.color="#000000",a.bgColor="#FFFFFF",a.align="center",a.x=r,a.width=i,a.padding="16,16,16,16",a.y=s.y-a.height-16,t.addChild(a);var c=new Laya.Button;c.height=a.height,c.width=a.height,c.stateNum=1,c.x=a.x+i-c.width,c.y=a.y,console.log("Pheight:"+c.height),c.label="X",c.labelSize="50",c.labelFont="Arial",c.labelPadding="16,16,16,16",c.labelColors="#A7A7A8,#A7A7A8,#A7A7A8,#A7A7A8",c.clickHandler=new Laya.Handler(window,p),t.addChild(c),Laya.stage.addChild(t),g=t}}(c))}}if(s&&0==s.errcode){var l=s.balance,_=parseInt(e.exchange),y=e.postAmount*_,m=s.query_id;if(l==y){var v=l/_;wx.showModal({content:"您账户上有余额："+v+"元，您选择确定会使用余额进行购买此商品，选择取消的话不做购买操作，您可以再去选择其他对应余额的商品进行购买！",showCancel:!0,cancelText:"取消",confirmText:"确定",success:function(e){e.confirm&&d(m)}})}else l>=_?(v=l/_,wx.showModal({content:"您账户上有余额："+v+"元，您可以选择取消购买，然后到对应金额档位进行余额消耗或者选择继续购买进行此商品购买！",showCancel:!0,cancelText:"取消购买",confirmText:"继续购买",success:function(e){e.confirm&&u(y,t,m,n)}})):(f("balance:"+l),u(y,t,m,n))}else console.log("error:",s),wx.showToast({icon:"none",title:"接口错误"})}else wx.showToast({icon:"none",title:""+r.msg});else wx.showToast({icon:"none",title:"充值失败,请检查您的网络"})}))},initSdkAd:function(e,t){try{if(wx.setStorageSync("common_bannerAdId",e+""),wx.setStorageSync("common_videoAdId",t),f("videoAdId:"+t),t.length>1){let e=wx.createRewardedVideoAd({adUnitId:t});e.onError((e=>{console.log(e)})),e.load().then((function(){}))}}catch(e){}},showBannerAd:function(e){let t=wx.getStorageSync("common_bannerAdId");typeof window.sdkBannerAd==BannerAd&&window.sdkBannerAd.destroy(),window.sdkBannerAd=wx.createBannerAd({adUnitId:t,style:e}),window.sdkBannerAd.show().catch((e=>console.log("show banner ad error:",e)))},hideBannerAd:function(){typeof window.sdkBannerAd==BannerAd&&(window.sdkBannerAd.hide(),window.sdkBannerAd.destroy())},showRewardedVideoAd:function(e){let t=wx.getStorageSync("common_videoAdId"),n=wx.createRewardedVideoAd({adUnitId:t});n.offClose(),n.onClose((t=>{f("onClose",t),t&&t.isEnded||void 0===t?e&&e(1,"play finish"):e&&e(0,"user click exit"),n.offClose(e)})),n.show().then((function(){})).catch((t=>{console.log("show video ad error:",t),n.load().then((()=>n.show())).catch((n=>{console.log("show video ad error2:",t),e&&n&&n.errCode?e(n.errCode,""+n.errMsg):e(1e3,"unknow error")}))}))},msgSecCheck:function(e,t){f("sdkMsgSecCheck");var n="/sdk/msg_sec_check?format=json&type=7&mk=";n+="&app_id="+wx.getStorageSync("common_sdkAppId"),n+="&login_key="+wx.getStorageSync("common_sdkLoginKey"),i(n+="&open_id="+wx.getStorageSync("common_sdkOpenId"),"content="+e.content+"&scene="+e.scene,(function(e,n){if(1==e)if(f("msgCheckResult",n),0==n.code){var o=n.msg.errcode,i=n.msg.errmsg;0==o?t(1,"success"):t(0,""+i)}else t(0,"server error");else t(0,"network error"),console.log("sdkMsgSecCheck error:"+n)}),"application/x-www-form-urlencoded","POST")},requestSubscribeWhatsNew:function(e){wx.requestSubscribeWhatsNew({msgType:1,success(t){f("requestSubscribeWhatsNew",t),!0===t.confirm?e(1,"success"):e(0,"user cancel")},fail(t){console.log("requestSubscribeWhatsNewe",t),e(0,"Subscribe fail")}})},getWhatsNewSubscriptionsSetting:function(e){wx.getWhatsNewSubscriptionsSetting({msgType:1,success(t){f("getWhatsNewSubscriptionsSetting",t),t&&2===t.status?e(1,"已订阅"):e(0,"未订阅")},fail(t){console.log("getWhatsNewSubscriptionsSettinge",t),e(0,"未订阅")}})},sdkUserCenter:function(){try{const e={action:"change_plat",open_id:wx.getStorageSync("common_sdkOpenId")};console.log("changPlatParam",e),h('请您发送"密码”，重置当前密码设置。',JSON.stringify(e),o()+"/static/img/change_plat.jpg")}catch(e){}}}}},t={};function n(o){var i=t[o];if(void 0!==i)return i.exports;var r=t[o]={exports:{}};return e[o].call(r.exports,r,r.exports,n),r.exports}n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),(()=>{"use strict";var e=n(713);class t{constructor(){this.checkConfigListener="",this.checkOrderListener="",this.statisticsListenerProxy="",this.location="",this.configInfoReturn,this.gzyasjhInfo={},this.configCheckUrl="https://static.public.sdk.guangkatf.com/config",this.sdkInfo={gameSimpleName:"",sdkSimpleName:"",sdkVersionCode:"",sdkClientVersion:"",loginCheckUrl:"",payOrderUrl:"",payCheckUrl:"",logReportUrl:"",statisticsUrl:"",channelParameter1:"",channelParameter2:"",channelId:"",custom:""},this.loginInfo={gameSimpleName:"",sdkSimpleName:"",sdkVersionCode:"",result:"",userType:"",openId:"",userName:"",sign:"",timestamp:"",other:"",serverSign:"",custom:"",custom2:"",di:"",nt:"",gv:"",ot:"",imei:"",rl:"",c1:"",c2:""},this.payInfo={serverId:"",serverName:"",playerId:"",playerName:"",playerLevel:"",postAmount:"",productId:"",productName:"",productDesc:"",custom:"",exchange:"",otherInfo:"",timestamp:"",tsign:"",userId:"",di:"",nt:"",gv:"",ot:"",imei:"",rl:"",c1:"",c2:"",coinName:"",coinNum:""},this.orderInfo={queryID:"",serverID:"",roleId:"",postTime:"",postAmount:"",productID:"",currency:"",other:"",products:"",danjiConfig:""},this.roleInfo={roleId:"",roleName:"",roleLevel:"",roleSex:"",serverId:"",serverName:"",roleCTime:"",partyName:"",roleType:"",roleChangeTime:"",vipLevel:"",diamond:"",moneyType:"",custom:""}}static getInstance(){return t.instance||(t.instance=new t),t.instance}connection(e,t,n){var o=this,i=new XMLHttpRequest;if(i.onreadystatechange=function(){if(4==i.readyState)if(i.status>=200&&i.status<300||304==i){o.sdkLog("H5SDKLog||"+e+"||Connect server successful:"+i.responseText);try{o.sdkLog(typeof i.response);let t=i.response,n=i.responseText;t&&"object"==typeof t?n=JSON.stringify(t):t&&"string"==typeof t&&(n=t),o.responseListener(e,JSON.parse(n),i.status)}catch(e){o.sdkLog(e)}}else o.noResponse(e),o.sdkLog("H5SDKLog||"+e+"||Fail to connect server!");o.sdkLog("readyState: "+i.readyState)},"login"==e){i.open("post",t,!1);var r="data="+JSON.stringify(this.loginInfo);o.sdkLog("login url: "+t),o.sdkLog("login data: "+r),i.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),i.send(r)}else if("submitInfo"==e)r=this.addUrlParam("",n),i.open("post",t,!1),i.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),i.send(r.substring(1));else if("statistics"==e)i.open("post",t,!1),i.setRequestHeader("Content-Type","application/x-www-form-urlencoded; charset=UTF-8"),r=JSON.stringify(n),i.send(r);else{if("configInfo"==e)t=t+"/"+this.sdkInfo.gameSimpleName+"/"+this.sdkInfo.sdkSimpleName+"/"+this.sdkInfo.sdkVersionCode+"/config.json?"+(new Date).getTime().toString(),o.sdkLog("configInfo url:"+t);else if("location"==e);else if("payCheck"==e){var s=JSON.stringify(n);window.conch||(s=encodeURIComponent(s)),t=t+"/"+this.sdkInfo.gameSimpleName+"/"+this.sdkInfo.sdkSimpleName+"/"+this.sdkInfo.sdkVersionCode+"?data="+s}else t=t+"/"+this.sdkInfo.gameSimpleName+"/"+this.sdkInfo.sdkSimpleName+"/"+this.sdkInfo.sdkVersionCode,t=this.addUrlParam(t,n);o.sdkLog(t),i.open("get",t,!1),i.send(null)}}configCheck(e,t){for(var n in"string"==typeof e&&(e=JSON.parse(e)),e)this.sdkInfo[n]=e[n],"otherInfo"===n&&this.setGzyasjhInfo(e[n]),this.sdkLog(this.sdkInfo[n]);this.checkConfigListener=t,this.sdkLog("H5SDKLog||Ask configCheck to server with sdkInfo:"+JSON.stringify(this.sdkInfo)),this.connection("configInfo",this.configCheckUrl,this.sdkInfo)}loginCheck(e){for(var t in e)this.loginInfo[t]=e[t];this.loginInfo.sign=e.token,this.loginInfo.custom=commonH5SDK.h5Proxy.loginExt,this.loginInfo=this._addDataToTable(this.loginInfo),this.sdkLog("H5SDKLog||Ask loginCheck to server with loginInfo:"+JSON.stringify(this.loginInfo)),this.connection("login",this.sdkInfo.loginCheckUrl,this.loginInfo)}payCheck(t,n){for(var o in t)this.payInfo[o]=t[o];null!=t.openId&&t.openId.length>0&&(this.payInfo.userId=t.openId),this.payInfo.tsign=e(this.payInfo.timestamp+"12*&#**@321"),this.checkOrderListener=n,this.payInfo=this._addDataToTable(this.payInfo),this.sdkLog("H5SDKLog||Ask payCheck to server with payInfo:"+JSON.stringify(this.payInfo)),this.connection("payCheck",this.sdkInfo.payOrderUrl,this.payInfo)}submitInfo(t,n){if(null!=n)for(var o in n)this.roleInfo[o]=n[o];var i="";-1!=t.indexOf(":")&&(i=t.substring(t.indexOf(":")+1));var r={pid:this.roleInfo.roleId,pn:this.roleInfo.roleName,sid:this.roleInfo.serverId,sn:this.roleInfo.serverName,lv:this.roleInfo.roleLevel,openId:this.loginInfo.openId,gameSimpleName:this.sdkInfo.gameSimpleName,sdkSimpleName:this.sdkInfo.sdkSimpleName,sdkVersionCode:this.sdkInfo.sdkVersionCode,di:"",nt:"",gv:"",ot:"",imei:"",rl:"",c1:this.sdkInfo.channelParameter1,c2:this.sdkInfo.channelParameter2,eventName:i};r=this._addDataToTable(r);var s=(new Date).getTime().toString();s=s.substring(0,s.length-3),this.sdkLog(JSON.stringify(r));var a="action="+t+"jssdk=yt="+s+"12909asdask23",c={action:t,data:JSON.stringify(r),jssdk:"y",t:s,ta:"default",test:"test",sign:e(a)};this.sdkLog("H5SDKLog||Submit to server with roleCheckInfo:"+JSON.stringify(c));var l=this.sdkInfo.logReportUrl+"?ya_game="+this.sdkInfo.gameSimpleName+"&ya_sn="+this.sdkInfo.sdkSimpleName+"&ya_ssv="+this.sdkInfo.sdkVersionCode;this.connection("submitInfo",l,c)}statistics(e,t){this.statisticsListenerProxy=t,this.connection("statistics",this.sdkInfo.statisticsUrl,e)}addUrlParam(e,t){for(var n in t)e=(e+=-1==e.indexOf("?")?"?":"&")+n+"="+t[n];return e}responseListener(e,t,n){if("configInfo"==e)1==t.code?(this.sdkInfo.payCheckUrl=t.data.payCheckUrl,this.sdkInfo.payOrderUrl=t.data.payOrderUrl,this.sdkInfo.loginCheckUrl=t.data.loginCheckUrl,this.sdkInfo.logReportUrl=t.data.logReportUrl,this.sdkInfo.channelParameter1=t.data.channelParameter1,this.sdkInfo.custom=t.data.custom,this.sdkInfo.channelId=t.data.channelSimpleName,this.loginInfo.c1=t.data.channelParameter1,this.payInfo.c1=t.data.channelParameter1,this.sdkInfo.channelParameter2=t.data.channelParameter2,this.loginInfo.c2=t.data.channelParameter2,this.payInfo.c2=t.data.channelParameter2,this.sdkLog("H5SDKLog||ConfigCheck successful with configInfo:"+JSON.parse(t.data.clientConfig)),this.configInfoReturn=JSON.parse(t.data.clientConfig),this.checkConfigListener.onCheckConfigSuccess()):(this.sdkLog("H5SDKLog||No configs!Please check whether the sdkInfo is matching or not!"),this.checkConfigListener.onCheckConfigFail());else if("login"==e)1==t.code?(this.sdkLog("H5SDKLog||LoginCheck successful with loginInfo:"+JSON.stringify(t)),this.loginInfo.openId=t.openId,this.payInfo.userId=t.openId,this.loginInfo.custom=commonH5SDK.h5Proxy.loginExt,t.custom=commonH5SDK.h5Proxy.loginExt,t.custom2=this.loginInfo.custom2,t.serverState=this.loginInfo.serverState?this.loginInfo.serverState:0,commonH5SDK.callback(commonH5SDK.code.LOGIN_SUCCESS,t)):(this.sdkLog("H5SDKLog||LoginCheck fail with message:"+t.message),commonH5SDK.callback(commonH5SDK.code.LOGIN_FAIL,"login error"));else if("payCheck"==e){var o,i,r;for(var s in t)"code"==s?o=t[s]:"data"==s?i=t[s]:"message"==s&&(r=t[s]);this.sdkLog(o),this.sdkLog(i),this.sdkLog(r),1==o?(this.sdkLog("H5SDKLog||PayCheck successful with orderInfo:"+JSON.stringify(i)),this.checkOrderListener.onSuccess(i)):(this.sdkLog("H5SDKLog||PayCheck fail with message:"+r),this.checkOrderListener.onFail())}else"roleInfo"==e?this.sdkLog("H5SDKLog||Submit roleInfo result:"+t):"statistics"==e?200==n?(this.sdkLog("H5SDKLog||Statistics result:"+n),this.statisticsListenerProxy.onStatisticsSuccess()):(this.sdkLog("H5SDKLog||Statistics result:"+n),this.statisticsListenerProxy.onStatisticsFail()):"location"==e&&(0==t.code?(this.location=t.data,this.sdkLog("Location:"+t.data)):this.sdkLog("Location:Fail!"))}noResponse(e){"configInfo"==e?this.checkConfigListener.onCheckConfigFail():"login"==e?commonH5SDK.callback(commonH5SDK.code.LOGIN_FAIL,"login check network error"):"payCheck"==e?this.checkOrderListener.onFail():"roleInfo"==e||("statistics"==e?this.statisticsListenerProxy.onStatisticsFail():"location"==e&&this.sdkLog("Location:Fail!"))}setGzyasjhInfo(e){if(e&&e.length>0){var t=JSON.parse(decodeURIComponent(e));t&&(this.gzyasjhInfo=t)}"object"!=typeof this.gzyasjhInfo&&(this.gzyasjhInfo={})}_addDataToTable(e){return"object"==typeof e&&(e.di=this.gzyasjhInfo.di||"",e.nt=this.gzyasjhInfo.nt||"",e.gv=this.gzyasjhInfo.gv||"",e.ot="",e.imei=this.gzyasjhInfo.imei||"",e.rl=this.gzyasjhInfo.rl||""),e}allinfo(){return{userAgent:(navigator.userAgent||"").replace(/[;/]/g," "),appName:navigator.appName||"",appVersion:(navigator.appVersion||"").replace(/[;/]/g," "),cookieEnabled:navigator.cookieEnabled||"",cpuClass:navigator.cpuClass||"",mimeType:navigator.mimeTypes||"",platform:navigator.platform||"",userLanguage:navigator.userLanguage||"",systemLanguage:navigator.systemLanguage||"",oscpu:navigator.oscpu||"",product:navigator.product||"",productSub:navigator.productSub||"",vender:navigator.vender||"",vendorSub:navigator.vendorSub||"",webkitPersistentStorage:navigator.webkitPersistentStorage||"",language:navigator.language||"",appCodeName:navigator.appCodeName||""}}getPayUrlCallBack(){return this.sdkInfo.payCheckUrl+"/"+this.sdkInfo.gameSimpleName+"/"+this.sdkInfo.sdkSimpleName+"/"+this.sdkInfo.sdkVersionCode}sdkLog(e){commonH5SDK.sdk_isShowLog&&console.log("commonH5SDK:"+e)}}var o=n(19),i=n(815);class r extends class{constructor(){this.hasInit=!1,this.hasLogout=!1,this.hasSwitchAccount=!1,this.hasFloatView=!1,this.hasUserCenter=!1,this.hasExitDialog=!1,this.sdkInfo={},this.loginExt="1",this.isPaying=0,this.gameSettingData={has_micro_client:0,is_share:0,is_focus:0,is_apple_pay:0,is_open_pay:1,is_desktop:0,is_switch_account:0,is_realverify:0,is_real_name_verify:-1,birthday:"",is_super_kefu:0,is_backtogame:0,is_lock:0,is_focus_gift:0,is_bindphone:0,is_msgseccheck:0,is_msgseccheck2:0,is_checkbalance:0,is_showad:0,is_usercenter:0,is_kefu:0,is_score:0,is_query_products_info:0,is_event_track:0,is_blue_vip_function:0,is_subscribe_whats_new:0,msg_subscribe_code:0,is_get_scene_interface:0,is_show_vip:0,is_show_qqun:0,is_query_promote_list:0}}init(e){console.log("super init")}login(e){console.log("super login")}logout(){console.log("super logout")}switchAccount(){console.log("super switchaccmount")}userCenter(){console.log("super userCenter")}submitData(e,t){console.log("super submitData")}expansionInterface(e,t,n){console.log("super expansionInterface"),commonH5SDK.expansion_type.gameSetting==e?n({code:0,message:"success",data:this.gameSettingData}):n({code:-1,message:""})}createOrder(e){var n=this;if(1!==this.isPaying){setTimeout((function(){n.isPaying=0}),5e3),this.isPaying=1;var o={};for(var i in e)o[i]=e[i];var r={onSuccess:function(e){commonH5SDK.h5Proxy.pay(o,e)},onFail:function(){console.log("create order fail"),commonH5SDK.callback(commonH5SDK.code.PAY_FAIL,"创建订单错误")}};t.getInstance().payCheck(o,r)}}pay(e,t){console.log("super pay")}showExitDialog(){console.log("super showExitDialog")}exitGame(){}}{constructor(){super(),this.appVersion="1.0",this.wxOpenId=""}init(e){this.appVersion=e.appVersion,o.init(t.getInstance().configInfoReturn.sdkApiHost)}isAdUser(){var e=wx.getLaunchOptionsSync().query;return e&&e.gdt_vid||e&&e.clue_token||e&&e.channel&&e.trackid||e&&e.callback&&e.ksUnitId?1:0}tfInit(e,n){var o=t.getInstance().configInfoReturn.tfUrl,r=t.getInstance().configInfoReturn.tfAppId,s=t.getInstance().configInfoReturn.wxAppId,a=t.getInstance().configInfoReturn.companyId,c=t.getInstance().configInfoReturn.signKey,l=t.getInstance().configInfoReturn.showLog;i.is_show_log=l,i.tfInit(o,r,s,a,c,n,e,"","")}login(e){var n=this;this.gameSettingData.is_share=1,this.gameSettingData.is_kefu=1,this.gameSettingData.is_msgseccheck2=1;var i=t.getInstance().configInfoReturn.bannerAdId,r=t.getInstance().configInfoReturn.videoAdId;o.initSdkAd(i,r);var s=t.getInstance().configInfoReturn.sdkAppId,a=t.getInstance();o.login(s,(function(e,t){if(1==e){var i={gameSimpleName:a.sdkInfo.gameSimpleName,sdkSimpleName:a.sdkInfo.sdkSimpleName,sdkVersionCode:a.sdkInfo.sdkVersionCode,result:"",userType:"",openId:t.openId,userName:"",token:t.token,timestamp:t.timestamp,other:t.loginKey,serverSign:"",serverState:t.serverState,wxOpenId:t.wxOpenId};if(n.wxOpenId=i.wxOpenId,t.loginCount<=2){var r=commonH5SDK.callback;commonH5SDK.callback=function(e,t){e==commonH5SDK.code.LOGIN_SUCCESS&&(t.userFlag=n.isAdUser()),r(e,t),commonH5SDK.callback=r}}n.tfInit(i.wxOpenId,i.openId),a.loginCheck(i),o.menuShareInit("")}else window.commonH5SDK.callback(commonH5SDK.code.LOGIN_FAIL,"sdk login fail"),console.log("login fail",t)}),this.appVersion)}submitData(e,t){switch(e){case window.commonH5SDK.type.enterGame:o.sdkUserCloudStorage("xizhai",t.roleLevel),o.setWxMenuShareContent("inviteId="+t.roleId),i.tfEvent(i.TFEventType.ENTER,"",""),o.setUniqueId(i.XingYunLocalStorage.getItem("device_id"));break;case window.commonH5SDK.type.createRole:i.tfEvent(i.TFEventType.CREATE_ROLE,t.roleId,"");break;case window.commonH5SDK.type.upRoleLevel:i.tfEvent(i.TFEventType.UPGRADE,t.roleId,"")}}createOrder(e){var t={device_id:i.XingYunLocalStorage.getItem("device_id"),wx_open_id:this.wxOpenId};e.other=JSON.stringify(t),super.createOrder(e)}pay(e,n){i.tfEvent(i.TFEventType.POST_PAY,e.postAmount,""),e.queryId=n.queryId;var r=t.getInstance().configInfoReturn.offerId;o.sdkPay(e,r,!1)}expansionInterface(e,t,n){switch(e){case commonH5SDK.expansion_type.qgGameShare:t&&0==t.type?o.sdkShareContent(t.data):t&&1==t.type&&o.menuShareTimeline(t.data);break;case commonH5SDK.expansion_type.showAd:var i=t.type;1==i?o.showRewardedVideoAd((function(e,t){n(1==e?{code:0,message:"success"}:0==e?{code:-1,message:"fail"}:{code:e,message:""+t})})):2==i?o.showBannerAd(t.style):-2==i&&o.hideBannerAd();break;case commonH5SDK.expansion_type.whatsNewSubscriptionsSetting:o.getWhatsNewSubscriptionsSetting((function(e,t){n(1==e?{code:0,message:"already subscribe"}:{code:-1,message:"not subscribe"})}));break;case commonH5SDK.expansion_type.subscribeWhatsNew:o.requestSubscribeWhatsNew((function(e,t){n(1==e?{code:0,message:"subscribe success"}:{code:-1,message:"subscribe fail"})}));break;case commonH5SDK.expansion_type.msgSecCheck2:o.msgSecCheck(t,(function(e,t){n(1==e?{code:0,message:"success"}:{code:-1,message:t})}));break;case commonH5SDK.expansion_type.kefu:o.sdkUserCenter();break;default:super.expansionInterface(e,t,n)}}}window.commonH5SDK={sdk_isShowLog:!1,code:{INIT_SUCCESS:1,INIT_FAIL:-1,LOGIN_SUCCESS:10,LOGIN_FAIL:-10,LOGOUT_SUCCESS:100,LOGOUT_FAIL:-100,SWITCH_SUCCESS:1e3,SWITCH_FAIL:-1e3,PAY_SUCCESS:2,PAY_FAIL:-2,PAY_CANCEL:-3,EXIT_SUCCESS:4,EXIT_CANCEL:-4},type:{arriveServerSelectPage:1,createRole:2,enterGame:3,upRoleLevel:4,arriveEntrance:5,enterServer:6,arriveFirstScene:7,arriveLoadingFinish:8,arriveLoadingZero:9,arriveCreateRolePage:10,newcomerFinish:11,checkpointStart:12,checkpointEnd:13,selectRole:14,renameRole:15,newcomerStart:16},expansion_type:{gameSetting:100,downloadMicroClent:101,share:102,focus:103,save2Desktop:104,realVerify:105,back2Game:106,bindPhone:107,msgSecCheck:108,msgSecCheck2:1081,checkBalance:109,showAd:110,score:111,kefu:112,queryProductsInfo:113,userCenter:114,eventTrack:115,realNameVerify:116,mulShare:117,blueVip:118,blueVipBuy:119,whatsNewSubscriptionsSetting:120,subscribeWhatsNew:121,firstEnterGameScene:122,msgSubscribe:123,qgGameShare:124,queryPromoteList:125,promoteClickLog:126,adBehaviorLog:127},callback:{},h5Proxy:"",init:function(e,n){this.callback=n;var o=this,i={onCheckConfigSuccess:function(){o.h5Proxy=new r,o.h5Proxy.init(e),o.h5Proxy.hasInit||n(commonH5SDK.code.INIT_SUCCESS,"初始化成功")},onCheckConfigFail:function(){console.log("onCheckConfigFail"),o.callback(o.code.INIT_FAIL,"初始化错误")}};t.getInstance().configCheck(e,i)},login:function(e){t.getInstance().submitInfo("event:openSDKLogin",null),this.h5Proxy.loginExt=e,this.h5Proxy.login(e)},pay:function(e){this.h5Proxy.createOrder(e)},hasLogout:function(){return this.h5Proxy.hasLogout},logout:function(){this.h5Proxy.logout()},hasSwitchAccount:function(){return this.h5Proxy.hasSwitchAccount},switchAccount:function(){return this.h5Proxy.switchAccount()},hasShowExitDialog:function(){return this.h5Proxy.hasExitDialog},showExitDialog:function(){this.h5Proxy.showExitDialog()},exitGame:function(){this.h5Proxy.exitGame()},submitData:function(e,n){switch(console.log("submitData type:"+e),e){case this.type.enterGame:t.getInstance().submitInfo("enter",n);break;case this.type.upRoleLevel:t.getInstance().submitInfo("upgrade",n)}this.h5Proxy.submitData(e,n)},getParamValueByKey:function(e){return t.getInstance().sdkInfo[e]},expansionInterface:function(e,t,n){this.h5Proxy.expansionInterface(e,t,n)},statistics:function(e,n){var o={onStatisticsSuccess:function(){n.onStatisticsSuccess()},onStatisticsFail:function(){n.onStatisticsFail()}};t.getInstance().statistics(e,o)}}})()})();