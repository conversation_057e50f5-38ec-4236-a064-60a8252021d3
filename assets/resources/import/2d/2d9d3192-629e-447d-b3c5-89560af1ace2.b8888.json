[1, 0, 0, [["cc.Json<PERSON>set", ["_name", "json"], 1]], [[0, 0, 1, 3]], [[0, "protoCompiled", {"nested": {"com": {"nested": {"cca": {"nested": {"core": {"nested": {"network": {"nested": {"cmd": {"nested": {"ClientCmdData": {"fields": {"appendData": {"id": 4, "type": "bytes"}, "clientIndex": {"id": 2, "rule": "required", "type": "sint32"}, "data": {"id": 3, "type": "bytes"}, "messageId": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdCache": {"fields": {"cacheName": {"id": 1, "rule": "required", "type": "string"}, "data": {"id": 3, "rule": "repeated", "type": "bytes", "options": {"packed": false}}, "type": {"id": 2, "rule": "required", "type": "int32"}}}, "CmdCacheField": {"fields": {"data": {"id": 2, "rule": "required", "type": "bytes"}, "fieldNumber": {"id": 1, "rule": "required", "type": "int32"}}}, "CmdCacheUpdateByField": {"fields": {"fields": {"id": 2, "rule": "repeated", "type": "CmdCacheField", "options": {}}, "key": {"id": 1, "rule": "required", "type": "bytes"}}}, "CmdDouble": {"fields": {"value": {"id": 1, "rule": "required", "type": "double"}}}, "CmdFloat": {"fields": {"value": {"id": 1, "rule": "required", "type": "float"}}}, "CmdInt32": {"fields": {"value": {"id": 1, "rule": "required", "type": "int32"}}}, "CmdInt64": {"fields": {"value": {"id": 1, "rule": "required", "type": "int64"}}}, "ServerCmdData": {"fields": {"clientIndex": {"id": 1, "rule": "required", "type": "sint32"}, "cmdCaches": {"id": 5, "rule": "repeated", "type": "CmdCache", "options": {}}, "compress": {"id": 6, "rule": "required", "type": "bool"}, "data": {"id": 4, "rule": "required", "type": "bytes"}, "messageIds": {"id": 2, "rule": "repeated", "type": "sint32", "options": {"packed": false}}, "result": {"id": 7, "rule": "required", "type": "bool"}, "splits": {"id": 3, "rule": "repeated", "type": "sint32", "options": {"packed": false}}}}}}}}}}}}, "xkhy": {"nested": {"tafang": {"nested": {"cmd": {"nested": {"CmdActivity": {"fields": {"activityRefId": {"id": 2, "type": "sint32"}, "detail": {"id": 5, "type": "CmdActivityDetail"}, "endTime": {"id": 4, "type": "sint64"}, "openResId": {"id": 1, "type": "sint32"}, "startTime": {"id": 3, "type": "sint64"}}}, "CmdActivityDetail": {"fields": {"cmdRechargeGiftAct2Detail": {"id": 4, "type": "CmdRechargeGiftAct2Detail"}, "cmdRechargeGiftAct3Detail": {"id": 5, "type": "CmdRechargeGiftAct3Detail"}, "dayChallengeActDetail": {"id": 1, "type": "CmdDayChallengeActDetail"}, "monthCardActDetail": {"id": 2, "type": "CmdMonthCardActDetail"}, "monthCardActDetail2": {"id": 6, "type": "CmdMonthCardActDetail"}, "rechargeGiftActDetail": {"id": 3, "type": "CmdRechargeGiftActDetail"}, "skinActDetail": {"id": 7, "type": "CmdSkinActDetail"}}}, "CmdActivityGoods": {"fields": {"resId": {"id": 1, "rule": "required", "type": "int32"}, "times": {"id": 2, "rule": "required", "type": "int32"}}}, "CmdActivityJsonReqMsg": {"fields": {"activityId": {"id": 1, "rule": "required", "type": "string"}, "tlJsonName": {"id": 2, "rule": "repeated", "type": "string", "options": {"packed": false}}}}, "CmdActivityJsonRspMsg": {"fields": {"content": {"id": 1, "rule": "required", "type": "string"}}}, "CmdActivityLimitSkinBuyReqMsg": {"fields": {"id": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdActivityLimitSkinBuyRspMsg": {"fields": {"cmdSkinPlayer": {"id": 2, "rule": "required", "type": "CmdSkinPlayer"}, "tlReward": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdActivityLimitSkinInfoReqMsg": {"fields": {}}, "CmdActivityLimitSkinInfoRspMsg": {"fields": {"cmdSkinPlayer": {"id": 1, "rule": "required", "type": "CmdSkinPlayer"}}}, "CmdActivityLimitTimeInfoReqMsg": {"fields": {}}, "CmdActivityLimitTimeInfoRspMsg": {"fields": {"tlCmdTask": {"id": 1, "rule": "repeated", "type": "CmdTask", "options": {}}, "tlLimitTimeActivity": {"id": 3, "rule": "repeated", "type": "CmdActivityGoods", "options": {}}, "tlLimitTimeDay": {"id": 2, "rule": "repeated", "type": "CmdActivityGoods", "options": {}}}}, "CmdActivityLimitTimeTakeReqMsg": {"fields": {"resId": {"id": 1, "rule": "required", "type": "sint32"}, "type": {"id": 2, "rule": "required", "type": "sint32"}}}, "CmdActivityLimitTimeTakeRspMsg": {"fields": {"tlCmdGoods": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}, "tlCmdTask": {"id": 2, "rule": "repeated", "type": "CmdTask", "options": {}}, "tlLimitTimeActivity": {"id": 4, "rule": "repeated", "type": "CmdActivityGoods", "options": {}}, "tlLimitTimeDay": {"id": 3, "rule": "repeated", "type": "CmdActivityGoods", "options": {}}}}, "CmdActivitySimple": {"fields": {"changeTime": {"id": 6, "type": "int64"}, "cmdActivityTime": {"id": 4, "rule": "required", "type": "CmdActivityTime"}, "exist": {"id": 8, "rule": "required", "type": "bool"}, "id": {"id": 1, "rule": "required", "type": "string"}, "jsonNames": {"id": 7, "rule": "repeated", "type": "string", "options": {"packed": false}}, "name": {"id": 3, "rule": "required", "type": "string"}, "tlCmdActivityTime": {"id": 5, "rule": "repeated", "type": "CmdActivityTime", "options": {}}, "type": {"id": 2, "rule": "required", "type": "int32"}}}, "CmdActivitySimple_Key": {"fields": {"id": {"id": 1, "rule": "required", "type": "string"}}}, "CmdActivityTime": {"fields": {"endTime": {"id": 2, "type": "sint64"}, "startTime": {"id": 1, "type": "sint64"}}}, "CmdActivityTypeEnum": {"values": {"limit_skin": 102, "limit_time": 101}}, "CmdActivity_Key": {"fields": {"openResId": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdAdvert": {"fields": {"nextTime": {"id": 3, "type": "sint64"}, "num": {"id": 2, "rule": "required", "type": "sint32"}, "type": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdAppletInfo": {"fields": {"extra": {"id": 3, "type": "string"}, "num": {"id": 2, "rule": "required", "type": "sint32"}, "type": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdApplyFriendCache": {"fields": {"friendDetail": {"id": 2, "rule": "required", "type": "CmdFriendsShowDetail"}, "roleId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdApplyFriendCache_Key": {"fields": {"roleId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdBattleAttr": {"fields": {"refId": {"id": 1, "rule": "required", "type": "sint32"}, "value": {"id": 2, "rule": "required", "type": "string"}}}, "CmdBattleBroMsg": {"fields": {}}, "CmdBattleBuff": {"fields": {"id": {"id": 1, "rule": "required", "type": "sint32"}, "refId": {"id": 2, "rule": "required", "type": "sint32"}}}, "CmdBattleCard": {"fields": {"id": {"id": 1, "rule": "required", "type": "sint32"}, "useNum": {"id": 2, "rule": "required", "type": "sint32"}}}, "CmdBattleChapter": {"fields": {"chapterId": {"id": 1, "rule": "required", "type": "sint32"}, "star": {"id": 3, "rule": "required", "type": "sint32"}, "takeStar": {"id": 2, "rule": "required", "type": "sint32"}, "tlCmdBattleLevel": {"id": 4, "rule": "repeated", "type": "CmdBattleLevel", "options": {}}}}, "CmdBattleChapter_Key": {"fields": {"chapterId": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdBattleCompleteInfoReqMsg": {"fields": {"level": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdBattleCompleteInfoRspMsg": {"fields": {"star3": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdBattleData": {"fields": {"advertNum": {"id": 5, "rule": "required", "type": "sint32"}, "battleId": {"id": 7, "rule": "required", "type": "string"}, "battleType": {"id": 10, "type": "CmdBattleType"}, "clientData": {"id": 9, "type": "bytes"}, "cmdBattleSkill": {"id": 15, "type": "CmdBattleSkill"}, "gemRefreshSkillNum": {"id": 11, "type": "sint32"}, "randomTimes": {"id": 6, "rule": "required", "type": "sint32"}, "spiritAnimalData": {"id": 14, "type": "CmdSpiritAnimalData"}, "times": {"id": 12, "rule": "required", "type": "sint32"}, "tlCmdBattleAttr": {"id": 4, "rule": "repeated", "type": "CmdBattleAttr", "options": {}}, "tlCmdBattleBuff": {"id": 3, "rule": "repeated", "type": "CmdBattleBuff", "options": {}}, "tlCmdBattleCard": {"id": 2, "rule": "repeated", "type": "CmdBattleCard", "options": {}}, "tlId": {"id": 8, "rule": "repeated", "type": "sint32", "options": {"packed": false}}, "tlSkillId": {"id": 1, "rule": "repeated", "type": "sint32", "options": {"packed": false}}, "tlWallSkillId": {"id": 13, "rule": "repeated", "type": "sint32", "options": {"packed": false}}}}, "CmdBattleDataUploadReqMsg": {"fields": {"battleId": {"id": 1, "rule": "required", "type": "string"}, "clientData": {"id": 2, "rule": "required", "type": "bytes"}}}, "CmdBattleDataUploadRspMsg": {"fields": {}}, "CmdBattleLevel": {"fields": {"battleTime": {"id": 3, "rule": "required", "type": "sint32"}, "level": {"id": 1, "rule": "required", "type": "sint32"}, "star": {"id": 2, "rule": "required", "type": "sint32"}}}, "CmdBattleLevel_Key": {"fields": {"level": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdBattlePreviewReqMsg": {"fields": {"level": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdBattlePreviewRspMsg": {"fields": {"tlCmdGoods": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdBattleRandomSkillReqMsg": {"fields": {"battleTime": {"id": 6, "type": "sint32"}, "cardId": {"id": 4, "type": "sint32"}, "clientData": {"id": 5, "type": "bytes"}, "hp": {"id": 3, "type": "sint32"}, "level": {"id": 1, "type": "sint32"}, "type": {"id": 2, "type": "sint32"}}}, "CmdBattleRandomSkillRspMsg": {"fields": {"cmdBattleData": {"id": 3, "type": "CmdBattleData"}, "selectId": {"id": 2, "rule": "repeated", "type": "sint32", "options": {"packed": false}}, "tlId": {"id": 1, "rule": "repeated", "type": "sint32", "options": {"packed": false}}}}, "CmdBattleResultReqMsg": {"fields": {"battleTime": {"id": 3, "type": "sint32"}, "cmdBattleType": {"id": 7, "type": "CmdBattleType"}, "killNumber": {"id": 4, "type": "sint32"}, "level": {"id": 1, "type": "sint32"}, "report": {"id": 5, "type": "bytes"}, "star": {"id": 2, "type": "sint32"}, "version": {"id": 6, "type": "sint32"}}}, "CmdBattleResultRspMsg": {"fields": {"tipType": {"id": 2, "type": "sint32"}, "tlCmdGoods": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdBattleSelectSkillReqMsg": {"fields": {"id": {"id": 2, "rule": "repeated", "type": "sint32", "options": {"packed": false}}, "level": {"id": 1, "type": "sint32"}, "type": {"id": 3, "type": "sint32"}}}, "CmdBattleSelectSkillRspMsg": {"fields": {"cmdBattleData": {"id": 1, "rule": "required", "type": "CmdBattleData"}}}, "CmdBattleSkill": {"fields": {"curRandomSkill": {"id": 4, "rule": "repeated", "type": "sint32", "options": {"packed": false}}, "curRandomSkillType": {"id": 5, "type": "sint32"}, "tlBaseSkillId": {"id": 2, "rule": "repeated", "type": "sint32", "options": {"packed": false}}, "tlBossSkillId": {"id": 3, "rule": "repeated", "type": "sint32", "options": {"packed": false}}, "tlSkillId": {"id": 1, "rule": "repeated", "type": "sint32", "options": {"packed": false}}}}, "CmdBattleStatisticsReqMsg": {"fields": {"battleId": {"id": 1, "rule": "required", "type": "string"}, "battleTime": {"id": 4, "type": "sint32"}, "cmdBattleType": {"id": 3, "type": "CmdBattleType"}, "level": {"id": 2, "type": "sint32"}, "report": {"id": 5, "type": "bytes"}, "roundIndex": {"id": 7, "type": "sint32"}, "version": {"id": 6, "type": "sint32"}}}, "CmdBattleStatisticsRspMsg": {"fields": {}}, "CmdBattleSweepReqMsg": {"fields": {"level": {"id": 2, "rule": "required", "type": "sint32"}, "type": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdBattleSweepRspMsg": {"fields": {"tlCmdGoods": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdBattleToken": {"fields": {"exp": {"id": 3, "rule": "required", "type": "sint32"}, "level": {"id": 2, "rule": "required", "type": "sint32"}, "roleId": {"id": 1, "rule": "required", "type": "sint64"}, "surplusDay": {"id": 5, "rule": "required", "type": "sint32"}, "tlBattleTokenMessage": {"id": 4, "rule": "repeated", "type": "CmdBattleTokenMessage", "options": {}}}}, "CmdBattleTokenMessage": {"fields": {"open": {"id": 2, "rule": "required", "type": "bool"}, "resId": {"id": 1, "rule": "required", "type": "sint32"}, "tlTakeLevel": {"id": 3, "rule": "repeated", "type": "sint32", "options": {"packed": false}}}}, "CmdBattleTokenOpenReqMsg": {"fields": {"resId": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdBattleTokenOpenRspMsg": {"fields": {}}, "CmdBattleTokenResetReqMsg": {"fields": {}}, "CmdBattleTokenResetRspMsg": {"fields": {}}, "CmdBattleTokenTakeRewardReqMsg": {"fields": {"level": {"id": 3, "type": "sint32"}, "resId": {"id": 2, "type": "sint32"}, "takeAll": {"id": 1, "type": "bool"}}}, "CmdBattleTokenTakeRewardRspMsg": {"fields": {"tlReward": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdBattleToken_Key": {"fields": {"roleId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdBattleType": {"values": {"CHAPTER": 1, "LOCK_MONSTER": 2, "SECRET_REALM": 3}}, "CmdBlackFriendCache": {"fields": {"friendDetail": {"id": 2, "rule": "required", "type": "CmdFriendsShowDetail"}, "roleId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdBlackFriendCache_Key": {"fields": {"roleId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdBreach": {"fields": {}}, "CmdBreachReqMsg": {"fields": {"resId": {"id": 1, "rule": "required", "type": "sint64"}, "type": {"id": 2, "rule": "required", "type": "sint32"}}}, "CmdBreachRspMsg": {"fields": {}}, "CmdBreach_Key": {"fields": {"resId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdBuySkinReqMsg": {"fields": {"resId": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdBuySkinRspMsg": {"fields": {"tlReward": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdBuyStaBuyReqMsg": {"fields": {"resId": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdBuyStaBuyRspMsg": {"fields": {}}, "CmdBuyStaInfoReqMsg": {"fields": {}}, "CmdBuyStaInfoRspMsg": {"fields": {"cmdBuyStaState": {"id": 1, "rule": "repeated", "type": "CmdBuyStaState", "options": {}}}}, "CmdBuyStaState": {"fields": {"resId": {"id": 1, "rule": "required", "type": "sint32"}, "state": {"id": 2, "rule": "required", "type": "sint32"}}}, "CmdCacheBroMsg": {"fields": {}}, "CmdCardBuyReqMsg": {"fields": {"resId": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdCardBuyRspMsg": {"fields": {"tlRewards": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdCardCache": {"fields": {"endTime": {"id": 2, "rule": "required", "type": "sint64"}, "resId": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdCardCache_Key": {"fields": {"resId": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdCastleApprentice": {"fields": {"id": {"id": 1, "rule": "required", "type": "sint32"}, "level": {"id": 2, "rule": "required", "type": "sint32"}, "state": {"id": 3, "rule": "required", "type": "bool"}}}, "CmdCastleApprenticeUpLevelReqMsg": {"fields": {"id": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdCastleApprenticeUpLevelRspMsg": {"fields": {}}, "CmdCastleApprenticeUpReqMsg": {"fields": {"id": {"id": 1, "rule": "required", "type": "sint32"}, "type": {"id": 2, "rule": "required", "type": "sint32"}}}, "CmdCastleApprenticeUpRspMsg": {"fields": {}}, "CmdCastleApprentice_Key": {"fields": {"id": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdCastleSkill": {"fields": {"level": {"id": 2, "rule": "required", "type": "sint32"}, "skillId": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdCastleSkillResearchReqMsg": {"fields": {}}, "CmdCastleSkillResearchRspMsg": {"fields": {}}, "CmdCastleSkillUpLevelReqMsg": {"fields": {"skillId": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdCastleSkillUpLevelRspMsg": {"fields": {}}, "CmdCastleSkill_Key": {"fields": {"skillId": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdCastleWall": {"fields": {"level": {"id": 3, "rule": "required", "type": "sint32"}, "selectType": {"id": 1, "rule": "required", "type": "sint32"}, "tlWallType": {"id": 2, "rule": "repeated", "type": "sint32", "options": {"packed": false}}}}, "CmdCastleWallQualityUpReqMsg": {"fields": {"wallType": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdCastleWallQualityUpRspMsg": {"fields": {}}, "CmdCastleWallSelectReqMsg": {"fields": {"type": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdCastleWallSelectRspMsg": {"fields": {}}, "CmdCastleWallSkill": {"fields": {"id": {"id": 1, "rule": "required", "type": "sint32"}, "level": {"id": 2, "rule": "required", "type": "sint32"}}}, "CmdCastleWallSkill_Key": {"fields": {"id": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdCastleWallUpLevelReqMsg": {"fields": {}}, "CmdCastleWallUpLevelRspMsg": {"fields": {}}, "CmdCastleWallUpSkillLevelReqMsg": {"fields": {"id": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdCastleWallUpSkillLevelRspMsg": {"fields": {}}, "CmdCastleWall_Key": {"fields": {}}, "CmdCastleWorkShop": {"fields": {"history": {"id": 6, "rule": "required", "type": "string"}, "id": {"id": 1, "rule": "required", "type": "sint32"}, "level": {"id": 2, "rule": "required", "type": "sint32"}, "levelEndTime": {"id": 4, "rule": "required", "type": "sint64"}, "rewardEndTime": {"id": 5, "rule": "required", "type": "sint64"}, "rewardNum": {"id": 3, "rule": "required", "type": "sint32"}}}, "CmdCastleWorkShopOpenReqMsg": {"fields": {"id": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdCastleWorkShopOpenRspMsg": {"fields": {}}, "CmdCastleWorkShopSpeedReqMsg": {"fields": {"id": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdCastleWorkShopSpeedRspMsg": {"fields": {}}, "CmdCastleWorkShopTakeReqMsg": {"fields": {"id": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdCastleWorkShopTakeRspMsg": {"fields": {"tlCmdGoods": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdCastleWorkShopUpLevelReqMsg": {"fields": {"id": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdCastleWorkShopUpLevelRspMsg": {"fields": {}}, "CmdCastleWorkShop_Key": {"fields": {"id": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdChangeUsedSkinReqMsg": {"fields": {"resId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdChangeUsedSkinRspMsg": {"fields": {}}, "CmdChapter": {"fields": {"finishState": {"id": 2, "rule": "required", "type": "sint32"}, "giftState": {"id": 5, "rule": "required", "type": "sint32"}, "liveTime": {"id": 3, "rule": "required", "type": "sint64"}, "resId": {"id": 1, "rule": "required", "type": "sint64"}, "tlChapterBoxState": {"id": 4, "rule": "repeated", "type": "CmdChapterBoxState", "options": {}}}}, "CmdChapterBoxState": {"fields": {"boxResId": {"id": 1, "rule": "required", "type": "int64"}, "rewardState": {"id": 2, "rule": "required", "type": "CmdCommonState"}}}, "CmdChapterRewardReqMsg": {"fields": {"resId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdChapterRewardRspMsg": {"fields": {"tlCmdGoods": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdChapter_Key": {"fields": {"resId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdClientAppendData": {"fields": {"tlCmdClientAppendDataModel": {"id": 1, "rule": "repeated", "type": "CmdClientAppendDataModel", "options": {}}}}, "CmdClientAppendDataModel": {"fields": {"appendData": {"id": 2, "rule": "required", "type": "bytes"}, "cmdClientAppendDataModelEnum": {"id": 1, "rule": "required", "type": "CmdClientAppendDataModelEnum"}}}, "CmdClientAppendDataModelEnum": {"values": {"CmdGuideList": 1}}, "CmdClientInfo": {"fields": {"appId": {"id": 15, "type": "string"}, "appType": {"id": 14, "type": "string"}, "bigChannel": {"id": 11, "rule": "required", "type": "string"}, "idfa": {"id": 3, "rule": "required", "type": "string"}, "imei": {"id": 1, "rule": "required", "type": "string"}, "mac": {"id": 2, "rule": "required", "type": "string"}, "mobileBrand": {"id": 4, "rule": "required", "type": "string"}, "mobileModel": {"id": 5, "rule": "required", "type": "string"}, "networkType": {"id": 10, "rule": "required", "type": "string"}, "smallChannel": {"id": 12, "rule": "required", "type": "string"}, "smallChannelPack": {"id": 13, "rule": "required", "type": "string"}, "systemName": {"id": 8, "rule": "required", "type": "string"}, "systemVersion": {"id": 9, "rule": "required", "type": "string"}, "versionCode": {"id": 7, "rule": "required", "type": "string"}, "versionName": {"id": 6, "rule": "required", "type": "string"}}}, "CmdCode": {"fields": {}}, "CmdCodeExchangeReqMsg": {"fields": {"code": {"id": 1, "rule": "required", "type": "string"}}}, "CmdCodeExchangeRspMsg": {"fields": {"tlReward": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdCode_Key": {"fields": {"id": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdCommonState": {"values": {"FINISH": 1, "REWARDED": 2, "UNDONE": 0}}, "CmdCommonTips": {"fields": {"content": {"id": 1, "rule": "required", "type": "CmdI18N"}}}, "CmdContinueNextBattleReqMsg": {"fields": {"battleType": {"id": 1, "rule": "required", "type": "CmdBattleType"}, "continue": {"id": 2, "type": "bool"}}}, "CmdContinueNextBattleRspMsg": {"fields": {"cmdBattleData": {"id": 1, "type": "CmdBattleData"}}}, "CmdContinuityGiftFinishRewardReqMsg": {"fields": {"days": {"id": 1, "rule": "required", "type": "int32"}}}, "CmdContinuityGiftFinishRewardRspMsg": {"fields": {"tlCmdGoods": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdContinuityGiftInfo": {"fields": {"days": {"id": 1, "rule": "required", "type": "int32"}, "status": {"id": 2, "rule": "required", "type": "int32"}}}, "CmdContinuityGiftInfoReqMsg": {"fields": {}}, "CmdContinuityGiftInfoRspMsg": {"fields": {"accumulativeAward": {"id": 3, "rule": "repeated", "type": "int32", "options": {"packed": false}}, "endTime": {"id": 2, "rule": "required", "type": "int64"}, "tlCmdContinuityGiftInfo": {"id": 1, "rule": "repeated", "type": "CmdContinuityGiftInfo", "options": {}}}}, "CmdContinuityGiftRewardReqMsg": {"fields": {"days": {"id": 1, "rule": "required", "type": "int32"}}}, "CmdContinuityGiftRewardRspMsg": {"fields": {"tlCmdContinuityGiftInfo": {"id": 1, "rule": "repeated", "type": "CmdContinuityGiftInfo", "options": {}}, "tlCmdGoods": {"id": 2, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdDayChallengeActDetail": {"fields": {"tlDayChallengeTask": {"id": 1, "rule": "repeated", "type": "CmdDayChallengeTask", "options": {}}}}, "CmdDayChallengeTask": {"fields": {"process": {"id": 2, "rule": "required", "type": "sint32"}, "resId": {"id": 1, "rule": "required", "type": "sint32"}, "state": {"id": 3, "rule": "required", "type": "CmdCommonState"}}}, "CmdDayWeekMonthGift": {"fields": {"rechargeDay": {"id": 5, "rule": "required", "type": "sint32"}, "tlDayGift": {"id": 1, "rule": "repeated", "type": "EveryDayGift", "options": {}}, "tlMonthGift": {"id": 3, "rule": "repeated", "type": "EveryDayGift", "options": {}}, "tlRechargeCount": {"id": 4, "rule": "repeated", "type": "RechargeCount", "options": {}}, "tlWeekGift": {"id": 2, "rule": "repeated", "type": "EveryDayGift", "options": {}}}}, "CmdDayWeekMonthGiftFreeBuyReqMsg": {"fields": {"id": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdDayWeekMonthGiftFreeBuyRspMsg": {"fields": {"tlCmdGoods": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdDayWeekMonthGiftGetRewardReqMsg": {"fields": {"id": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdDayWeekMonthGiftGetRewardRspMsg": {"fields": {"tlCmdGoods": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdDayWeekMonthGiftInfoReqMsg": {"fields": {}}, "CmdDayWeekMonthGiftInfoRspMsg": {"fields": {"cmdDayWeekMonthGift": {"id": 1, "rule": "required", "type": "CmdDayWeekMonthGift"}}}, "CmdDayWeekMonthGift_Key": {"fields": {"id": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdEndBroMsg": {"fields": {"end": {"id": 1, "rule": "required", "type": "sint32"}, "nextTime": {"id": 2, "rule": "required", "type": "sint64"}}}, "CmdEndBuyInfoReqMsg": {"fields": {}}, "CmdEndBuyInfoRspMsg": {"fields": {"tlEndBuyInfo": {"id": 1, "rule": "repeated", "type": "CmdRoleEndBuyInfo", "options": {}}}}, "CmdEndBuyReqMsg": {"fields": {"buyId": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdEndBuyRspMsg": {"fields": {}}, "CmdEnterBattleReqMsg": {"fields": {"battleType": {"id": 1, "rule": "required", "type": "CmdBattleType"}, "level": {"id": 2, "type": "sint32"}}}, "CmdEnterBattleRspMsg": {"fields": {"cmdBattleData": {"id": 1, "rule": "required", "type": "CmdBattleData"}}}, "CmdEquip": {"fields": {"equipId": {"id": 1, "rule": "required", "type": "sint32"}, "level": {"id": 2, "rule": "required", "type": "sint32"}, "tlCmdGemInfo": {"id": 3, "rule": "repeated", "type": "CmdGemInfo", "options": {}}}}, "CmdEquipChangePageNameReqMsg": {"fields": {"name": {"id": 2, "rule": "required", "type": "string"}, "page": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdEquipChangePageNameRspMsg": {"fields": {}}, "CmdEquipPage": {"fields": {"name": {"id": 2, "rule": "required", "type": "string"}, "page": {"id": 1, "rule": "required", "type": "sint32"}, "tlCmdEquip": {"id": 3, "rule": "repeated", "type": "CmdEquip", "options": {}}}}, "CmdEquipPage_Key": {"fields": {"page": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdEquipSelectPageReqMsg": {"fields": {"page": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdEquipSelectPageRspMsg": {"fields": {}}, "CmdEquipUpLevelAllReqMsg": {"fields": {}}, "CmdEquipUpLevelAllRspMsg": {"fields": {}}, "CmdEquipUpLevelReqMsg": {"fields": {"id": {"id": 1, "rule": "required", "type": "sint32"}, "type": {"id": 2, "type": "sint32"}}}, "CmdEquipUpLevelRspMsg": {"fields": {}}, "CmdEveryDayGetRewardsReqMsg": {"fields": {"day": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdEveryDayGetRewardsRspMsg": {"fields": {"tlCmdGoods": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdEveryDaySign": {"fields": {}}, "CmdEveryDaySignInInfoReqMsg": {"fields": {}}, "CmdEveryDaySignInInfoRspMsg": {"fields": {"days": {"id": 2, "rule": "required", "type": "sint32"}, "tlEveryDaySignInReward": {"id": 1, "rule": "repeated", "type": "EveryDaySignInReward", "options": {}}}}, "CmdEveryDaySignInReqMsg": {"fields": {}}, "CmdEveryDaySignInRspMsg": {"fields": {"tlCmdGoods": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdEveryDaySign_Key": {"fields": {"id": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdFirstRecharge": {"fields": {}}, "CmdFirstRechargeInfo": {"fields": {"id": {"id": 1, "rule": "required", "type": "sint32"}, "lastTakeTime": {"id": 3, "type": "sint64"}, "rewardIds": {"id": 2, "rule": "repeated", "type": "sint64", "options": {"packed": false}}}}, "CmdFirstRechargeOpenReqMsg": {"fields": {}}, "CmdFirstRechargeOpenRspMsg": {"fields": {}}, "CmdFirstRechargeRewardReqMsg": {"fields": {"resId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdFirstRechargeRewardRspMsg": {"fields": {"tlCmdGoods": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdFirstRecharge_Key": {"fields": {"id": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdFriendAgreeOneKeyReqMsg": {"fields": {}}, "CmdFriendAgreeOneKeyRspMsg": {"fields": {}}, "CmdFriendBlackRemoveReqMsg": {"fields": {"targetId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdFriendBlackRemoveRspMsg": {"fields": {}}, "CmdFriendCache": {"fields": {"friendDetail": {"id": 2, "rule": "required", "type": "CmdFriendsShowDetail"}, "roleId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdFriendCache_Key": {"fields": {"roleId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdFriendDetailReqMsg": {"fields": {"targetId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdFriendDetailRspMsg": {"fields": {"cmdFriendsShowDetail": {"id": 1, "rule": "required", "type": "CmdFriendsShowDetail"}, "equipList": {"id": 2, "rule": "repeated", "type": "CmdFriendsWearEquip", "options": {}}}}, "CmdFriendGetEndOneKeyReqMsg": {"fields": {}}, "CmdFriendGetEndOneKeyRspMsg": {"fields": {}}, "CmdFriendGetEndReqMsg": {"fields": {"targetId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdFriendGetEndRspMsg": {"fields": {}}, "CmdFriendLikeReqMsg": {"fields": {"targetId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdFriendLikeRspMsg": {"fields": {}}, "CmdFriendRefreshSuggestReqMsg": {"fields": {}}, "CmdFriendRefreshSuggestRspMsg": {"fields": {"tlSuggestFriend": {"id": 1, "rule": "repeated", "type": "CmdFriendsShowDetail", "options": {}}}}, "CmdFriendRefuseOneKeyReqMsg": {"fields": {}}, "CmdFriendRefuseOneKeyRspMsg": {"fields": {}}, "CmdFriendSendEndOneKeyReqMsg": {"fields": {}}, "CmdFriendSendEndOneKeyRspMsg": {"fields": {}}, "CmdFriendSendEndReqMsg": {"fields": {"targetId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdFriendSendEndRspMsg": {"fields": {}}, "CmdFriendsAddReqMsg": {"fields": {"targetId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdFriendsAddRspMsg": {"fields": {}}, "CmdFriendsApplyReqMsg": {"fields": {"targetId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdFriendsApplyRspMsg": {"fields": {}}, "CmdFriendsBlackReqMsg": {"fields": {"targetId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdFriendsBlackRspMsg": {"fields": {}}, "CmdFriendsRefuseReqMsg": {"fields": {"targetId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdFriendsRefuseRspMsg": {"fields": {}}, "CmdFriendsRemoveReqMsg": {"fields": {"targetId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdFriendsRemoveRspMsg": {"fields": {}}, "CmdFriendsSearchReqMsg": {"fields": {"targetId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdFriendsSearchRspMsg": {"fields": {"detail": {"id": 1, "rule": "required", "type": "CmdFriendsShowDetail"}}}, "CmdFriendsShowDetail": {"fields": {"apply": {"id": 15, "type": "bool"}, "headFrame": {"id": 5, "rule": "required", "type": "sint64"}, "heroId": {"id": 13, "type": "sint64"}, "lastOfflineTime": {"id": 8, "type": "sint64"}, "level": {"id": 12, "type": "sint64"}, "online": {"id": 6, "type": "bool"}, "popularity": {"id": 7, "type": "sint64"}, "power": {"id": 4, "rule": "required", "type": "sint64"}, "roleId": {"id": 1, "rule": "required", "type": "sint64"}, "roleImg": {"id": 2, "rule": "required", "type": "sint64"}, "roleName": {"id": 3, "rule": "required", "type": "string"}, "todayLike": {"id": 14, "type": "bool"}, "todayReceive": {"id": 10, "type": "bool"}, "todaySend": {"id": 9, "type": "bool"}, "todayTake": {"id": 11, "type": "bool"}}}, "CmdFriendsWearEquip": {"fields": {"level": {"id": 4, "rule": "required", "type": "sint64"}, "phase": {"id": 3, "rule": "required", "type": "sint64"}, "quality": {"id": 2, "rule": "required", "type": "sint64"}, "resId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdFuBen": {"fields": {"detail": {"id": 2, "rule": "required", "type": "CmdFuBenDetail"}, "resId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdFuBenBuyTimesReqMsg": {"fields": {"resId": {"id": 2, "rule": "required", "type": "sint64"}, "type": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdFuBenBuyTimesRspMsg": {"fields": {}}, "CmdFuBenDailyDetail": {"fields": {"buyTime": {"id": 4, "rule": "required", "type": "sint32"}, "cmdFuBenRewardState": {"id": 7, "rule": "repeated", "type": "CmdFuBenRewardState", "options": {}}, "enterCount": {"id": 6, "rule": "required", "type": "sint32"}, "freeTime": {"id": 5, "rule": "required", "type": "sint32"}, "level": {"id": 3, "rule": "required", "type": "sint32"}, "resId": {"id": 1, "rule": "required", "type": "sint64"}, "type": {"id": 2, "rule": "required", "type": "sint32"}}}, "CmdFuBenDailyGetRewardReqMsg": {"fields": {"resId": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdFuBenDailyGetRewardRspMsg": {"fields": {"tlCmdGoods": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdFuBenDetail": {"fields": {"cmdFuBenDailyDetail": {"id": 2, "type": "CmdFuBenDailyDetail"}, "cmdFuBenGoldDetail": {"id": 1, "type": "CmdFuBenGoldDetail"}}}, "CmdFuBenEnterReqMsg": {"fields": {"resId": {"id": 2, "rule": "required", "type": "sint64"}, "type": {"id": 1, "rule": "required", "type": "CmdFuBenType"}}}, "CmdFuBenEnterRspMsg": {"fields": {}}, "CmdFuBenGoldDetail": {"fields": {"buyTime": {"id": 4, "rule": "required", "type": "sint32"}, "enterCount": {"id": 6, "rule": "required", "type": "sint32"}, "freeTime": {"id": 5, "rule": "required", "type": "sint32"}, "level": {"id": 3, "rule": "required", "type": "sint32"}, "resId": {"id": 1, "rule": "required", "type": "sint64"}, "type": {"id": 2, "rule": "required", "type": "sint32"}}}, "CmdFuBenRewardState": {"fields": {"resId": {"id": 1, "rule": "required", "type": "sint32"}, "state": {"id": 2, "rule": "required", "type": "sint32"}}}, "CmdFuBenType": {"values": {"CHAPTER": 1, "DAILY": 3, "MONEY": 2}}, "CmdFuBen_Key": {"fields": {"resId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdFund": {"fields": {}}, "CmdFundDetail": {"fields": {"buyState": {"id": 2, "rule": "required", "type": "sint32"}, "cmdFundRewardState": {"id": 3, "rule": "repeated", "type": "CmdFundRewardState", "options": {}}, "resId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdFundGetInfoReqMsg": {"fields": {}}, "CmdFundGetInfoRspMsg": {"fields": {"cmdFundDetail": {"id": 1, "rule": "repeated", "type": "CmdFundDetail", "options": {}}}}, "CmdFundGetRewardReqMsg": {"fields": {"resId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdFundGetRewardRspMsg": {"fields": {"tlCmdGoods": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdFundRewardState": {"fields": {"resId": {"id": 1, "rule": "required", "type": "sint64"}, "state": {"id": 2, "rule": "required", "type": "sint32"}}}, "CmdFund_Key": {"fields": {"id": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdGameCircle": {"fields": {"likeRewardState": {"id": 1, "rule": "required", "type": "CmdCommonState"}}}, "CmdGameCircle_Key": {"fields": {}}, "CmdGameExtend": {"fields": {"clazzName": {"id": 1, "rule": "required", "type": "string"}, "message": {"id": 2, "rule": "required", "type": "bytes"}}}, "CmdGem": {"fields": {"attribute": {"id": 5, "rule": "required", "type": "sint32"}, "id": {"id": 1, "rule": "required", "type": "sint64"}, "lock": {"id": 6, "rule": "required", "type": "sint32"}, "newAttribute": {"id": 7, "type": "sint32"}, "num": {"id": 4, "rule": "required", "type": "sint32"}, "quality": {"id": 3, "rule": "required", "type": "sint32"}, "resId": {"id": 2, "rule": "required", "type": "sint64"}}}, "CmdGemComposePreviewReqMsg": {"fields": {}}, "CmdGemComposePreviewRspMsg": {"fields": {"tlCmdGemPreview": {"id": 1, "rule": "repeated", "type": "CmdGemPreview", "options": {}}}}, "CmdGemComposeReqMsg": {"fields": {}}, "CmdGemComposeRspMsg": {"fields": {"tlCmdGoods": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdGemDownReqMsg": {"fields": {"equipId": {"id": 1, "rule": "required", "type": "sint32"}, "order": {"id": 3, "rule": "required", "type": "sint32"}, "page": {"id": 2, "rule": "required", "type": "sint32"}}}, "CmdGemDownRspMsg": {"fields": {}}, "CmdGemInfo": {"fields": {"gemId": {"id": 2, "rule": "required", "type": "sint64"}, "order": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdGemLockReqMsg": {"fields": {"id": {"id": 1, "rule": "required", "type": "sint64"}, "lock": {"id": 2, "type": "sint32"}}}, "CmdGemLockRspMsg": {"fields": {}}, "CmdGemOnReqMsg": {"fields": {"equipId": {"id": 1, "rule": "required", "type": "sint32"}, "id": {"id": 3, "rule": "required", "type": "sint64"}, "page": {"id": 2, "rule": "required", "type": "sint32"}}}, "CmdGemOnRspMsg": {"fields": {}}, "CmdGemPreview": {"fields": {"id": {"id": 1, "rule": "required", "type": "sint64"}, "num": {"id": 2, "rule": "required", "type": "sint32"}}}, "CmdGemRefineReqMsg": {"fields": {"id": {"id": 1, "rule": "required", "type": "sint64"}, "type": {"id": 2, "type": "sint64"}}}, "CmdGemRefineRspMsg": {"fields": {}}, "CmdGemRefineSaveReqMsg": {"fields": {"id": {"id": 1, "rule": "required", "type": "sint64"}, "type": {"id": 2, "rule": "required", "type": "sint64"}}}, "CmdGemRefineSaveRspMsg": {"fields": {"id": {"id": 1, "type": "sint64"}}}, "CmdGemRefreshSkillReqMsg": {"fields": {}}, "CmdGemRefreshSkillRspMsg": {"fields": {"tlId": {"id": 1, "rule": "repeated", "type": "sint32", "options": {"packed": false}}}}, "CmdGemReplaceReqMsg": {"fields": {"equipId": {"id": 1, "rule": "required", "type": "sint32"}, "id": {"id": 3, "rule": "required", "type": "sint64"}, "order": {"id": 4, "rule": "required", "type": "sint32"}, "page": {"id": 2, "rule": "required", "type": "sint32"}}}, "CmdGemReplaceRspMsg": {"fields": {}}, "CmdGem_Key": {"fields": {"id": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdGetAllPlayClientAniReqMsg": {"fields": {}}, "CmdGetAllPlayClientAniRspMsg": {"fields": {"clientAniRefId": {"id": 1, "rule": "repeated", "type": "int32", "options": {"packed": false}}}}, "CmdGetHallRoleListReqMsg": {"fields": {}}, "CmdGetHallRoleListRspMsg": {"fields": {"tlHallRole": {"id": 1, "rule": "repeated", "type": "CmdHallRole", "options": {}}}}, "CmdGetRankReqMsg": {"fields": {"rankType": {"id": 1, "rule": "required", "type": "CmdRankType"}, "type": {"id": 2, "type": "sint32"}}}, "CmdGetRankRspMsg": {"fields": {"rankInfo": {"id": 1, "type": "CmdRankData"}}}, "CmdGmInfo": {"fields": {"cmd": {"id": 1, "rule": "required", "type": "string"}, "desc": {"id": 2, "type": "string"}}}, "CmdGmListReqMsg": {"fields": {}}, "CmdGmListRspMsg": {"fields": {"cmd": {"id": 1, "rule": "repeated", "type": "CmdGmInfo", "options": {}}}}, "CmdGoodReplace": {"fields": {"newResId": {"id": 2, "rule": "required", "type": "int64"}, "number": {"id": 3, "rule": "required", "type": "int32"}, "oldResId": {"id": 1, "rule": "required", "type": "int64"}}}, "CmdGoods": {"fields": {"number": {"id": 2, "rule": "required", "type": "string"}, "quality": {"id": 3, "type": "sint32"}, "resId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdGoodsList": {"fields": {"tlCmdGoods": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdGoodsRedDot": {"fields": {"id": {"id": 2, "type": "int64"}, "resId": {"id": 1, "rule": "required", "type": "int64"}}}, "CmdGoodsRedDotDispelReqMsg": {"fields": {"dispelParam": {"id": 1, "rule": "repeated", "type": "CmdGoodsRedDot", "options": {}}}}, "CmdGoodsRedDotDispelRspMsg": {"fields": {}}, "CmdGuanKaGift": {"fields": {"endTime": {"id": 2, "rule": "required", "type": "int64"}, "receive": {"id": 4, "type": "sint32"}, "resId": {"id": 1, "rule": "required", "type": "sint32"}, "selectId": {"id": 3, "type": "sint64"}}}, "CmdGuanKaGiftBroMsg": {"fields": {"cmdGuanKaGift": {"id": 1, "rule": "required", "type": "CmdGuanKaGift"}}}, "CmdGuanKaGift_Key": {"fields": {"resId": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdGuide": {"fields": {}}, "CmdGuideDetail": {"fields": {"extend": {"id": 3, "type": "string"}, "resId": {"id": 1, "rule": "required", "type": "sint32"}, "type": {"id": 2, "rule": "required", "type": "sint32"}}}, "CmdGuideReadReqMsg": {"fields": {}}, "CmdGuideReadRspMsg": {"fields": {"cmdGuideDetail": {"id": 1, "rule": "repeated", "type": "CmdGuideDetail", "options": {}}}}, "CmdGuideRecordReqMsg": {"fields": {"extend": {"id": 3, "type": "string"}, "resId": {"id": 1, "rule": "required", "type": "sint32"}, "type": {"id": 2, "rule": "required", "type": "sint32"}}}, "CmdGuideRecordRspMsg": {"fields": {}}, "CmdGuide_Key": {"fields": {"id": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdHallRole": {"fields": {"point": {"id": 3, "type": "CmdPoint"}, "roleId": {"id": 1, "rule": "required", "type": "sint64"}, "roleName": {"id": 2, "type": "string"}}}, "CmdHang": {"fields": {}}, "CmdHangGetRewardReqMsg": {"fields": {}}, "CmdHangGetRewardRspMsg": {"fields": {"tlCmdGoods": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdHangQuickReqMsg": {"fields": {}}, "CmdHangQuickRspMsg": {"fields": {"tlCmdGoods": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdHangTimeReqMsg": {"fields": {}}, "CmdHangTimeRspMsg": {"fields": {"time": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdHang_Key": {"fields": {"resId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdHero": {"fields": {"equipId": {"id": 4, "type": "sint64"}, "level": {"id": 2, "rule": "required", "type": "sint64"}, "resId": {"id": 1, "rule": "required", "type": "sint64"}, "state": {"id": 3, "rule": "required", "type": "sint32"}}}, "CmdHeroChangeReqMsg": {"fields": {"resId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdHeroChangeRspMsg": {"fields": {}}, "CmdHeroLevelUpOneKeyReqMsg": {"fields": {"resId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdHeroLevelUpOneKeyRspMsg": {"fields": {}}, "CmdHeroLevelUpReqMsg": {"fields": {"resId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdHeroLevelUpRspMsg": {"fields": {}}, "CmdHeroOpenReqMsg": {"fields": {}}, "CmdHeroOpenRspMsg": {"fields": {}}, "CmdHeroUnlockGetInfoReqMsg": {"fields": {}}, "CmdHeroUnlockGetInfoRspMsg": {"fields": {"cmdHeroUnlockInfo": {"id": 1, "rule": "repeated", "type": "CmdHeroUnlockInfo", "options": {}}}}, "CmdHeroUnlockInfo": {"fields": {"count": {"id": 2, "rule": "required", "type": "sint32"}, "resId": {"id": 1, "rule": "required", "type": "sint64"}, "rewardState": {"id": 3, "rule": "required", "type": "sint32"}}}, "CmdHeroUnlockRewardReqMsg": {"fields": {"resId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdHeroUnlockRewardRspMsg": {"fields": {}}, "CmdHero_Key": {"fields": {"resId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdI18N": {"fields": {"cmdI18NRef": {"id": 2, "type": "string"}, "template": {"id": 1, "type": "string"}, "tlParam": {"id": 3, "rule": "repeated", "type": "CmdI18N", "options": {}}}}, "CmdItem": {"fields": {"isNew": {"id": 3, "rule": "required", "type": "bool"}, "number": {"id": 2, "rule": "required", "type": "sint32"}, "resId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdItemBoxUseReqMsg": {"fields": {"cmdGoodsList": {"id": 3, "type": "CmdGoodsList"}, "number": {"id": 2, "rule": "required", "type": "sint32"}, "resId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdItemBoxUseRspMsg": {"fields": {"tlCmdGoods": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdItemUseReqMsg": {"fields": {"number": {"id": 2, "rule": "required", "type": "sint32"}, "resId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdItemUseRspMsg": {"fields": {"tlCmdGoods": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdItem_Key": {"fields": {"resId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdLockMonster": {"fields": {"level": {"id": 1, "rule": "required", "type": "sint32"}, "nextBoxRefreshTime": {"id": 2, "rule": "required", "type": "sint64"}}}, "CmdLockMonster_Key": {"fields": {}}, "CmdMail": {"fields": {"cmdGoodsList": {"id": 4, "type": "CmdGoodsList"}, "content": {"id": 3, "rule": "required", "type": "string"}, "id": {"id": 1, "rule": "required", "type": "sint64"}, "read": {"id": 5, "rule": "required", "type": "bool"}, "readTime": {"id": 8, "rule": "required", "type": "int64"}, "sendTime": {"id": 7, "rule": "required", "type": "int64"}, "take": {"id": 6, "rule": "required", "type": "bool"}, "title": {"id": 2, "rule": "required", "type": "string"}}}, "CmdMailDeleteReqMsg": {"fields": {"id": {"id": 1, "rule": "repeated", "type": "sint64", "options": {"packed": false}}}}, "CmdMailDeleteRspMsg": {"fields": {}}, "CmdMailListReqMsg": {"fields": {}}, "CmdMailListRspMsg": {"fields": {"tlCmdMailSimple": {"id": 1, "rule": "repeated", "type": "CmdMailSimple", "options": {}}}}, "CmdMailReadReqMsg": {"fields": {"id": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdMailReadRspMsg": {"fields": {"cmdMail": {"id": 1, "rule": "required", "type": "CmdMail"}}}, "CmdMailSimple": {"fields": {"id": {"id": 1, "rule": "required", "type": "sint64"}, "read": {"id": 3, "rule": "required", "type": "bool"}, "sendTime": {"id": 5, "rule": "required", "type": "int64"}, "take": {"id": 4, "rule": "required", "type": "bool"}, "title": {"id": 2, "rule": "required", "type": "string"}}}, "CmdMailTakeRewardReqMsg": {"fields": {"id": {"id": 1, "rule": "repeated", "type": "sint64", "options": {"packed": false}}}}, "CmdMailTakeRewardRspMsg": {"fields": {"tlReward": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdMall": {"fields": {"detail": {"id": 2, "type": "CmdMallDetail"}, "id": {"id": 1, "type": "sint64"}}}, "CmdMallBroMsg": {"fields": {"cmdMall": {"id": 2, "rule": "required", "type": "CmdMall"}, "mallId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdMallChapterGiftDetail": {"fields": {"resId": {"id": 1, "type": "sint64"}, "state": {"id": 2, "type": "sint32"}}}, "CmdMallChargeDetail": {"fields": {"resId": {"id": 1, "type": "sint64"}, "state": {"id": 2, "type": "sint32"}}}, "CmdMallCoinBuyReqMsg": {"fields": {"mallId": {"id": 1, "rule": "required", "type": "sint64"}, "resId": {"id": 2, "rule": "required", "type": "sint64"}, "type": {"id": 3, "rule": "required", "type": "sint32"}}}, "CmdMallCoinBuyRspMsg": {"fields": {}}, "CmdMallCoinDetail": {"fields": {"buyCount": {"id": 2, "type": "sint32"}, "freeTime": {"id": 3, "type": "sint64"}, "resId": {"id": 1, "type": "sint64"}}}, "CmdMallDailyShopBuyReqMsg": {"fields": {"isAd": {"id": 3, "rule": "required", "type": "sint32"}, "mallId": {"id": 1, "rule": "required", "type": "sint64"}, "planId": {"id": 2, "rule": "required", "type": "sint64"}}}, "CmdMallDailyShopBuyRspMsg": {"fields": {"tlCmdGoods": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdMallDailyShopDetail": {"fields": {"adTime": {"id": 4, "type": "sint64"}, "buyCount": {"id": 3, "type": "sint32"}, "discount": {"id": 6, "type": "string"}, "finalNumber": {"id": 7, "type": "string"}, "original": {"id": 5, "type": "string"}, "resId": {"id": 1, "type": "sint64"}, "rewardId": {"id": 2, "type": "sint64"}}}, "CmdMallDetail": {"fields": {"cmdMallChapterGiftDetail": {"id": 4, "rule": "repeated", "type": "CmdMallChapterGiftDetail", "options": {}}, "cmdMallChargeDetail": {"id": 5, "rule": "repeated", "type": "CmdMallChargeDetail", "options": {}}, "cmdMallCoinDetail": {"id": 6, "rule": "repeated", "type": "CmdMallCoinDetail", "options": {}}, "cmdMallDailyShopDetail": {"id": 3, "rule": "repeated", "type": "CmdMallDailyShopDetail", "options": {}}, "cmdMallLuckDrawDetail": {"id": 2, "rule": "repeated", "type": "CmdMallLuckDrawDetail", "options": {}}, "cmdMallSEquipDetail": {"id": 1, "rule": "repeated", "type": "CmdMallSEquipDetail", "options": {}}}}, "CmdMallGetDataReqMsg": {"fields": {}}, "CmdMallGetDataRspMsg": {"fields": {"cmdMall": {"id": 1, "rule": "repeated", "type": "CmdMall", "options": {}}}}, "CmdMallLuckDrawDetail": {"fields": {"bigPrizeCount": {"id": 3, "type": "sint32"}, "buyCount": {"id": 2, "type": "sint32"}, "freeTime": {"id": 4, "type": "sint64"}, "resId": {"id": 1, "type": "sint64"}}}, "CmdMallLuckDrawReqMsg": {"fields": {"mallId": {"id": 1, "rule": "required", "type": "sint64"}, "resId": {"id": 2, "rule": "required", "type": "sint64"}, "type": {"id": 3, "rule": "required", "type": "sint32"}}}, "CmdMallLuckDrawRspMsg": {"fields": {"tlCmdGoods": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdMallSEquipDetail": {"fields": {"bigPrizeCount": {"id": 3, "type": "sint32"}, "buyCount": {"id": 2, "type": "sint32"}, "resId": {"id": 1, "type": "sint64"}, "ultimatePrizeCount": {"id": 4, "type": "sint32"}}}, "CmdMallSEquipDrawReqMsg": {"fields": {"count": {"id": 4, "rule": "required", "type": "bool"}, "mallId": {"id": 1, "rule": "required", "type": "sint64"}, "resId": {"id": 2, "rule": "required", "type": "sint64"}, "type": {"id": 3, "rule": "required", "type": "sint32"}}}, "CmdMallSEquipDrawRspMsg": {"fields": {"tlCmdGoods": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdMall_Key": {"fields": {"id": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdMiningBuyReqMsg": {"fields": {}}, "CmdMiningBuyRspMsg": {"fields": {"tlReward": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdModifyItemBroMsg": {"fields": {"tlCmdGoods": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdMonthCardActDetail": {"fields": {"cardEndTime": {"id": 1, "rule": "required", "type": "sint64"}, "todayTake": {"id": 2, "type": "bool"}}}, "CmdOpenCache": {"fields": {"refId": {"id": 1, "rule": "required", "type": "sint32"}, "show": {"id": 2, "rule": "required", "type": "bool"}}}, "CmdOpenCache_Key": {"fields": {"refId": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdPoint": {"fields": {"x": {"id": 1, "rule": "required", "type": "sint32"}, "y": {"id": 2, "rule": "required", "type": "sint32"}}}, "CmdRankData": {"fields": {"myRank": {"id": 2, "rule": "required", "type": "CmdRankRole"}, "rankList": {"id": 1, "rule": "repeated", "type": "CmdRankRole", "options": {}}, "region": {"id": 3, "type": "string"}}}, "CmdRankItem": {"fields": {"endTime": {"id": 3, "type": "sint32"}, "showEndTime": {"id": 4, "type": "sint32"}, "startTime": {"id": 2, "type": "sint32"}, "type": {"id": 1, "rule": "required", "type": "CmdRankType"}}}, "CmdRankItem_Key": {"fields": {"type": {"id": 1, "rule": "required", "type": "CmdRankType"}}}, "CmdRankRole": {"fields": {"headFrame": {"id": 6, "type": "sint64"}, "platFormImg": {"id": 5, "type": "string"}, "rank": {"id": 1, "rule": "required", "type": "sint32"}, "roleId": {"id": 2, "rule": "required", "type": "sint64"}, "roleImg": {"id": 4, "type": "sint64"}, "roleName": {"id": 3, "type": "string"}, "selectServerId": {"id": 9, "type": "sint64"}, "serverId": {"id": 8, "type": "sint64"}, "value": {"id": 7, "type": "sint64"}}}, "CmdRankType": {"values": {"CHAPTER_FULL_RANK": 5, "CHAPTER_REGION_RANK": 6, "CHAPTER_SERVER_RANK": 7, "CHAPTER_SERVER_REWARD_RANK": 10, "LOCK_MONSTER_FULL_RANK": 1, "LOCK_MONSTER_REGION_RANK": 2, "LOCK_MONSTER_SERVER_RANK": 8, "SECRET_REALM_FULL_RANK": 3, "SECRET_REALM_REGION_RANK": 4, "SECRET_REALM_SERVER_RANK": 9}}, "CmdRecharge": {"fields": {"id": {"id": 1, "rule": "required", "type": "sint64"}, "rewards": {"id": 2, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdRechargeBroMsg": {"fields": {"cmdRecharge": {"id": 1, "rule": "required", "type": "CmdRecharge"}}}, "CmdRechargeGiftAct2Detail": {"fields": {"curProcess": {"id": 1, "rule": "required", "type": "sint32"}, "tlTakeProcess": {"id": 2, "rule": "repeated", "type": "sint32", "options": {"packed": false}}}}, "CmdRechargeGiftAct3Detail": {"fields": {"curProcess": {"id": 1, "rule": "required", "type": "sint32"}, "tlTakeProcess": {"id": 2, "rule": "repeated", "type": "sint32", "options": {"packed": false}}}}, "CmdRechargeGiftActDetail": {"fields": {"curRechargeProcess": {"id": 1, "rule": "required", "type": "sint32"}, "tlTakeRechargeProcess": {"id": 2, "rule": "repeated", "type": "sint32", "options": {"packed": false}}}}, "CmdRecordClientAniReqMsg": {"fields": {"clientAniRefId": {"id": 1, "rule": "required", "type": "int32"}}}, "CmdRecordClientAniRspMsg": {"fields": {}}, "CmdRedDotCache": {"fields": {"nextRefreshTime": {"id": 3, "type": "sint64"}, "number": {"id": 2, "rule": "required", "type": "sint32"}, "redDotType": {"id": 1, "rule": "required", "type": "CmdRedDotType"}}}, "CmdRedDotCache_Key": {"fields": {"redDotType": {"id": 1, "rule": "required", "type": "CmdRedDotType"}}}, "CmdRedDotType": {"values": {"GEM_COMPOSE": 4, "MAil": 2, "SEVEN_DAY": 5, "SPIRIT_ANIMAL_BOX": 8, "TEST": 1, "continuity_days": 3, "limit_time_activity": 6, "signin": 7}}, "CmdRole": {"fields": {"battleSpiritAnimalUniqueId": {"id": 29, "type": "sint64"}, "cmdFirstRechargeInfo": {"id": 18, "rule": "required", "type": "CmdFirstRechargeInfo"}, "coin": {"id": 9, "rule": "required", "type": "string"}, "createTime": {"id": 12, "rule": "required", "type": "sint64"}, "end": {"id": 11, "rule": "required", "type": "sint32"}, "endMax": {"id": 23, "rule": "required", "type": "sint32"}, "exp": {"id": 6, "rule": "required", "type": "sint32"}, "freeRenameCount": {"id": 21, "rule": "required", "type": "sint32"}, "gem": {"id": 8, "rule": "required", "type": "string"}, "gold": {"id": 7, "rule": "required", "type": "string"}, "headFrame": {"id": 4, "rule": "required", "type": "sint64"}, "id": {"id": 1, "rule": "required", "type": "sint64"}, "img": {"id": 3, "rule": "required", "type": "sint64"}, "internal": {"id": 24, "rule": "required", "type": "sint32"}, "lv": {"id": 5, "rule": "required", "type": "sint32"}, "maxBattleLevelPassTime": {"id": 31, "type": "sint64"}, "miningProcess": {"id": 15, "rule": "required", "type": "sint32"}, "miningShow": {"id": 16, "rule": "required", "type": "bool"}, "name": {"id": 2, "rule": "required", "type": "string"}, "nextEndRecoverTime": {"id": 13, "rule": "required", "type": "sint64"}, "platformImg": {"id": 20, "rule": "required", "type": "string"}, "recharge": {"id": 17, "rule": "required", "type": "sint32"}, "region": {"id": 27, "type": "string"}, "selectEquipPage": {"id": 14, "rule": "required", "type": "sint32"}, "selectServerId": {"id": 28, "type": "sint32"}, "spiritAnimalAtkAdd": {"id": 30, "type": "sint32"}, "sweepTimes": {"id": 26, "rule": "required", "type": "sint32"}, "tlCmdAdvert": {"id": 19, "rule": "repeated", "type": "CmdAdvert", "options": {}}, "tlCmdAppletInfo": {"id": 25, "rule": "repeated", "type": "CmdAppletInfo", "options": {}}, "tlExtraId": {"id": 22, "rule": "repeated", "type": "sint32", "options": {"packed": false}}, "washStone": {"id": 10, "rule": "required", "type": "sint32"}}}, "CmdRoleAppletShareReqMsg": {"fields": {}}, "CmdRoleAppletShareRewardReqMsg": {"fields": {}}, "CmdRoleAppletShareRewardRspMsg": {"fields": {"tlCmdGoods": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdRoleAppletShareRspMsg": {"fields": {}}, "CmdRoleChangeHeadFrameReqMsg": {"fields": {"resId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdRoleChangeHeadFrameRspMsg": {"fields": {}}, "CmdRoleChangeImgReqMsg": {"fields": {"resId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdRoleChangeImgRspMsg": {"fields": {}}, "CmdRoleChangeNameReqMsg": {"fields": {"name": {"id": 1, "rule": "required", "type": "string"}}}, "CmdRoleChangeNameRspMsg": {"fields": {}}, "CmdRoleDouYinRewardReqMsg": {"fields": {}}, "CmdRoleDouYinRewardRspMsg": {"fields": {"tlCmdGoods": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdRoleEndBuyInfo": {"fields": {"buyId": {"id": 1, "rule": "required", "type": "sint32"}, "number": {"id": 2, "rule": "required", "type": "sint32"}}}, "CmdRoleEndUpdateReqMsg": {"fields": {}}, "CmdRoleEndUpdateRspMsg": {"fields": {}}, "CmdRoleGuanKaGiftBuyReqMsg": {"fields": {"resId": {"id": 1, "rule": "required", "type": "int32"}}}, "CmdRoleGuanKaGiftBuyRspMsg": {"fields": {"tlCmdGoods": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdRoleGuanKaGiftSelectReqMsg": {"fields": {"gemId": {"id": 2, "rule": "required", "type": "int32"}, "resId": {"id": 1, "rule": "required", "type": "int32"}}}, "CmdRoleGuanKaGiftSelectRspMsg": {"fields": {}}, "CmdRoleInfoViewReqMsg": {"fields": {"roleId": {"id": 1, "rule": "required", "type": "int64"}, "serverId": {"id": 2, "rule": "required", "type": "int32"}}}, "CmdRoleInfoViewRspMsg": {"fields": {"headFrame": {"id": 5, "rule": "required", "type": "sint64"}, "img": {"id": 3, "rule": "required", "type": "sint64"}, "level": {"id": 6, "rule": "required", "type": "sint32"}, "platformImg": {"id": 4, "rule": "required", "type": "string"}, "roleId": {"id": 1, "rule": "required", "type": "int64"}, "roleName": {"id": 2, "rule": "required", "type": "string"}, "tlCmdEquip": {"id": 7, "rule": "repeated", "type": "CmdEquip", "options": {}}}}, "CmdRoleLoginReqMsg": {"fields": {"cmdClientInfo": {"id": 1, "rule": "required", "type": "CmdClientInfo"}, "code": {"id": 3, "rule": "required", "type": "string"}, "img": {"id": 8, "type": "string"}, "openid": {"id": 9, "type": "string"}, "platform": {"id": 2, "rule": "required", "type": "int32"}, "reconnect": {"id": 7, "type": "bool"}, "roleId": {"id": 4, "rule": "required", "type": "int64"}, "selectServerId": {"id": 10, "type": "int32"}, "time": {"id": 5, "rule": "required", "type": "int64"}, "token": {"id": 6, "rule": "required", "type": "string"}}}, "CmdRoleLoginRspMsg": {"fields": {"cmdBattleData": {"id": 2, "type": "CmdBattleData"}, "loginStatus": {"id": 3, "rule": "required", "type": "LoginStatus"}, "newRole": {"id": 1, "type": "bool"}}}, "CmdRoleOfflineBroMsg": {"fields": {"type": {"id": 1, "rule": "required", "type": "int32"}}}, "CmdRoleTakeFirstChargeReqMsg": {"fields": {}}, "CmdRoleTakeFirstChargeRspMsg": {"fields": {"tlCmdGoods": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdRoleUploadPlatformImgReqMsg": {"fields": {"platformImg": {"id": 1, "rule": "required", "type": "string"}}}, "CmdRoleUploadPlatformImgRspMsg": {"fields": {}}, "CmdRoleWatchAdvertReqMsg": {"fields": {"type": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdRoleWatchAdvertRspMsg": {"fields": {"tlCmdGoods": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}, "tlId": {"id": 2, "rule": "repeated", "type": "sint32", "options": {"packed": false}}}}, "CmdRole_Key": {"fields": {}}, "CmdRoll": {"fields": {"content": {"id": 1, "rule": "required", "type": "string"}, "time": {"id": 2, "rule": "required", "type": "sint64"}}}, "CmdRollBroMsg": {"fields": {"cmdRoll": {"id": 1, "rule": "required", "type": "CmdRoll"}}}, "CmdRune": {"fields": {"id": {"id": 1, "rule": "required", "type": "sint64"}, "isNew": {"id": 6, "rule": "required", "type": "bool"}, "isPutOn": {"id": 5, "rule": "required", "type": "bool"}, "phase": {"id": 4, "rule": "required", "type": "sint32"}, "quality": {"id": 3, "rule": "required", "type": "sint32"}, "resId": {"id": 2, "rule": "required", "type": "sint64"}}}, "CmdRunePutOnReqMsg": {"fields": {"area": {"id": 2, "rule": "required", "type": "sint32"}, "id": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdRunePutOnRspMsg": {"fields": {}}, "CmdRuneReplaceReqMsg": {"fields": {"id": {"id": 1, "rule": "required", "type": "sint64"}, "replaceId": {"id": 2, "rule": "required", "type": "sint64"}}}, "CmdRuneReplaceRspMsg": {"fields": {}}, "CmdRuneSynthesisOneKeyReqMsg": {"fields": {}}, "CmdRuneSynthesisOneKeyRspMsg": {"fields": {"id": {"id": 1, "rule": "repeated", "type": "sint64", "options": {"packed": false}}}}, "CmdRuneSynthesisReqMsg": {"fields": {"nid": {"id": 4, "rule": "repeated", "type": "sint64", "options": {"packed": false}}, "phase": {"id": 3, "rule": "required", "type": "sint32"}, "quality": {"id": 2, "rule": "required", "type": "sint32"}, "resId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdRuneSynthesisRspMsg": {"fields": {}}, "CmdRuneTakeOffReqMsg": {"fields": {"area": {"id": 2, "rule": "required", "type": "sint32"}, "id": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdRuneTakeOffRspMsg": {"fields": {}}, "CmdRune_Key": {"fields": {"id": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdSecretRealm": {"fields": {"endTime": {"id": 4, "rule": "required", "type": "sint64"}, "level": {"id": 3, "rule": "required", "type": "sint32"}, "roleId": {"id": 1, "rule": "required", "type": "sint64"}, "secretRealmScore": {"id": 5, "rule": "repeated", "type": "CmdSecretRealmScore", "options": {}}, "type": {"id": 2, "rule": "required", "type": "sint32"}}}, "CmdSecretRealmScore": {"fields": {"level": {"id": 2, "rule": "required", "type": "sint32"}, "type": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdSecretRealm_Key": {"fields": {"roleId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdSevenDayInfo": {"fields": {"cmdSevenDayPoint": {"id": 2, "rule": "required", "type": "CmdSevenDayPoint"}, "tlCmdSevenDayShop": {"id": 3, "rule": "repeated", "type": "CmdSevenDayShop", "options": {}}, "tlCmdTask": {"id": 1, "rule": "repeated", "type": "CmdTask", "options": {}}}}, "CmdSevenDayInfoReqMsg": {"fields": {}}, "CmdSevenDayInfoRspMsg": {"fields": {"cmdSevenDayInfo": {"id": 1, "rule": "required", "type": "CmdSevenDayInfo"}}}, "CmdSevenDayPoint": {"fields": {"point": {"id": 1, "rule": "required", "type": "sint32"}, "tlIds": {"id": 2, "rule": "repeated", "type": "sint32", "options": {"packed": false}}}}, "CmdSevenDayShop": {"fields": {"buyTimes": {"id": 2, "rule": "required", "type": "int32"}, "id": {"id": 1, "rule": "required", "type": "int32"}}}, "CmdSevenDayShopBuyReqMsg": {"fields": {"id": {"id": 1, "rule": "required", "type": "int32"}}}, "CmdSevenDayShopBuyRspMsg": {"fields": {"cmdSevenDayInfo": {"id": 1, "rule": "required", "type": "CmdSevenDayInfo"}, "tlCmdGoods": {"id": 2, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdSevenDayTakePointRewardReqMsg": {"fields": {"resId": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdSevenDayTakePointRewardRspMsg": {"fields": {"cmdSevenDayInfo": {"id": 1, "rule": "required", "type": "CmdSevenDayInfo"}, "tlCmdGoods": {"id": 2, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdSevenDayTakeTaskRewardReqMsg": {"fields": {"resId": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdSevenDayTakeTaskRewardRspMsg": {"fields": {"cmdSevenDayInfo": {"id": 1, "rule": "required", "type": "CmdSevenDayInfo"}, "tlCmdGoods": {"id": 2, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdShare": {"fields": {}}, "CmdShareGetRewardReqMsg": {"fields": {}}, "CmdShareGetRewardRspMsg": {"fields": {"tlCmdGoods": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdShareStateReqMsg": {"fields": {}}, "CmdShareStateRspMsg": {"fields": {"rewardState": {"id": 2, "rule": "required", "type": "sint32"}, "shareState": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdShare_Key": {"fields": {"id": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdShop": {"fields": {"cmdShopType": {"id": 1, "rule": "required", "type": "CmdShopType"}, "tlCmdShopGoods": {"id": 2, "rule": "repeated", "type": "CmdShopGoods", "options": {}}}}, "CmdShopBuyReqMsg": {"fields": {"cmdShopType": {"id": 2, "rule": "required", "type": "CmdShopType"}, "id": {"id": 1, "rule": "required", "type": "int32"}, "tenTimes": {"id": 3, "type": "bool"}, "tlGoodReplace": {"id": 4, "rule": "repeated", "type": "CmdGoodReplace", "options": {}}}}, "CmdShopBuyRspMsg": {"fields": {"tlCmdGoods": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdShopGoods": {"fields": {"alreadyTakeBox": {"id": 7, "rule": "repeated", "type": "sint32", "options": {"packed": false}}, "boxProcess": {"id": 6, "type": "sint32"}, "buyTimes": {"id": 2, "rule": "required", "type": "int32"}, "id": {"id": 1, "rule": "required", "type": "int32"}, "nextBuyTime": {"id": 3, "type": "sint64"}, "nextTime": {"id": 4, "type": "sint32"}, "nextTime50": {"id": 5, "type": "sint32"}}}, "CmdShopType": {"values": {"box": 1, "diamond": 3, "gift": 5, "gold": 4, "resource": 2, "spiritAnimal": 6}}, "CmdShop_Key": {"fields": {"cmdShopType": {"id": 1, "rule": "required", "type": "CmdShopType"}}}, "CmdSigninInfo": {"fields": {"boxTlCmdTask": {"id": 2, "rule": "repeated", "type": "CmdTask", "options": {}}, "day": {"id": 5, "rule": "required", "type": "int32"}, "round": {"id": 4, "rule": "required", "type": "int32"}, "tlCmdTask": {"id": 1, "rule": "repeated", "type": "CmdTask", "options": {}}, "type": {"id": 3, "rule": "required", "type": "int32"}}}, "CmdSigninInfoReqMsg": {"fields": {}}, "CmdSigninInfoRspMsg": {"fields": {"cmdSigninInfo": {"id": 1, "rule": "required", "type": "CmdSigninInfo"}}}, "CmdSigninTakeTaskRewardReqMsg": {"fields": {"resId": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdSigninTakeTaskRewardRspMsg": {"fields": {"cmdSigninInfo": {"id": 1, "rule": "required", "type": "CmdSigninInfo"}, "tlCmdGoods": {"id": 2, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdSkin": {"fields": {"resId": {"id": 1, "rule": "required", "type": "sint64"}, "used": {"id": 2, "type": "bool"}}}, "CmdSkinActDetail": {"fields": {"alreadyBuy": {"id": 3, "rule": "required", "type": "bool"}, "price": {"id": 2, "rule": "required", "type": "int32"}, "skinResId": {"id": 1, "rule": "required", "type": "int64"}}}, "CmdSkinPlayer": {"fields": {"tlCmdSkinShop": {"id": 1, "rule": "repeated", "type": "CmdSkinShop", "options": {}}}}, "CmdSkinShop": {"fields": {"buy": {"id": 2, "rule": "required", "type": "bool"}, "id": {"id": 1, "rule": "required", "type": "int32"}}}, "CmdSkin_Key": {"fields": {"resId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdSpecialGiftCache": {"fields": {"endTime": {"id": 3, "rule": "required", "type": "sint64"}, "resId": {"id": 1, "rule": "required", "type": "sint64"}, "state": {"id": 2, "rule": "required", "type": "CmdCommonState"}}}, "CmdSpecialGiftCache_Key": {"fields": {"resId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdSpiritAnimal": {"fields": {"level": {"id": 3, "rule": "required", "type": "sint32"}, "resId": {"id": 2, "rule": "required", "type": "sint64"}, "uniqueId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdSpiritAnimalBattleReqMsg": {"fields": {"uniqueId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdSpiritAnimalBattleRspMsg": {"fields": {}}, "CmdSpiritAnimalData": {"fields": {"tlCmdBattleAttr": {"id": 3, "rule": "repeated", "type": "CmdBattleAttr", "options": {}}, "tlCmdBattleBuff": {"id": 2, "rule": "repeated", "type": "CmdBattleBuff", "options": {}}, "tlSkillId": {"id": 1, "rule": "repeated", "type": "sint32", "options": {"packed": false}}}}, "CmdSpiritAnimalLevelUpReqMsg": {"fields": {"uniqueId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdSpiritAnimalLevelUpRspMsg": {"fields": {}}, "CmdSpiritAnimalResetPreviewReqMsg": {"fields": {"uniqueId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdSpiritAnimalResetPreviewRspMsg": {"fields": {"tlCmdGoods": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdSpiritAnimalResetReqMsg": {"fields": {"uniqueId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdSpiritAnimalResetRspMsg": {"fields": {"tlCmdGoods": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdSpiritAnimalStepUpReqMsg": {"fields": {"tlCampUniqueId": {"id": 3, "rule": "repeated", "type": "sint64", "options": {"packed": false}}, "tlSameUniqueId": {"id": 2, "rule": "repeated", "type": "sint64", "options": {"packed": false}}, "uniqueId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdSpiritAnimalStepUpRspMsg": {"fields": {}}, "CmdSpiritAnimal_Key": {"fields": {"uniqueId": {"id": 1, "rule": "required", "type": "sint64"}}}, "CmdSystemGmReqMsg": {"fields": {"cmd": {"id": 1, "rule": "required", "type": "string"}}}, "CmdSystemGmRspMsg": {"fields": {"tlCmdGoods": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdSystemSyncTimeReqMsg": {"fields": {}}, "CmdSystemSyncTimeRspMsg": {"fields": {"serverTime": {"id": 1, "rule": "required", "type": "int64"}}}, "CmdTakeChapterRewardReqMsg": {"fields": {"chapter": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdTakeChapterRewardRspMsg": {"fields": {"tlCmdGoods": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdTakeDayChallengeActRewardReqMsg": {"fields": {"resId": {"id": 1, "rule": "required", "type": "sint32"}, "taskResId": {"id": 2, "rule": "required", "type": "sint32"}}}, "CmdTakeDayChallengeActRewardRspMsg": {"fields": {"tlReward": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdTakeGameCircleRewardReqMsg": {"fields": {}}, "CmdTakeGameCircleRewardRspMsg": {"fields": {"tlCmdGoods": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdTakeLockMonsterBoxReqMsg": {"fields": {}}, "CmdTakeLockMonsterBoxRspMsg": {"fields": {"tlCmdGoods": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdTakeMonthCardActRewardReqMsg": {"fields": {"resId": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdTakeMonthCardActRewardRspMsg": {"fields": {"tlReward": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdTakeRechargeGiftActRewardReqMsg": {"fields": {"rechargeProcess": {"id": 2, "rule": "required", "type": "sint32"}, "resId": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdTakeRechargeGiftActRewardRspMsg": {"fields": {"tlReward": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdTakeSpiritAnimalBoxReqMsg": {"fields": {"id": {"id": 1, "rule": "required", "type": "sint32"}, "resId": {"id": 3, "rule": "required", "type": "sint64"}, "takeProcess": {"id": 2, "rule": "required", "type": "sint32"}}}, "CmdTakeSpiritAnimalBoxRspMsg": {"fields": {"tlCmdGoods": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdTakeStarRewardReqMsg": {"fields": {"level": {"id": 1, "rule": "required", "type": "sint32"}, "star": {"id": 2, "rule": "required", "type": "sint32"}}}, "CmdTakeStarRewardRspMsg": {"fields": {"tlCmdGoods": {"id": 1, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "CmdTask": {"fields": {"count": {"id": 2, "rule": "required", "type": "sint32"}, "resId": {"id": 1, "rule": "required", "type": "sint32"}, "rewardState": {"id": 3, "rule": "required", "type": "CmdCommonState"}}}, "CmdTaskType": {"values": {"SEVEN_DAY_TASK": 2, "signin": 4}}, "CmdTask_Key": {"fields": {"resId": {"id": 1, "rule": "required", "type": "sint32"}}}, "CmdTips": {"fields": {"content": {"id": 1, "rule": "required", "type": "string"}}}, "CmdUpdateRedDotReqMsg": {"fields": {"redDotType": {"id": 1, "rule": "required", "type": "CmdRedDotType"}}}, "CmdUpdateRedDotRspMsg": {"fields": {}}, "EveryDayGift": {"fields": {"buyCount": {"id": 3, "rule": "required", "type": "sint32"}, "fromId": {"id": 2, "rule": "required", "type": "sint32"}, "giftId": {"id": 1, "rule": "required", "type": "sint64"}, "rewards": {"id": 4, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "EveryDaySignInReward": {"fields": {"day": {"id": 1, "rule": "required", "type": "sint32"}, "state": {"id": 3, "rule": "required", "type": "sint32"}, "tlCmdGoods": {"id": 2, "rule": "repeated", "type": "CmdGoods", "options": {}}}}, "LoginStatus": {"values": {"BAN": 3, "SUCCESS": 1, "WEI_HU": 2}}, "MessageId": {"values": {"CmdActivityJsonRspMsg": 904212428, "CmdActivityLimitSkinBuyRspMsg": 730391206, "CmdActivityLimitSkinInfoRspMsg": 2115460800, "CmdActivityLimitTimeInfoRspMsg": 1769777808, "CmdActivityLimitTimeTakeRspMsg": 1469215657, "CmdBattleBroMsg": 1253466612, "CmdBattleCompleteInfoRspMsg": 1330132868, "CmdBattleDataUploadRspMsg": 1914585408, "CmdBattlePreviewRspMsg": 1720724839, "CmdBattleRandomSkillRspMsg": 140321439, "CmdBattleResultRspMsg": 475498478, "CmdBattleSelectSkillRspMsg": 355247302, "CmdBattleStatisticsRspMsg": 881202280, "CmdBattleSweepRspMsg": 70842819, "CmdBattleTokenOpenRspMsg": 224520948, "CmdBattleTokenResetRspMsg": 952348427, "CmdBattleTokenTakeRewardRspMsg": 2063652608, "CmdBreachRspMsg": 287063570, "CmdBuySkinRspMsg": 55511540, "CmdBuyStaBuyRspMsg": 730345707, "CmdBuyStaInfoRspMsg": 2114050331, "CmdCacheBroMsg": 331957642, "CmdCardBuyRspMsg": 579891201, "CmdCastleApprenticeUpLevelRspMsg": 492743159, "CmdCastleApprenticeUpRspMsg": 1220313759, "CmdCastleSkillResearchRspMsg": 1359547363, "CmdCastleSkillUpLevelRspMsg": 21015693, "CmdCastleWallQualityUpRspMsg": 486054753, "CmdCastleWallSelectRspMsg": 108080165, "CmdCastleWallUpLevelRspMsg": 157500720, "CmdCastleWallUpSkillLevelRspMsg": 2070127885, "CmdCastleWorkShopOpenRspMsg": 1776254022, "CmdCastleWorkShopSpeedRspMsg": 367202545, "CmdCastleWorkShopTakeRspMsg": 310061251, "CmdCastleWorkShopUpLevelRspMsg": 581980397, "CmdChangeUsedSkinRspMsg": 1911316089, "CmdChapterRewardRspMsg": 1975570309, "CmdCodeExchangeRspMsg": 452401139, "CmdContinueNextBattleRspMsg": 986683025, "CmdContinuityGiftFinishRewardRspMsg": 402025121, "CmdContinuityGiftInfoRspMsg": 1330212203, "CmdContinuityGiftRewardRspMsg": 1554306220, "CmdDayWeekMonthGiftFreeBuyRspMsg": 355499005, "CmdDayWeekMonthGiftGetRewardRspMsg": 1356079122, "CmdDayWeekMonthGiftInfoRspMsg": 1005269291, "CmdEndBroMsg": 1882529373, "CmdEndBuyInfoRspMsg": 1357989782, "CmdEndBuyRspMsg": 1695409208, "CmdEnterBattleRspMsg": 954352089, "CmdEquipChangePageNameRspMsg": 918039683, "CmdEquipSelectPageRspMsg": 806986012, "CmdEquipUpLevelAllRspMsg": 1246543183, "CmdEquipUpLevelRspMsg": 154590858, "CmdEveryDayGetRewardsRspMsg": 1935261292, "CmdEveryDaySignInInfoRspMsg": 1133773426, "CmdEveryDaySignInRspMsg": 1704555072, "CmdFirstRechargeOpenRspMsg": 938272586, "CmdFirstRechargeRewardRspMsg": 622072529, "CmdFriendAgreeOneKeyRspMsg": 1277358288, "CmdFriendBlackRemoveRspMsg": 78131250, "CmdFriendDetailRspMsg": 1169482284, "CmdFriendGetEndOneKeyRspMsg": 437546553, "CmdFriendGetEndRspMsg": 2055097216, "CmdFriendLikeRspMsg": 545204978, "CmdFriendRefreshSuggestRspMsg": 429905916, "CmdFriendRefuseOneKeyRspMsg": 1734010408, "CmdFriendSendEndOneKeyRspMsg": 384409047, "CmdFriendSendEndRspMsg": 1466069662, "CmdFriendsAddRspMsg": 552573225, "CmdFriendsApplyRspMsg": 2057674506, "CmdFriendsBlackRspMsg": 227867847, "CmdFriendsRefuseRspMsg": 1262322162, "CmdFriendsRemoveRspMsg": 1219983774, "CmdFriendsSearchRspMsg": 246249338, "CmdFuBenBuyTimesRspMsg": 427584667, "CmdFuBenDailyGetRewardRspMsg": 1528771729, "CmdFuBenEnterRspMsg": 827208359, "CmdFundGetInfoRspMsg": 510764152, "CmdFundGetRewardRspMsg": 1902555017, "CmdGemComposePreviewRspMsg": 1938966802, "CmdGemComposeRspMsg": 1565473696, "CmdGemDownRspMsg": 561910022, "CmdGemLockRspMsg": 998030141, "CmdGemOnRspMsg": 723509321, "CmdGemRefineRspMsg": 346087611, "CmdGemRefineSaveRspMsg": 1149209122, "CmdGemRefreshSkillRspMsg": 2140529010, "CmdGemReplaceRspMsg": 1557847038, "CmdGetAllPlayClientAniRspMsg": 1343478075, "CmdGetHallRoleListRspMsg": 1624091372, "CmdGetRankRspMsg": 345265589, "CmdGmListRspMsg": 323387391, "CmdGoodsRedDotDispelRspMsg": 805392448, "CmdGuanKaGiftBroMsg": 915823971, "CmdGuideReadRspMsg": 142005541, "CmdGuideRecordRspMsg": 353871062, "CmdHangGetRewardRspMsg": 1443586620, "CmdHangQuickRspMsg": 1023337980, "CmdHangTimeRspMsg": 20353276, "CmdHeroChangeRspMsg": 302212057, "CmdHeroLevelUpOneKeyRspMsg": 200868985, "CmdHeroLevelUpRspMsg": 914609230, "CmdHeroOpenRspMsg": 1722636353, "CmdHeroUnlockGetInfoRspMsg": 647497073, "CmdHeroUnlockRewardRspMsg": 2024711478, "CmdItemBoxUseRspMsg": 522720460, "CmdItemUseRspMsg": 486417821, "CmdMailDeleteRspMsg": 172353567, "CmdMailListRspMsg": 319013138, "CmdMailReadRspMsg": 546189302, "CmdMailTakeRewardRspMsg": 555367350, "CmdMallBroMsg": 528672976, "CmdMallCoinBuyRspMsg": 115313430, "CmdMallDailyShopBuyRspMsg": 1185112280, "CmdMallGetDataRspMsg": 1322643093, "CmdMallLuckDrawRspMsg": 1193344794, "CmdMallSEquipDrawRspMsg": 163390926, "CmdMiningBuyRspMsg": 1065397953, "CmdModifyItemBroMsg": 658416489, "CmdRechargeBroMsg": 495748573, "CmdRecordClientAniRspMsg": 855569897, "CmdRoleAppletShareRewardRspMsg": 1186069383, "CmdRoleAppletShareRspMsg": 1597596872, "CmdRoleChangeHeadFrameRspMsg": 2075087664, "CmdRoleChangeImgRspMsg": 912768250, "CmdRoleChangeNameRspMsg": 1996633358, "CmdRoleDouYinRewardRspMsg": 1594668726, "CmdRoleEndUpdateRspMsg": 1865990615, "CmdRoleGuanKaGiftBuyRspMsg": 549296840, "CmdRoleGuanKaGiftSelectRspMsg": 1810611536, "CmdRoleInfoViewRspMsg": 1505925414, "CmdRoleLoginRspMsg": 158618628, "CmdRoleOfflineBroMsg": 2041625045, "CmdRoleTakeFirstChargeRspMsg": 17475600, "CmdRoleUploadPlatformImgRspMsg": 225100382, "CmdRoleWatchAdvertRspMsg": 1151490410, "CmdRollBroMsg": 11546745, "CmdRunePutOnRspMsg": 648739037, "CmdRuneReplaceRspMsg": 224396995, "CmdRuneSynthesisOneKeyRspMsg": 1160730652, "CmdRuneSynthesisRspMsg": 1308719965, "CmdRuneTakeOffRspMsg": 887086199, "CmdSevenDayInfoRspMsg": 867503290, "CmdSevenDayShopBuyRspMsg": 1979875530, "CmdSevenDayTakePointRewardRspMsg": 1539905038, "CmdSevenDayTakeTaskRewardRspMsg": 328767495, "CmdShareGetRewardRspMsg": 883372771, "CmdShareStateRspMsg": 669018575, "CmdShopBuyRspMsg": 1338068263, "CmdSigninInfoRspMsg": 946249011, "CmdSigninTakeTaskRewardRspMsg": 75320742, "CmdSpiritAnimalBattleRspMsg": 1439043070, "CmdSpiritAnimalLevelUpRspMsg": 2051518427, "CmdSpiritAnimalResetPreviewRspMsg": 1924442819, "CmdSpiritAnimalResetRspMsg": 454379051, "CmdSpiritAnimalStepUpRspMsg": 1991681903, "CmdSystemGmRspMsg": 1915513454, "CmdSystemSyncTimeRspMsg": 2058086284, "CmdTakeChapterRewardRspMsg": 1792000898, "CmdTakeDayChallengeActRewardRspMsg": 1731012380, "CmdTakeGameCircleRewardRspMsg": 1855584437, "CmdTakeLockMonsterBoxRspMsg": 1090703712, "CmdTakeMonthCardActRewardRspMsg": 650624469, "CmdTakeRechargeGiftActRewardRspMsg": 364759860, "CmdTakeSpiritAnimalBoxRspMsg": 923167072, "CmdTakeStarRewardRspMsg": 1668952987, "CmdUpdateRedDotRspMsg": 87323966}}, "RechargeCount": {"fields": {"buyState": {"id": 2, "rule": "required", "type": "sint32"}, "resId": {"id": 1, "rule": "required", "type": "sint32"}, "rewards": {"id": 3, "rule": "repeated", "type": "CmdGoods", "options": {}}}}}}}}}}}}}}]], 0, 0, [], [], []]