[1, ["e77zaeoRpIQ5GJcH95xnjT", "a8P4IJbyBNEpcCNtP3jfEe", "e8k7c7gzxMraidqJTfiwYk", "6cydYkGqdIZ67w5ghLDAub", "7alv4en9lHeZAXm8rbLYgt", "b66F5rvKtNHamtocyU4Jvh"], ["_textureSetter", "value"], ["cc.SpriteFrame", ["cc.AnimationClip", ["_name", "_duration", "sample", "events", "curveData"], -1, 11]], [[1, 0, 1, 2, 3, 4, 5]], [[[{"name": "落石术_009", "rect": [2, 730, 244, 216], "offset": [-2, 40], "originalSize": [350, 350], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "落石术_010", "rect": [249, 2, 236, 224], "offset": [-2, 44], "originalSize": [350, 350], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "落石术_007", "rect": [248, 862, 230, 192], "offset": [-2, 33], "originalSize": [350, 350], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "落石术_011", "rect": [291, 1246, 212, 146], "offset": [-1, 5], "originalSize": [350, 350], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "落石术_008", "rect": [249, 448, 236, 208], "offset": [-2, 37], "originalSize": [350, 350], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[[0, "落石术_燃烧陨石_坑", 1, 24, [{"frame": 0.3333333333333333, "func": "Hit", "params": []}], [{}, "paths", 11, [{}, "图片_特效_落石术_001", 11, [{"props": {"active": [{"frame": 0, "value": false}, {"frame": 0.3333333333333333, "value": true}, {"frame": 0.75, "value": false}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0.3333333333333333}, "value", 6, 0], [{"frame": 0.4166666666666667}, "value", 6, 1], [{"frame": 0.5}, "value", 6, 2], [{"frame": 0.5833333333333334}, "value", 6, 3], [{"frame": 0.6666666666666666}, "value", 6, 4]], 11, 11, 11, 11, 11]]]], "烟01", 11, [{}, "props", 11, [{"active": [{"frame": 0, "value": false}, {"frame": 0.3333333333333333, "value": true}], "position": [{"frame": 0.3333333333333333, "value": [-89.622, -0.717, 0]}, {"frame": 1, "value": [-138.622, 14.283, 0]}], "angle": [{"frame": 0.3333333333333333, "value": -93.737}, {"frame": 1, "value": -140.285}], "opacity": [{"frame": 0.3333333333333333, "value": 0}, {"frame": 0.6666666666666666, "value": 150}, {"frame": 1, "value": 0}]}, "scale", 12, [[[{"frame": 0.3333333333333333}, "value", 8, [1, 1.418, 1.418, 1.418]], [{"frame": 1}, "value", 8, [1, 2.188, 2.188, 2.188]]], 11, 11]]], "烟02", 11, [{}, "props", 11, [{"active": [{"frame": 0, "value": false}, {"frame": 0.3333333333333333, "value": true}], "position": [{"frame": 0.3333333333333333, "value": [42.301, -26.528, 0]}, {"frame": 0.875, "value": [54.301, -37.528, 0]}], "angle": [{"frame": 0.3333333333333333, "value": 0}, {"frame": 0.875, "value": 72.819}], "opacity": [{"frame": 0.3333333333333333, "value": 0}, {"frame": 0.5833333333333334, "value": 200}, {"frame": 0.875, "value": 0}]}, "scale", 12, [[[{"frame": 0.3333333333333333}, "value", 8, [1, 1.714, 1.714, 1.714]], [{"frame": 0.875}, "value", 8, [1, 2.407, 2.407, 2.407]]], 11, 11]]], "烟03", 11, [{}, "props", 11, [{"active": [{"frame": 0, "value": false}, {"frame": 0.3333333333333333, "value": true}], "position": [{"frame": 0.3333333333333333, "value": [-63.094, -24.377, 0]}, {"frame": 0.8333333333333334, "value": [-83.094, -37.377, 0]}], "angle": [{"frame": 0.3333333333333333, "value": -109.292}, {"frame": 0.8333333333333334, "value": -176.83800000000002}], "opacity": [{"frame": 0.3333333333333333, "value": 0}, {"frame": 0.5416666666666666, "value": 150}, {"frame": 0.8333333333333334, "value": 0}]}, "scale", 12, [[[{"frame": 0.3333333333333333}, "value", 8, [1, 1.626, 1.626, 1.626]], [{"frame": 0.8333333333333334}, "value", 8, [1, 2.348, 2.348, 2.348]]], 11, 11]]], "烟04", 11, [{}, "props", 11, [{"active": [{"frame": 0, "value": false}, {"frame": 0.3333333333333333, "value": true}], "position": [{"frame": 0.3333333333333333, "value": [5.019, -35.132, 0]}, {"frame": 0.875, "value": [-10.981, -41.132, 0]}], "angle": [{"frame": 0.3333333333333333, "value": 131.284}, {"frame": 0.875, "value": 100.08399999999999}], "opacity": [{"frame": 0.3333333333333333, "value": 0}, {"frame": 0.5833333333333334, "value": 200}, {"frame": 0.875, "value": 0}]}, "scale", 12, [[[{"frame": 0.3333333333333333}, "value", 8, [1, 2.326, 2.326, 2.326]], [{"frame": 0.875}, "value", 8, [1, 3.198, 3.198, 3.198]]], 11, 11]]], "烟05", 11, [{}, "props", 11, [{"active": [{"frame": 0, "value": false}, {"frame": 0.3333333333333333, "value": true}], "position": [{"frame": 0.3333333333333333, "value": [62.566, 6.547, 0]}, {"frame": 0.9583333333333334, "value": [100.566, -8.453, 0]}], "angle": [{"frame": 0.3333333333333333, "value": 0}, {"frame": 0.9583333333333334, "value": 63.395}], "opacity": [{"frame": 0.3333333333333333, "value": 0}, {"frame": 0.6666666666666666, "value": 80}, {"frame": 0.9583333333333334, "value": 0}]}, "scale", 12, [[[{"frame": 0.3333333333333333}, "value", 8, [1, 1.488, 1.488, 1.488]], [{"frame": 0.9583333333333334}, "value", 8, [1, 1.876, 1.876, 1.876]]], 11, 11]]]]]]], 0, 0, [0, 0, 0, 0, 0], [1, 1, 1, 1, 1], [1, 2, 3, 4, 5]]]]