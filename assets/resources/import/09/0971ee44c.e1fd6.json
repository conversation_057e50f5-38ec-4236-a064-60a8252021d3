[1, ["e1yDFiKM9K2beDFh/qW9bQ", "106Ilu3B1E+7y7CHwNMm7T", "0b2xsPfJJHbItpH+5ILU4R", "6fOFK+5PZE5ZOi5dep+UFT", "6cxgFu0t9F8pF2JS/4Raox"], ["_parent", "node", "_textureSetter", "root", "data", "_spriteFrame", "_defaultClip"], [["cc.Node", ["_name", "_prefab", "_parent", "_trs", "_children", "_components", "_contentSize"], 2, 4, 1, 7, 2, 9, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_children", "_prefab", "_contentSize"], 2, 1, 12, 4, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6]], [[1, 0, 1, 2, 2], [0, 0, 2, 1, 3, 2], [3, 0, 2], [0, 0, 4, 1, 2], [0, 0, 2, 5, 1, 6, 3, 2], [4, 0, 1, 2, 3, 4, 2], [1, 1, 2, 1], [5, 0, 1, 2, 3, 4, 3], [6, 0, 1, 2, 3, 2]], [[[{"name": "骷髅壮汉-animation_0", "rect": [2, 660, 172, 114], "offset": [-8, -13], "originalSize": [238, 242], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [1]], [[[2, "白骨战士"], [3, "root", [-2], [6, -1, 0]], [5, "position", 1, [[-3, [1, "脚底_挂点", -4, [0, "e3XqhryhdK36DEUHpAIYKb", 1, 0], [0, -66, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "身体_挂点", -5, [0, "00UP0dX9JIxo1O6ykBooM7", 1, 0], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "头顶_挂点", -6, [0, "5cp2Ztl1hNKYApZIxC3mBI", 1, 0], [0, 83, 0, 0, 0, 0, 1, 1, 1, 1]]], 1, 4, 4, 4], [0, "1768HiGARNaoyAvM2o3PlD", 1, 0], [5, 120, 120]], [4, "sprite", 2, [[7, 0, false, -7, [0], 1], [8, true, -8, [3, 4, 5], 2]], [0, "24MVL74RhOqINJoHjo0+ow", 1, 0], [5, 340, 346], [38, 24, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 3, 1, 0, -1, 2, 0, -1, 3, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 1, 3, 0, 1, 3, 0, 4, 1, 8], [0, 0, 0, 0, 0, 0], [-1, 5, 6, -1, -2, -3], [2, 3, 0, 0, 0, 4]]]]