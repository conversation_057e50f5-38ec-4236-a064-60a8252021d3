[1, ["d1+33AB2hD7ooy8pcuCSp3", "28gRXK8gtJZ4XTak6ocgNY", "d0mHj5P7tB05Rp8neBrBdY", "68OezSqXFLL4GbXlY7A+Gi", "b9oLE5f/hJarv2863RZIs8"], ["value", "_textureSetter"], ["cc.SpriteFrame", ["cc.AnimationClip", ["_name", "_duration", "sample", "wrapMode", "curveData"], -1, 11]], [[1, 0, 1, 2, 3, 4, 5]], [[[[0, "巨木术_常态", 0.7083333333333334, 24, 2, [{"comps": {"cc.Sprite": {"spriteFrame": []}}}, "paths", 11, [{}, "滚石术_低级_1", 11, [{}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 0], [{"frame": 0.16666666666666666}, "value", 6, 1], [{"frame": 0.3333333333333333}, "value", 6, 2], [{"frame": 0.5}, "value", 6, 3], [{"frame": 0.6666666666666666}, "value", 6, 4]], 11, 11, 11, 11, 11]]]], "滚石术_低级_2", 11, [{}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 5], [{"frame": 0.16666666666666666}, "value", 6, 6], [{"frame": 0.3333333333333333}, "value", 6, 7], [{"frame": 0.5}, "value", 6, 8], [{"frame": 0.6666666666666666}, "value", 6, 9]], 11, 11, 11, 11, 11]]]], "滚石术_低级_3", 11, [{}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 10], [{"frame": 0.16666666666666666}, "value", 6, 11], [{"frame": 0.3333333333333333}, "value", 6, 12], [{"frame": 0.5}, "value", 6, 13], [{"frame": 0.6666666666666666}, "value", 6, 14]], 11, 11, 11, 11, 11]]]], "滚石术_低级_4", 11, [{}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 15], [{"frame": 0.16666666666666666}, "value", 6, 16], [{"frame": 0.3333333333333333}, "value", 6, 17], [{"frame": 0.5}, "value", 6, 18], [{"frame": 0.6666666666666666}, "value", 6, 19]], 11, 11, 11, 11, 11]]]], "滚石术_低级_5", 11, [{}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 20], [{"frame": 0.16666666666666666}, "value", 6, 21], [{"frame": 0.3333333333333333}, "value", 6, 22], [{"frame": 0.5}, "value", 6, 23], [{"frame": 0.6666666666666666}, "value", 6, 24]], 11, 11, 11, 11, 11]]]]]]]], 0, 0, [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 2, 3, 0, 0, 1, 2, 3, 0, 0, 1, 2, 3, 0, 0, 1, 2, 3, 0, 0, 1, 2, 3, 0, 0]], [[{"name": "滚石术_低级_000", "rect": [166, 184, 62, 112], "offset": [-1, 3], "originalSize": [70, 140], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [4]], [[{"name": "滚石术_低级_002", "rect": [236, 2, 60, 118], "offset": [-2, 1], "originalSize": [70, 140], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [4]], [[{"name": "滚石术_低级_001", "rect": [236, 64, 58, 118], "offset": [-1, 0], "originalSize": [70, 140], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [4]], [[{"name": "滚石术_低级_003", "rect": [236, 124, 58, 114], "offset": [-2, 3], "originalSize": [70, 140], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [4]]]]