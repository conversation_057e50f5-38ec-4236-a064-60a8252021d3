[1, ["ecpdLyjvZBwrvm+cedCcQy", "53QlW+DHtOwahmrzy4HYFe", "22tJtObLpPWJxCt0wnFM/A", "d2igJ+8K1EILJyXCl5lhh9", "6cqOzoS9FH/Y3GNsJCMJO/"], ["node", "_spriteFrame", "_textureSetter", "root", "data", "_parent", "_defaultClip"], [["cc.Node", ["_name", "_opacity", "_prefab", "_components", "_parent", "_children", "_contentSize", "_trs", "_color", "_anchorPoint"], 1, 4, 9, 1, 2, 5, 7, 5, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_dstBlendFactor", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], "cc.SpriteFrame", ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.AnimationClip", ["_name", "_duration", "curveData"], 1, 11], ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6]], [[3, 0, 1, 2, 2], [0, 0, 4, 3, 2, 6, 7, 2], [1, 3, 4, 5, 1], [1, 2, 3, 4, 5, 2], [0, 0, 4, 3, 2, 8, 6, 9, 7, 2], [0, 0, 4, 5, 2, 2], [4, 0, 1, 2, 3], [5, 0, 2], [0, 0, 5, 2, 2], [0, 0, 5, 3, 2, 2], [0, 0, 1, 4, 3, 2, 8, 6, 9, 7, 3], [3, 1, 2, 1], [1, 0, 1, 3, 4, 3], [6, 0, 1, 2, 3, 2]], [[[{"name": "城墙回血_02", "rect": [2, 2, 103, 36], "offset": [0, 1], "originalSize": [119, 42], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [3]], [[{"name": "城墙回血_01", "rect": [40, 2, 66, 68], "offset": [0, 1], "originalSize": [70, 70], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [3]], [[[6, "城墙回血", 0.9833333333333333, [{}, "paths", 11, [{"城墙回血_1": {"props": {"position": [{"frame": 0, "value": [-324.927, 51.99, 0]}, {"frame": 0.5333333333333333, "value": [-324.927, 166.359, 0]}], "opacity": [{"frame": 0, "value": 0}, {"frame": 0.23333333333333334, "value": 255}, {"frame": 0.5333333333333333, "value": 0}]}}, "城墙回血_2": {"props": {"position": [{"frame": 0.13333333333333333, "value": [-280.049, 41.831, 0]}, {"frame": 0.65, "value": [-280.049, 164.652, 0]}], "opacity": [{"frame": 0.13333333333333333, "value": 0}, {"frame": 0.35, "value": 255}, {"frame": 0.65, "value": 0}]}}, "城墙回血_3": {"props": {"position": [{"frame": 0.2833333333333333, "value": [-124.249, 29.129, 0]}, {"frame": 0.7833333333333333, "value": [-124.249, 149.256, 0]}], "opacity": [{"frame": 0.2833333333333333, "value": 0}, {"frame": 0.5166666666666667, "value": 255}, {"frame": 0.7833333333333333, "value": 0}]}}, "城墙回血_4": {"props": {"position": [{"frame": 0.05, "value": [-179.373, 65.431, 0]}, {"frame": 0.5666666666666667, "value": [-179.373, 150.01, 0]}], "opacity": [{"frame": 0.05, "value": 0}, {"frame": 0.2833333333333333, "value": 255}, {"frame": 0.5666666666666667, "value": 0}]}}, "城墙回血_5": {"props": {"position": [{"frame": 0.25, "value": [79.731, 85.753, 0]}, {"frame": 0.7666666666666667, "value": [79.731, 149.55, 0]}], "opacity": [{"frame": 0.25, "value": 0}, {"frame": 0.48333333333333334, "value": 255}, {"frame": 0.7666666666666667, "value": 0}]}}, "城墙回血_6": {"props": {"position": [{"frame": 0.11666666666666667, "value": [-23.486, 54.531, 0]}, {"frame": 0.65, "value": [-23.486, 179.876, 0]}], "opacity": [{"frame": 0.11666666666666667, "value": 0}, {"frame": 0.38333333333333336, "value": 255}, {"frame": 0.65, "value": 0}]}}, "城墙回血_7": {"props": {"position": [{"frame": 0.21666666666666667, "value": [183.12, 60.459, 0]}, {"frame": 0.7166666666666667, "value": [183.12, 162.812, 0]}], "opacity": [{"frame": 0.21666666666666667, "value": 0}, {"frame": 0.45, "value": 255}, {"frame": 0.7166666666666667, "value": 0}]}}, "城墙回血_8": {"props": {"position": [{"frame": 0.25, "value": [332.994, 68.079, 0]}, {"frame": 0.7333333333333333, "value": [332.994, 183.278, 0]}], "opacity": [{"frame": 0.25, "value": 0}, {"frame": 0.48333333333333334, "value": 255}, {"frame": 0.7333333333333333, "value": 0}]}}, "城墙回血_9": {"props": {"position": [{"frame": 0.08333333333333333, "value": [241.546, 29.975, 0]}, {"frame": 0.65, "value": [241.546, 126.4, 0]}], "opacity": [{"frame": 0.08333333333333333, "value": 0}, {"frame": 0.3333333333333333, "value": 255}, {"frame": 0.65, "value": 0}]}}, "粒子/粒子01": {"props": {"position": [{"frame": 0, "value": [-341.547, 39.322, 0]}, {"frame": 0.4666666666666667, "value": [-341.547, 184.27, 0]}], "opacity": [{"frame": 0, "value": 0}, {"frame": 0.21666666666666667, "value": 255}, {"frame": 0.4666666666666667, "value": 0}]}}, "粒子/粒子02": {"props": {"position": [{"frame": 0.18333333333333332, "value": [-248.873, 16.074, 0]}, {"frame": 0.6333333333333333, "value": [-248.873, 143.701, 0]}], "opacity": [{"frame": 0.18333333333333332, "value": 0}, {"frame": 0.38333333333333336, "value": 255}, {"frame": 0.6333333333333333, "value": 0}]}}, "粒子/粒子03": {"props": {"position": [{"frame": 0.13333333333333333, "value": [91.102, 25.18, 0]}, {"frame": 0.6, "value": [91.102, 179.623, 0]}], "opacity": [{"frame": 0.13333333333333333, "value": 0}, {"frame": 0.35, "value": 255}, {"frame": 0.6, "value": 0}]}}, "粒子/粒子04": {"props": {"position": [{"frame": 0.2833333333333333, "value": [-87.992, 56.142, 0]}, {"frame": 0.7166666666666667, "value": [-87.992, 169.913, 0]}], "opacity": [{"frame": 0.2833333333333333, "value": 0}, {"frame": 0.48333333333333334, "value": 255}, {"frame": 0.7166666666666667, "value": 0}]}}, "粒子/粒子05": {"props": {"position": [{"frame": 0.26666666666666666, "value": [304.772, 14.038, 0]}, {"frame": 0.75, "value": [304.772, 185.34, 0]}], "opacity": [{"frame": 0.26666666666666666, "value": 0}, {"frame": 0.48333333333333334, "value": 255}, {"frame": 0.75, "value": 0}]}}, "粒子/粒子06": {"props": {"position": [{"frame": 0.21666666666666667, "value": [138.772, 59.038, 0]}, {"frame": 0.7333333333333333, "value": [138.772, 178.725, 0]}], "opacity": [{"frame": 0.21666666666666667, "value": 0}, {"frame": 0.43333333333333335, "value": 255}, {"frame": 0.7333333333333333, "value": 0}]}}, "粒子/粒子07": {"props": {"position": [{"frame": 0.06666666666666667, "value": [-24.228, 16.038, 0]}, {"frame": 0.5666666666666667, "value": [-24.228, 155.689, 0]}], "opacity": [{"frame": 0.06666666666666667, "value": 0}, {"frame": 0.31666666666666665, "value": 255}, {"frame": 0.5666666666666667, "value": 0}]}}}, "城墙回血_02", 11, [{}, "props", 11, [{"opacity": [{"frame": 0, "value": 0}, {"frame": 0.4, "value": 160}, {"frame": 0.9833333333333333, "value": 0}]}, "scale", 12, [[[{"frame": 0}, "value", 8, [1, 8.127, 4.64, 8.127]], [{"frame": 0.9833333333333333}, "value", 8, [1, 8.127, 6.517, 8.127]]], 11, 11]]]]]]], 0, 0, [], [], []], [[[7, "城墙回血"], [8, "root", [-2], [11, -1, 0]], [9, "sprite", [-5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15], [[12, 0, false, -3, [34]], [13, true, -4, [36], 35]], [0, "24MVL74RhOqINJoHjo0+ow", 1, 0]], [5, "粒子", 2, [-16, -17, -18, -19, -20, -21, -22], [0, "7822XpNYlAHq+s47n7F/l8", 1, 0]], [5, "position", 1, [2], [0, "1768HiGARNaoyAvM2o3PlD", 1, 0]], [1, "城墙回血_1", 2, [[2, -23, [0], 1]], [0, "38Ob15xolMTqS5gAnC38Mk", 1, 0], [5, 66, 68], [-324.927, 75.701, 0, 0, 0, 0, 1, 0.627, 0.627, 0.627]], [1, "城墙回血_2", 2, [[2, -24, [2], 3]], [0, "a8XuYrNadO7L/7IgckgyvV", 1, 0], [5, 66, 68], [-280.049, 41.831, 0, 0, 0, 0, 1, 0.627, 0.627, 0.627]], [1, "城墙回血_3", 2, [[2, -25, [4], 5]], [0, "79KzznsitF+LshbKGtBenf", 1, 0], [5, 66, 68], [-124.249, 29.129, 0, 0, 0, 0, 1, 0.494, 0.494, 0.494]], [1, "城墙回血_4", 2, [[2, -26, [6], 7]], [0, "caegLZ6fdERLOOSiyUSDfa", 1, 0], [5, 66, 68], [-179.373, 65.431, 0, 0, 0, 0, 1, 0.39313, 0.39313, 0.39313]], [1, "城墙回血_5", 2, [[2, -27, [8], 9]], [0, "66rxs8wm1BMq03f2yYgOZ2", 1, 0], [5, 66, 68], [79.731, 85.753, 0, 0, 0, 0, 1, 0.453, 0.453, 0.453]], [1, "城墙回血_6", 2, [[2, -28, [10], 11]], [0, "7bmcjDXodN4rNddMiRBweN", 1, 0], [5, 66, 68], [-23.486, 54.531, 0, 0, 0, 0, 1, 0.627, 0.627, 0.627]], [1, "城墙回血_7", 2, [[2, -29, [12], 13]], [0, "1eZ0kQzGxHtad1nVyghP4w", 1, 0], [5, 66, 68], [183.12, 60.459, 0, 0, 0, 0, 1, 0.627, 0.627, 0.627]], [1, "城墙回血_8", 2, [[2, -30, [14], 15]], [0, "dc+e6gJFZJDZzK/dLd88qF", 1, 0], [5, 66, 68], [332.994, 68.079, 0, 0, 0, 0, 1, 0.627, 0.627, 0.627]], [1, "城墙回血_9", 2, [[2, -31, [16], 17]], [0, "5dnTpix4xPYLDGCvPLFbHi", 1, 0], [5, 66, 68], [241.546, 29.975, 0, 0, 0, 0, 1, 0.627, 0.627, 0.627]], [10, "城墙回血_02", 120, 2, [[3, 1, -32, [18], 19]], [0, "402ibySKVJ+JSxrKWqVJ7X", 1, 0], [4, 4281239316], [5, 103, 36], [0, 0.5, 0.16299677765843185], [0, -8, 0, 0, 0, 0, 1, 8.127, 6.517, 8.127]], [4, "粒子01", 3, [[3, 1, -33, [20], 21]], [0, "ec8lxqvJhL85vB1CVII2HQ", 1, 0], [4, 4282747184], [5, 103, 36], [0, 0.5, 0.16299677765843185], [-341.547, 39.322, 0, 0, 0, 0, 1, 0.042, 1.532, 0.042]], [4, "粒子02", 3, [[3, 1, -34, [22], 23]], [0, "58xL/ro/hD7K0IXvA7yy6q", 1, 0], [4, 4282747184], [5, 103, 36], [0, 0.5, 0.16299677765843185], [-248.873, 16.074, 0, 0, 0, 0, 1, 0.042, 1.532, 0.042]], [4, "粒子03", 3, [[3, 1, -35, [24], 25]], [0, "77Vup4rpxAgJ7SPWzX158g", 1, 0], [4, 4282747184], [5, 103, 36], [0, 0.5, 0.16299677765843185], [91.102, 25.18, 0, 0, 0, 0, 1, 0.042, 1.532, 0.042]], [4, "粒子04", 3, [[3, 1, -36, [26], 27]], [0, "adeeKih9RGDazqsyPQzyQq", 1, 0], [4, 4282747184], [5, 103, 36], [0, 0.5, 0.16299677765843185], [-87.992, 56.142, 0, 0, 0, 0, 1, 0.042, 1.532, 0.042]], [4, "粒子05", 3, [[3, 1, -37, [28], 29]], [0, "f4DO7pWChIpb4Vhgy9gEg/", 1, 0], [4, 4282747184], [5, 103, 36], [0, 0.5, 0.16299677765843185], [304.772, 14.038, 0, 0, 0, 0, 1, 0.042, 1.532, 0.042]], [4, "粒子06", 3, [[3, 1, -38, [30], 31]], [0, "1cTFWe9GBPX63AvyZtkVxt", 1, 0], [4, 4282747184], [5, 103, 36], [0, 0.5, 0.16299677765843185], [138.772, 59.038, 0, 0, 0, 0, 1, 0.042, 1.532, 0.042]], [4, "粒子07", 3, [[3, 1, -39, [32], 33]], [0, "c5VhkyGzNNap+a8X3iHb6i", 1, 0], [4, 4282747184], [5, 103, 36], [0, 0.5, 0.16299677765843185], [-24.228, 16.038, 0, 0, 0, 0, 1, 0.042, 1.532, 0.042]]], 0, [0, 3, 1, 0, -1, 4, 0, 0, 2, 0, 0, 2, 0, -1, 5, 0, -2, 6, 0, -3, 7, 0, -4, 8, 0, -5, 9, 0, -6, 10, 0, -7, 11, 0, -8, 12, 0, -9, 13, 0, -10, 14, 0, -11, 3, 0, -1, 15, 0, -2, 16, 0, -3, 17, 0, -4, 18, 0, -5, 19, 0, -6, 20, 0, -7, 21, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, 0, 15, 0, 0, 16, 0, 0, 17, 0, 0, 18, 0, 0, 19, 0, 0, 20, 0, 0, 21, 0, 4, 1, 2, 5, 4, 39], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 6, -1], [0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 4, 4]]]]