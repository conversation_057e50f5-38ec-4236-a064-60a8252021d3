[1, ["020B8J1VJBuZn9UzVxci9G", "1eZbxOZYtJSawKt5pc8EAD", "09hdjpaJdP+oLFILH3h30k", "2cxcJpJztJdZqxjmalB/VD", "57yIKPncNOyZ6gzv5C3lA2", "80TjjOpiRHK6t36hqLCYDM", "9dfbmikHFEEJCOLVkNtxa4"], ["_textureSetter", "value"], ["cc.SpriteFrame", ["cc.AnimationClip", ["_name", "_duration", "sample", "events", "curveData"], -1, 11]], [[1, 0, 1, 2, 3, 4, 5]], [[[{"name": "落石改金手掌_002", "rect": [2, 2, 318, 242], "offset": [6, 21], "originalSize": [350, 350], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "落石改金手掌_001", "rect": [2, 502, 318, 230], "offset": [6, 15], "originalSize": [350, 350], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "落石改金手掌_003", "rect": [234, 570, 288, 244], "offset": [1, 25], "originalSize": [350, 350], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "落石改金手掌_004", "rect": [2, 246, 292, 254], "offset": [1, 29], "originalSize": [350, 350], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "落石改金手掌_005", "rect": [296, 272, 296, 200], "offset": [1, 1], "originalSize": [350, 350], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "落石改金手掌_006", "rect": [322, 2, 188, 130], "offset": [-4, 2], "originalSize": [350, 350], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[[0, "金手掌_普通_坑", 1.0416666666666667, 24, [{"frame": 0.3333333333333333, "func": "Hit", "params": []}], [{}, "paths", 11, [{}, "图片_特效_落石术_001", 11, [{"props": {"active": [{"frame": 0, "value": false}, {"frame": 0.3333333333333333, "value": true}, {"frame": 0.8333333333333334, "value": false}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0.3333333333333333}, "value", 6, 0], [{"frame": 0.4166666666666667}, "value", 6, 1], [{"frame": 0.5}, "value", 6, 2], [{"frame": 0.5833333333333334}, "value", 6, 3], [{"frame": 0.6666666666666666}, "value", 6, 4], [{"frame": 0.75}, "value", 6, 5]], 11, 11, 11, 11, 11, 11]]]], "烟01", 11, [{}, "props", 11, [{"active": [{"frame": 0, "value": false}, {"frame": 0.3333333333333333, "value": true}], "position": [{"frame": 0.3333333333333333, "value": [-85, 6.7], "motionPath": []}, {"frame": 1.0416666666666667, "value": [-123.2, 14.6, 0]}], "opacity": [{"frame": 0.3333333333333333, "value": 0}, {"frame": 0.625, "value": 150}, {"frame": 1.0416666666666667, "value": 0}], "angle": [{"frame": 0.3333333333333333, "value": 0}, {"frame": 1.0416666666666667, "value": -47.61000000000001}]}, "scale", 12, [[[{"frame": 0.3333333333333333}, "value", 8, [1, 1.408, 1.408, 1.408]], [{"frame": 1.0416666666666667}, "value", 8, [1, 2.112, 2.112, 2.112]]], 11, 11]]], "烟02", 11, [{}, "props", 11, [{"active": [{"frame": 0, "value": false}, {"frame": 0.3333333333333333, "value": true}], "position": [{"frame": 0.3333333333333333, "value": [69.816, -7.892, 0]}, {"frame": 0.875, "value": [94.707, -0.607, 0]}], "opacity": [{"frame": 0.3333333333333333, "value": 0}, {"frame": 0.5833333333333334, "value": 100}, {"frame": 0.875, "value": 0}], "angle": [{"frame": 0.3333333333333333, "value": 83.895}, {"frame": 0.875, "value": 134.452}]}, "scale", 12, [[[{"frame": 0.3333333333333333}, "value", 8, [1, 1.277, 1.277, 1.277]], [{"frame": 0.875}, "value", 8, [1, 1.571, 1.571, 1.571]]], 11, 11]]], "烟03", 11, [{}, "props", 11, [{"active": [{"frame": 0, "value": false}, {"frame": 0.3333333333333333, "value": true}], "position": [{"frame": 0.3333333333333333, "value": [-49.783, -21.249, 0]}, {"frame": 0.9583333333333334, "value": [-60.711, -19.428, 0]}], "angle": [{"frame": 0.3333333333333333, "value": 65.589}, {"frame": 0.9583333333333334, "value": 27.385999999999996}], "opacity": [{"frame": 0.3333333333333333, "value": 0}, {"frame": 0.625, "value": 100}, {"frame": 0.9583333333333334, "value": 0}]}, "scale", 12, [[[{"frame": 0.3333333333333333}, "value", 8, [1, 1.701, 1.701, 1.701]], [{"frame": 0.9583333333333334}, "value", 8, [1, 2.142, 2.142, 2.142]]], 11, 11]]], "烟04", 11, [{}, "props", 11, [{"active": [{"frame": 0, "value": false}, {"frame": 0.3333333333333333, "value": true}], "position": [{"frame": 0.3333333333333333, "value": [-9.106, -13.963, 0]}, {"frame": 1.0416666666666667, "value": [-6.071, -1.214, 0]}], "angle": [{"frame": 0.3333333333333333, "value": 0}, {"frame": 1.0416666666666667, "value": 26.006}], "opacity": [{"frame": 0.3333333333333333, "value": 0}, {"frame": 0.625, "value": 200}, {"frame": 1.0416666666666667, "value": 0}]}, "scale", 12, [[[{"frame": 0.3333333333333333}, "value", 8, [1, 1.663, 1.663, 1.663]], [{"frame": 1.0416666666666667}, "value", 8, [1, 2.176, 2.176, 2.176]]], 11, 11]]], "烟05", 11, [{}, "props", 11, [{"active": [{"frame": 0, "value": false}, {"frame": 0.3333333333333333, "value": true}], "position": [{"frame": 0.3333333333333333, "value": [38.247, -24.891, 0]}, {"frame": 0.875, "value": [63.103, -18.393, 0]}], "angle": [{"frame": 0.3333333333333333, "value": 173.897}, {"frame": 0.875, "value": 214.51999999999998}], "opacity": [{"frame": 0.3333333333333333, "value": 0}, {"frame": 0.625, "value": 90}, {"frame": 0.875, "value": 0}]}, "scale", 12, [[[{"frame": 0.3333333333333333}, "value", 8, [1, 1.154, 1.154, 1.154]], [{"frame": 0.875}, "value", 8, [1, 1.551, 1.551, 1.551]]], 11, 11]]]]]]], 0, 0, [0, 0, 0, 0, 0, 0], [1, 1, 1, 1, 1, 1], [1, 2, 3, 4, 5, 6]]]]