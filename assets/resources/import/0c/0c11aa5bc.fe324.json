[1, ["40xa92UM1I8pin/SSXNVAM", "366qzSmn9KjoPRMGwxGVR8", "66raSe3VhDzo+bnkABZBXL", "bf9TrWlWRI/JHOJ120mFFS", "dc6q5jb/pGRb/RCwhN3ajx", "ad+mhes+FC96//J/9yCUNr", "31UaCWxdFJKa685fUQ5LRa"], ["value", "_textureSetter"], ["cc.SpriteFrame", ["cc.AnimationClip", ["_name", "_duration", "sample", "wrapMode", "curveData"], -1, 11]], [[1, 0, 1, 2, 3, 4, 5]], [[[{"name": "九尾灵狐_技能_004", "rect": [2, 2, 176, 166], "offset": [2, 15], "originalSize": [196, 196], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "九尾灵狐_技能_005", "rect": [180, 2, 102, 122], "offset": [0, -2], "originalSize": [196, 196], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "九尾灵狐_技能_000", "rect": [304, 2, 104, 118], "offset": [-1, -3], "originalSize": [196, 196], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "九尾灵狐_技能_003", "rect": [424, 2, 108, 116], "offset": [0, -3], "originalSize": [196, 196], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "九尾灵狐_技能_001", "rect": [542, 2, 100, 112], "offset": [-1, -5], "originalSize": [196, 196], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[[0, "灵兽_九尾灵狐_技能", 1.0416666666666667, 24, 2, [{}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 0], [{"frame": 0.16666666666666666}, "value", 6, 1], [{"frame": 0.3333333333333333}, "value", 6, 2], [{"frame": 0.5}, "value", 6, 3], [{"frame": 0.6666666666666666}, "value", 6, 4], [{"frame": 0.8333333333333334}, "value", 6, 5], [{"frame": 1}, "value", 6, 6]], 11, 11, 11, 11, 11, 11, 11]]]]]], 0, 0, [0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0], [2, 3, 4, 5, 6, 1, 1]], [[{"name": "九尾灵狐_技能_002", "rect": [768, 2, 104, 108], "offset": [-1, -7], "originalSize": [196, 196], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]]]]