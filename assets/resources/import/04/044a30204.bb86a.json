[1, ["3516MYBWtIIZjlI4n59rob", "9fPIfKbIxKVrk7AgsAVBiL", "71/ZL6rSVC1q1+xCUrjOeV", "51NSCzNIlLkaI613tKZ4xZ", "48l/lb72VKkZ0xL/cQp4Jc", "44jmdOYVpDKJ+ihnN9ENqn", "58aTAUG0VMm5jWupThVFDl", "18+N+uN51NWZ24hgm6jVJv"], ["_textureSetter", "value"], ["cc.SpriteFrame", ["cc.AnimationClip", ["_name", "_duration", "sample", "events", "curveData"], -1, 11]], [[1, 0, 1, 2, 3, 4, 5]], [[[{"name": "天帝之拳_016", "rect": [594, 2, 142, 94], "offset": [-5, -4], "originalSize": [350, 350], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "天帝之拳_014", "rect": [472, 754, 212, 146], "offset": [-1, 5], "originalSize": [350, 350], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "天帝之拳_013", "rect": [498, 528, 236, 224], "offset": [-2, 44], "originalSize": [350, 350], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "天帝之拳_012", "rect": [252, 496, 244, 216], "offset": [-2, 40], "originalSize": [350, 350], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "天帝之拳_015", "rect": [432, 908, 144, 94], "offset": [-5, -4], "originalSize": [350, 350], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "天帝之拳_011", "rect": [2, 668, 236, 208], "offset": [-2, 37], "originalSize": [350, 350], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "天帝之拳_010", "rect": [240, 714, 230, 192], "offset": [-2, 33], "originalSize": [350, 350], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[[0, "天帝之拳_毒拳_坑", 1, 24, [{"frame": 0.3333333333333333, "func": "Hit", "params": []}], [{}, "paths", 11, [{}, "图片_特效_落石术_001", 11, [{"props": {"active": [{"frame": 0, "value": false}, {"frame": 0.3333333333333333, "value": true}, {"frame": 0.9166666666666666, "value": false}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0.3333333333333333}, "value", 6, 0], [{"frame": 0.4166666666666667}, "value", 6, 1], [{"frame": 0.5}, "value", 6, 2], [{"frame": 0.5833333333333334}, "value", 6, 3], [{"frame": 0.6666666666666666}, "value", 6, 4], [{"frame": 0.75}, "value", 6, 5], [{"frame": 0.8333333333333334}, "value", 6, 6]], 11, 11, 11, 11, 11, 11, 11]]]], "烟01", 11, [{}, "props", 11, [{"active": [{"frame": 0, "value": false}, {"frame": 0.3333333333333333, "value": true}], "position": [{"frame": 0.3333333333333333, "value": [-89.622, -0.717, 0]}, {"frame": 1, "value": [-138.622, 14.283, 0]}], "angle": [{"frame": 0.3333333333333333, "value": -93.737}, {"frame": 1, "value": -140.285}], "opacity": [{"frame": 0.3333333333333333, "value": 0}, {"frame": 0.6666666666666666, "value": 150}, {"frame": 1, "value": 0}]}, "scale", 12, [[[{"frame": 0.3333333333333333}, "value", 8, [1, 1.418, 1.418, 1.418]], [{"frame": 1}, "value", 8, [1, 2.188, 2.188, 2.188]]], 11, 11]]], "烟02", 11, [{}, "props", 11, [{"active": [{"frame": 0, "value": false}, {"frame": 0.3333333333333333, "value": true}], "position": [{"frame": 0.3333333333333333, "value": [42.301, -26.528, 0]}, {"frame": 0.875, "value": [54.301, -37.528, 0]}], "angle": [{"frame": 0.3333333333333333, "value": 0}, {"frame": 0.875, "value": 72.819}], "opacity": [{"frame": 0.3333333333333333, "value": 0}, {"frame": 0.5833333333333334, "value": 200}, {"frame": 0.875, "value": 0}]}, "scale", 12, [[[{"frame": 0.3333333333333333}, "value", 8, [1, 1.714, 1.714, 1.714]], [{"frame": 0.875}, "value", 8, [1, 2.407, 2.407, 2.407]]], 11, 11]]], "烟03", 11, [{}, "props", 11, [{"active": [{"frame": 0, "value": false}, {"frame": 0.3333333333333333, "value": true}], "position": [{"frame": 0.3333333333333333, "value": [-63.094, -24.377, 0]}, {"frame": 0.8333333333333334, "value": [-83.094, -37.377, 0]}], "angle": [{"frame": 0.3333333333333333, "value": -109.292}, {"frame": 0.8333333333333334, "value": -176.83800000000002}], "opacity": [{"frame": 0.3333333333333333, "value": 0}, {"frame": 0.5416666666666666, "value": 150}, {"frame": 0.8333333333333334, "value": 0}]}, "scale", 12, [[[{"frame": 0.3333333333333333}, "value", 8, [1, 1.626, 1.626, 1.626]], [{"frame": 0.8333333333333334}, "value", 8, [1, 2.348, 2.348, 2.348]]], 11, 11]]], "烟04", 11, [{}, "props", 11, [{"active": [{"frame": 0, "value": false}, {"frame": 0.3333333333333333, "value": true}], "position": [{"frame": 0.3333333333333333, "value": [5.019, -35.132, 0]}, {"frame": 0.875, "value": [-10.981, -41.132, 0]}], "angle": [{"frame": 0.3333333333333333, "value": 131.284}, {"frame": 0.875, "value": 100.08399999999999}], "opacity": [{"frame": 0.3333333333333333, "value": 0}, {"frame": 0.5833333333333334, "value": 200}, {"frame": 0.875, "value": 0}]}, "scale", 12, [[[{"frame": 0.3333333333333333}, "value", 8, [1, 2.326, 2.326, 2.326]], [{"frame": 0.875}, "value", 8, [1, 3.198, 3.198, 3.198]]], 11, 11]]], "烟05", 11, [{}, "props", 11, [{"active": [{"frame": 0, "value": false}, {"frame": 0.3333333333333333, "value": true}], "position": [{"frame": 0.3333333333333333, "value": [62.566, 6.547, 0]}, {"frame": 0.9583333333333334, "value": [100.566, -8.453, 0]}], "angle": [{"frame": 0.3333333333333333, "value": 0}, {"frame": 0.9583333333333334, "value": 63.395}], "opacity": [{"frame": 0.3333333333333333, "value": 0}, {"frame": 0.6666666666666666, "value": 80}, {"frame": 0.9583333333333334, "value": 0}]}, "scale", 12, [[[{"frame": 0.3333333333333333}, "value", 8, [1, 1.488, 1.488, 1.488]], [{"frame": 0.9583333333333334}, "value", 8, [1, 1.876, 1.876, 1.876]]], 11, 11]]]]]]], 0, 0, [0, 0, 0, 0, 0, 0, 0], [1, 1, 1, 1, 1, 1, 1], [1, 2, 3, 4, 5, 6, 7]]]]