[1, ["69gJwPgkpAp6bERaOocWNh", "b906C629BJOLk9/cJ3amVS", "2ccZSUtY5EJLQFLelj3Roc", "5enTcCUZ5H34kTzxLl957g", "57hbsgQ9JBUIFo3sfZxdmC", "e0/uOiWjlKT5cFP71hZ0zV", "c52AuXNaFKwJQezuCfOo3b", "677C0D6i9E2p6AIlwaM79e", "3eZmILyXJKA78DDVff11g0"], ["value", "_textureSetter"], ["cc.SpriteFrame", ["cc.AnimationClip", ["_name", "_duration", "sample", "wrapMode", "curveData"], -1, 11]], [[1, 0, 1, 2, 3, 4, 5]], [[[{"name": "冰霜鲛人_000", "rect": [1208, 2, 200, 236], "offset": [0, -1], "originalSize": [280, 280], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "冰霜鲛人_006", "rect": [204, 2, 198, 240], "offset": [6, -3], "originalSize": [280, 280], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "冰霜鲛人_002", "rect": [1004, 2, 202, 236], "offset": [-5, -2], "originalSize": [280, 280], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "冰霜鲛人_001", "rect": [1410, 2, 196, 236], "offset": [-5, -1], "originalSize": [280, 280], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[[0, "冰霜鲛人", 1.0416666666666667, 24, 2, [{}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 0], [{"frame": 0.125}, "value", 6, 1], [{"frame": 0.25}, "value", 6, 2], [{"frame": 0.375}, "value", 6, 3], [{"frame": 0.5}, "value", 6, 4], [{"frame": 0.625}, "value", 6, 5], [{"frame": 0.75}, "value", 6, 6], [{"frame": 0.875}, "value", 6, 7], [{"frame": 1}, "value", 6, 8]], 11, 11, 11, 11, 11, 11, 11, 11, 11]]]]]], 0, 0, [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 3, 4, 5, 6, 7, 8, 1, 1]], [[{"name": "冰霜鲛人_005", "rect": [404, 2, 194, 240], "offset": [7, -3], "originalSize": [280, 280], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "冰霜鲛人_007", "rect": [2, 2, 200, 242], "offset": [4, -4], "originalSize": [280, 280], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "冰霜鲛人_004", "rect": [600, 2, 196, 238], "offset": [4, -3], "originalSize": [280, 280], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "冰霜鲛人_003", "rect": [798, 2, 204, 236], "offset": [-4, -3], "originalSize": [280, 280], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]]]]