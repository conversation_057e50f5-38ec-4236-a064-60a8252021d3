[1, ["ecpdLyjvZBwrvm+cedCcQy", "28gRXK8gtJZ4XTak6ocgNY", "164zsNde9HAY5lbszZf3c+", "b9oLE5f/hJarv2863RZIs8"], ["node", "_spriteFrame", "_textureSetter", "root", "data", "_parent", "_defaultClip"], [["cc.Node", ["_name", "_prefab", "_children", "_contentSize", "_components", "_trs", "_parent", "_eulerAngles"], 2, 4, 2, 5, 9, 7, 1, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6]], [[1, 0, 1, 2, 2], [0, 0, 6, 4, 1, 3, 5, 2], [2, 0, 1, 2, 3, 4, 3], [4, 0, 2], [0, 0, 2, 1, 2], [0, 0, 2, 4, 1, 3, 5, 7, 2], [0, 0, 6, 2, 1, 3, 2], [1, 1, 2, 1], [2, 0, 1, 2, 3, 3], [5, 0, 1, 2, 3, 2]], [[[{"name": "滚石术_低级_000", "rect": [166, 184, 62, 112], "offset": [-1, 3], "originalSize": [70, 140], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [2], [3]], [[[3, "滚石_常态"], [4, "root", [-2], [7, -1, 0]], [5, "sprite", [-5, -6, -7, -8, -9], [[8, 0, false, -3, [10]], [9, true, -4, [12], 11]], [0, "24MVL74RhOqINJoHjo0+ow", 1, 0], [5, 190, 54], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [6, "position", 1, [2], [0, "1768HiGARNaoyAvM2o3PlD", 1, 0], [5, 50, 240]], [1, "滚石术_低级_1", 2, [[2, 0, false, -10, [0], 1]], [0, "f34Zpe1s5Jdb3rY4YTs+At", 1, 0], [5, 100, 200], [-88.705, -17.424, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [1, "滚石术_低级_2", 2, [[2, 0, false, -11, [2], 3]], [0, "0e/JBjAv9BwKZn7jTr/hZ+", 1, 0], [5, 100, 200], [-43.964, -17.424, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [1, "滚石术_低级_3", 2, [[2, 0, false, -12, [4], 5]], [0, "d1iTXXO75Jd7XSC9Ra+s1j", 1, 0], [5, 100, 200], [0.454, -17.424, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [1, "滚石术_低级_4", 2, [[2, 0, false, -13, [6], 7]], [0, "6fCSNMOGlG36JBRhJzerB6", 1, 0], [5, 100, 200], [44.683, -17.424, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [1, "滚石术_低级_5", 2, [[2, 0, false, -14, [8], 9]], [0, "cc/PxEIyBNlKz6Nra4oykG", 1, 0], [5, 100, 200], [89.001, -17.424, 0, 0, 0, 0, 1, 0.6, 0.6, 1]]], 0, [0, 3, 1, 0, -1, 3, 0, 0, 2, 0, 0, 2, 0, -1, 4, 0, -2, 5, 0, -3, 6, 0, -4, 7, 0, -5, 8, 0, 0, 4, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 4, 1, 2, 5, 3, 14], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 6, -1], [0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 2]]]]