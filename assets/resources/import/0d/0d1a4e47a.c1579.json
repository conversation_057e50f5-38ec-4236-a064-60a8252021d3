[1, ["828UOE30dLLIoCldtoLK5i"], ["_effectAsset"], [["cc.EffectAsset", ["_name", "shaders", "techniques"], 0], ["cc.Material", ["_name", "_techniqueData"], 2, 11]], [[0, 0, 1, 2, 4], [1, 0, 1, 2]], [[[[0, "blcx-viewMask", [{"hash": 695271725, "record": null, "name": "blcx-viewMask|vs|fs", "glsl3": {"vert": "\n precision highp float;\nuniform CCGlobal {\n  mat4 cc_matView;\n  mat4 cc_matViewInv;\n  mat4 cc_matProj;\n  mat4 cc_matProjInv;\n  mat4 cc_matViewProj;\n  mat4 cc_matViewProjInv;\n  vec4 cc_cameraPos;\n  vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_screenScale;\n};\nuniform CCLocal {\n  mat4 cc_matWorld;\n  mat4 cc_matWorldIT;\n};\nin vec3 a_position;\nin vec4 a_color;\nout vec4 v_color;\n#if USE_TEXTURE\nin vec2 a_uv0;\nout vec2 v_uv0;\n#endif\nvoid main () {\n  vec4 pos = vec4(a_position, 1);\n  #if CC_USE_MODEL\n  pos = cc_matViewProj * cc_matWorld * pos;\n  #else\n  pos = cc_matViewProj * pos;\n  #endif\n  #if USE_TEXTURE\n  v_uv0 = a_uv0;\n  #endif\n  v_color = a_color;\n  gl_Position = pos;\n}", "frag": "\nprecision highp float;\n#if USE_ALPHA_TEST\n  uniform ALPHA_TEST {\n    float alphaThreshold;\n  };\n#endif\n in vec4 v_color;\n #if USE_TEXTURE\n in vec2 v_uv0;\n uniform sampler2D texture;\n #endif\n uniform ARGS {\n   vec2 center;\n   vec2 size;\n   float radius;\n   float feather;\n };\n void main () {\n   vec4 color = v_color;\n   color *= texture(texture, v_uv0);\n   float ratio = size.x / size.y;\n   float dis = distance(vec2(v_uv0.x, v_uv0.y / ratio), vec2(center.x, center.y / ratio));\n   color.a = smoothstep(radius - feather, radius, dis) * color.a;\n   color.a *= v_color.a;\n   gl_FragColor = color;\n }"}, "glsl1": {"vert": "\n precision highp float;\nuniform mat4 cc_matViewProj;\nuniform mat4 cc_matWorld;\nattribute vec3 a_position;\nattribute vec4 a_color;\nvarying vec4 v_color;\n#if USE_TEXTURE\nattribute vec2 a_uv0;\nvarying vec2 v_uv0;\n#endif\nvoid main () {\n  vec4 pos = vec4(a_position, 1);\n  #if CC_USE_MODEL\n  pos = cc_matViewProj * cc_matWorld * pos;\n  #else\n  pos = cc_matViewProj * pos;\n  #endif\n  #if USE_TEXTURE\n  v_uv0 = a_uv0;\n  #endif\n  v_color = a_color;\n  gl_Position = pos;\n}", "frag": "\nprecision highp float;\n#if USE_ALPHA_TEST\n#endif\n varying vec4 v_color;\n #if USE_TEXTURE\n varying vec2 v_uv0;\n uniform sampler2D texture;\n #endif\n uniform vec2 center;\nuniform vec2 size;\nuniform float radius;\nuniform float feather;\n void main () {\n   vec4 color = v_color;\n   color *= texture2D(texture, v_uv0);\n   float ratio = size.x / size.y;\n   float dis = distance(vec2(v_uv0.x, v_uv0.y / ratio), vec2(center.x, center.y / ratio));\n   color.a = smoothstep(radius - feather, radius, dis) * color.a;\n   color.a *= v_color.a;\n   gl_FragColor = color;\n }"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}], "samplers": []}, "locals": {"blocks": [{"name": "CCLocal", "defines": []}], "samplers": []}}, "defines": [{"name": "USE_TEXTURE", "type": "boolean", "defines": []}, {"name": "CC_USE_MODEL", "type": "boolean", "defines": []}, {"name": "USE_ALPHA_TEST", "type": "boolean", "defines": []}], "blocks": [{"name": "ALPHA_TEST", "binding": 0, "members": [{"name": "alphaThreshold", "type": 13, "count": 1}], "defines": ["USE_ALPHA_TEST"]}, {"name": "ARGS", "binding": 1, "members": [{"name": "center", "type": 14, "count": 1}, {"name": "size", "type": 14, "count": 1}, {"name": "radius", "type": 13, "count": 1}, {"name": "feather", "type": 13, "count": 1}], "defines": []}], "samplers": [{"name": "texture", "type": 29, "count": 1, "binding": 30, "defines": ["USE_TEXTURE"]}]}], [{"passes": [{"program": "blcx-viewMask|vs|fs", "blendState": {"targets": [{"blend": true}]}, "rasterizerState": {"cullMode": 0}, "properties": {"texture": {"value": "white", "type": 29}, "alphaThreshold": {"type": 13, "value": [0.5]}, "size": {"type": 14, "value": [960, 640], "editor": {"tooltip": "节点尺寸"}}, "center": {"type": 14, "value": [0.5, 0.5], "editor": {"tooltip": "中心点 (左上角为原点)"}}, "radius": {"type": 13, "value": [0.2], "editor": {"tooltip": "半径 (目标宽度 / 节点宽度)"}}, "feather": {"type": 13, "value": [0.1], "editor": {"tooltip": "边缘虚化宽度"}}}}]}]]], 0, 0, [], [], []], [[[1, "viewMask", [{}, "0", 11, [{"defines": {"USE_TEXTURE": false}}, "props", 11, [{"feather": 0.05}, "size", 8, [0, 2000, 2000]]]]]], 0, 0, [0], [0], [0]]]]