[1, ["92v9VFE+JOEa3fyNHUuOZv", "eaH5Zt/GdN55hjjgtkCPf6", "ecpdLyjvZBwrvm+cedCcQy", "54+F9SC+lGT78X7H82r8nY", "0b2xsPfJJHbItpH+5ILU4R", "93S2CAoEVJfZ+a614OqXvv", "76e35RrulFc6VGn5BgSi12"], ["_parent", "node", "_textureSetter", "_spriteFrame", "root", "data", "_defaultClip"], [["cc.Node", ["_name", "_prefab", "_parent", "_trs", "_children", "_components", "_contentSize"], 2, 4, 1, 7, 2, 9, 5], "cc.SpriteFrame", ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_children", "_prefab", "_contentSize"], 2, 1, 12, 4, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6]], [[2, 0, 1, 2, 2], [0, 0, 2, 1, 3, 2], [5, 0, 1, 2, 3, 4, 3], [3, 0, 2], [0, 0, 4, 1, 2], [0, 0, 2, 4, 5, 1, 6, 3, 2], [0, 0, 2, 5, 1, 6, 3, 2], [4, 0, 1, 2, 3, 4, 2], [2, 1, 2, 1], [6, 0, 1, 2, 3, 2]], [[[{"name": "技能_000", "rect": [144, 2, 72, 36], "offset": [-1, 26], "originalSize": [140, 140], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [1]], [[{"name": "skeleton-animation_0", "rect": [2, 484, 126, 128], "offset": [1, -1], "originalSize": [132, 130], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [1]], [[[3, "红炎灯鬼"], [4, "root", [-2], [8, -1, 0]], [7, "position", 1, [[-3, [1, "脚底_挂点", -4, [0, "e3XqhryhdK36DEUHpAIYKb", 1, 0], [0, -72.415, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "身体_挂点", -5, [0, "00UP0dX9JIxo1O6ykBooM7", 1, 0], [0, -30.469, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "头顶_挂点", -6, [0, "5cp2Ztl1hNKYApZIxC3mBI", 1, 0], [0, 99.28, 0, 0, 0, 0, 1, 1, 1, 1]]], 1, 4, 4, 4], [0, "1768HiGARNaoyAvM2o3PlD", 1, 0], [5, 140, 140]], [5, "sprite", 2, [-9], [[2, 0, false, -7, [2], 3], [9, true, -8, [5, 6, 7], 4]], [0, "24MVL74RhOqINJoHjo0+ow", 1, 0], [5, 189, 186], [-6.325, 15.264, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "技能_000", 3, [[2, 0, false, -10, [0], 1]], [0, "120ZfCWOpNQY8nXO3FmtTo", 1, 0], [5, 200, 200], [6.774, -71.95, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 4, 1, 0, -1, 2, 0, -1, 3, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 1, 3, 0, 1, 3, 0, -1, 4, 0, 1, 4, 0, 5, 1, 10], [0, 0, 0, 0, 0, 0, 0, 0], [-1, 3, -1, 3, 6, -1, -2, -3], [2, 3, 4, 5, 0, 0, 0, 6]]]]