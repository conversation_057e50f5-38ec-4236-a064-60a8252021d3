[1, ["ecpdLyjvZBwrvm+cedCcQy", "78Wy/kwMBB2J8vXoX6HiEQ", "18oash7ZJPZZ7EGM6pA8+u", "beJKeyG6JPRZDIGDbM35qg"], ["node", "_spriteFrame", "_textureSetter", "root", "data", "_parent", "_defaultClip"], [["cc.Node", ["_name", "_prefab", "_children", "_components", "_trs", "_parent", "_contentSize", "_anchorPoint", "_eulerAngles"], 2, 4, 2, 9, 7, 1, 5, 5, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], "cc.SpriteFrame", ["cc.AnimationClip", ["_name", "_duration", "sample", "wrapMode", "events", "curveData"], -2, 11], ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6]], [[1, 0, 1, 2, 2], [2, 0, 1, 2, 3, 4, 3], [0, 0, 5, 3, 1, 6, 7, 4, 8, 2], [4, 0, 1, 2, 3, 4, 5, 6], [5, 0, 2], [0, 0, 2, 1, 2], [0, 0, 2, 3, 1, 4, 2], [0, 0, 5, 2, 1, 6, 2], [0, 0, 5, 3, 1, 6, 7, 4, 2], [1, 1, 2, 1], [2, 0, 1, 2, 3, 3], [6, 0, 1, 2, 3, 2]], [[[[3, "长剑突刺", 0.8333333333333334, 24, 0, [{"frame": 0.16666666666666666, "func": "LoopStart", "params": []}, {"frame": 0.625, "func": "LoopEnd", "params": []}], [{}, "paths", 11, [{}, "01", 11, [{}, "props", 11, [{}, "scale", 12, [[[{"frame": 0}, "value", 8, [1, 1, 0, 1]], [{"frame": 0.08333333333333333}, "value", 8, [1, 1, 1.6, 1]], [{"frame": 0.16666666666666666}, "value", 8, [1, 1, 1.5, 1]], [{"frame": 0.625}, "value", 8, [1, 1, 1.5, 1]], [{"frame": 0.75}, "value", 8, [1, 1, 1.6, 1]], [{"frame": 0.8333333333333334}, "value", 8, [1, 1, 0, 1]]], 11, 11, 11, 11, 11, 11]]], "02", 11, [{}, "props", 11, [{}, "scale", 12, [[[{"frame": 0}, "value", 8, [1, 1, 0, 1]], [{"frame": 0.08333333333333333}, "value", 8, [1, 1, 1.6, 1]], [{"frame": 0.16666666666666666}, "value", 8, [1, 1, 1.5, 1]], [{"frame": 0.625}, "value", 8, [1, 1, 1.5, 1]], [{"frame": 0.75}, "value", 8, [1, 1, 1.6, 1]], [{"frame": 0.8333333333333334}, "value", 8, [1, 1, 0, 1]]], 11, 11, 11, 11, 11, 11]]], "03", 11, [{}, "props", 11, [{}, "scale", 12, [[[{"frame": 0}, "value", 8, [1, 1, 0, 1]], [{"frame": 0.08333333333333333}, "value", 8, [1, 1, 1.6, 1]], [{"frame": 0.16666666666666666}, "value", 8, [1, 1, 1.5, 1]], [{"frame": 0.625}, "value", 8, [1, 1, 1.5, 1]], [{"frame": 0.75}, "value", 8, [1, 1, 1.6, 1]], [{"frame": 0.8333333333333334}, "value", 8, [1, 1, 0, 1]]], 11, 11, 11, 11, 11, 11]]], "04", 11, [{}, "props", 11, [{}, "scale", 12, [[[{"frame": 0}, "value", 8, [1, 1, 0, 1]], [{"frame": 0.08333333333333333}, "value", 8, [1, 1, 1.6, 1]], [{"frame": 0.16666666666666666}, "value", 8, [1, 1, 1.5, 1]], [{"frame": 0.625}, "value", 8, [1, 1, 1.5, 1]], [{"frame": 0.75}, "value", 8, [1, 1, 1.6, 1]], [{"frame": 0.8333333333333334}, "value", 8, [1, 1, 0, 1]]], 11, 11, 11, 11, 11, 11]]], "05", 11, [{}, "props", 11, [{}, "scale", 12, [[[{"frame": 0}, "value", 8, [1, 1, 0, 1]], [{"frame": 0.08333333333333333}, "value", 8, [1, 1, 1.6, 1]], [{"frame": 0.16666666666666666}, "value", 8, [1, 1, 1.5, 1]], [{"frame": 0.625}, "value", 8, [1, 1, 1.5, 1]], [{"frame": 0.75}, "value", 8, [1, 1, 1.6, 1]], [{"frame": 0.8333333333333334}, "value", 8, [1, 1, 0, 1]]], 11, 11, 11, 11, 11, 11]]], "06", 11, [{}, "props", 11, [{}, "scale", 12, [[[{"frame": 0}, "value", 8, [1, 1, 0, 1]], [{"frame": 0.08333333333333333}, "value", 8, [1, 1, 1.6, 1]], [{"frame": 0.16666666666666666}, "value", 8, [1, 1, 1.5, 1]], [{"frame": 0.625}, "value", 8, [1, 1, 1.5, 1]], [{"frame": 0.75}, "value", 8, [1, 1, 1.6, 1]], [{"frame": 0.8333333333333334}, "value", 8, [1, 1, 0, 1]]], 11, 11, 11, 11, 11, 11]]], "07", 11, [{}, "props", 11, [{}, "scale", 12, [[[{"frame": 0}, "value", 8, [1, 1, 0, 1]], [{"frame": 0.08333333333333333}, "value", 8, [1, 1, 1.6, 1]], [{"frame": 0.16666666666666666}, "value", 8, [1, 1, 1.5, 1]], [{"frame": 0.625}, "value", 8, [1, 1, 1.5, 1]], [{"frame": 0.75}, "value", 8, [1, 1, 1.6, 1]], [{"frame": 0.8333333333333334}, "value", 8, [1, 1, 0, 1]]], 11, 11, 11, 11, 11, 11]]]]]]], 0, 0, [], [], []], [[{"name": "图片_特效_长剑突刺_001", "rect": [2, 2, 28, 117], "offset": [0, -1], "originalSize": [42, 119], "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [2], [3]], [[[4, "长剑突刺"], [5, "root", [-2], [9, -1, 0]], [6, "sprite", [-5, -6, -7, -8, -9, -10, -11], [[10, 0, false, -3, [14]], [11, true, -4, [16], 15]], [0, "24MVL74RhOqINJoHjo0+ow", 1, 0], [0, 73, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "position", 1, [2], [0, "1768HiGARNaoyAvM2o3PlD", 1, 0], [5, 720, 520]], [8, "01", 2, [[1, 0, false, -12, [0], 1]], [0, "0bsFlcH89L34drIgxQsauh", 1, 0], [5, 60, 170], [0, 0.5, 0], [0, -73, 0, 0, 0, 0, 1, 1, 1.5, 1]], [2, "02", 2, [[1, 0, false, -13, [2], 3]], [0, "adHIIkOsZGyoPZ0LN7HGm0", 1, 0], [5, 60, 170], [0, 0.5, 0], [-109.624, -74.701, 0, 0, 0, 0.04792582887638327, 0.9988508972446847, 1, 1.5, 1], [1, 0, 0, 5.493999999999999]], [2, "03", 2, [[1, 0, false, -14, [4], 5]], [0, "adQSEAlPpMJqt3f0UXsnvX", 1, 0], [5, 60, 170], [0, 0.5, 0], [-301.618, -75.914, 0, 0, 0, 0.17309813020338513, 0.9849045828505886, 1, 1.5, 1], [1, 0, 0, 19.936]], [2, "04", 2, [[1, 0, false, -15, [6], 7]], [0, "dbXRY0g01GgZfdqM8DvSDu", 1, 0], [5, 60, 170], [0, 0.5, 0], [105.382, -77.914, 0, 0, 0, -0.05007872063146273, 0.9987452737009151, 1, 1.5, 1], [1, 0, 0, -5.741]], [2, "05", 2, [[1, 0, false, -16, [8], 9]], [0, "c7KWX1OZpKU5Rjz7cGqcU5", 1, 0], [5, 60, 170], [0, 0.5, 0], [299.382, -74.914, 0, 0, 0, -0.1806478929080767, 0.9835478324859813, 1, 1.5, 1], [1, 0, 0, -20.814999999999998]], [2, "06", 2, [[1, 0, false, -17, [10], 11]], [0, "66rsx9NR9GFbdoqCqXBBB8", 1, 0], [5, 60, 170], [0, 0.5, 0], [-213.443, -77.432, 0, 0, 0, 0.06815459300628753, 0.9976747723843413, 1, 1.5, 1], [1, 0, 0, 7.816000000000002]], [2, "07", 2, [[1, 0, false, -18, [12], 13]], [0, "fddweXichLA4KIB+xMS6/t", 1, 0], [5, 60, 170], [0, 0.5, 0], [199.475, -78.555, 0, 0, 0, -0.13248128652295676, 0.9911855067146725, 1, 1.5, 1], [1, 0, 0, -15.225999999999999]]], 0, [0, 3, 1, 0, -1, 3, 0, 0, 2, 0, 0, 2, 0, -1, 4, 0, -2, 5, 0, -3, 6, 0, -4, 7, 0, -5, 8, 0, -6, 9, 0, -7, 10, 0, 0, 4, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 4, 1, 2, 5, 3, 18], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 6, -1], [0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 2]]]]