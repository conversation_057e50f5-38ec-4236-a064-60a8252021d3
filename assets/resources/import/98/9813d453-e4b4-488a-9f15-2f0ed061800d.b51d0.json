[1, 0, 0, [["cc.Json<PERSON>set", ["_name", "json"], 1]], [[0, 0, 1, 3]], [[0, "uiIconConfig", {"": ["project.manifest", "app_version.json", "uiIconConfig.json", "test.txt", "img_lucency.png", "version.manifest", "uiEffectConfig.json"], "fgui": ["共用_刘海屏适配.bin", "共用_字体_atlas0.png", "主界面_角色_atlas0.png", "共用_刘海屏适配_atlas0.png", "排行榜.bin", "图标_阵法技能.bin", "每日分享.bin", "排行榜_atlas0.png", "公用_飘字_atlas0.png", "公用_飘字.bin", "登录_进度条.bin", "特效_灵宠召唤_atlas0.png", "图标_灵宠.bin", "皮肤_atlas0.png", "共用弹窗_atlas0.png", "技能_闭关突破.bin", "每日签到.bin", "共用弹窗.bin", "限时活动_atlas0.png", "灵宠_atlas0.png", "技能_闭关突破_atlas0.png", "皮肤.bin", "公用0_atlas0.png", "玩法提示.bin", "图标_buff图标.bin", "图标_技能.bin", "图标_战斗选关_章节场景01.bin", "获得奖励_atlas0.png", "公用特效素材1.bin", "启动.bin", "主界面_门派.bin", "历练.bin", "图标_主城.bin", "共用组件.bin", "图标_阵法技能_atlas0.png", "图标_城墙.bin", "首充礼包.bin", "战斗.bin", "主界面_角色.bin", "GM_atlas0.png", "活动_皮肤礼包.bin", "图标_灵宠_atlas0.png", "公用特效素材1_atlas0.png", "主界面_秘境.bin", "每日签到_atlas0.png", "主界面_商店.bin", "共用_点击特效.bin", "图标_战斗选关_章节场景04.bin", "图标_主城_atlas0.png", "登录_进度条_atlas0.png", "共用加载中_atlas0.png", "图标_战斗选关_章节场景01_atlas0.png", "活动_皮肤礼包_atlas0.png", "GM.bin", "图标_战斗选关_章节场景02.bin", "限时活动.bin", "共用加载中.bin", "每日分享_atlas0.png", "特效_战斗_强敌来袭_atlas0.png", "七日狂欢_atlas0.png", "图标_商店.bin", "主界面_战斗.bin", "主界面_门派_atlas0.png", "提示_技能解锁.bin", "对话弹窗.bin", "购买体力.bin", "图标_buff图标_atlas0.png", "图标_城墙_atlas0.png", "图标_皮肤.bin", "图标_灵宠技能_atlas0.png", "首页.bin", "图标_体力商店.bin", "礼包.bin", "图标_atlas0.png", "图标_主界面.bin", "战斗_主界面.bin", "通关礼包.bin", "活动.bin", "七日狂欢.bin", "登录账号.bin", "提示_技能解锁_atlas0.png", "活动_atlas0.png", "公用0.bin", "图标_战斗选关_章节场景03_atlas0.png", "灵宠.bin", "图标_战斗选关_章节场景05.bin", "领奖.bin", "设置.bin", "主界面_战斗_atlas0.png", "获得奖励.bin", "个人信息.bin", "主界面_商店_atlas0.png", "首充礼包_atlas0.png", "图标_灵宠技能.bin", "购买体力_atlas0.png", "图标_金币商店_atlas0.png", "超值战令.bin", "图标_战斗选关_章节场景03.bin", "战斗_atlas0.png", "战斗_主界面_atlas0.png", "开服冲榜.bin", "邮件_atlas0.png", "图标_技能_atlas0.png", "主界面_秘境_atlas0.png", "font.bin", "开服冲榜_atlas0.png", "领奖_atlas0.png", "图标_战斗选关_章节场景02_atlas0.png", "图标_体力商店_atlas0.png", "共用_点击特效_atlas0.png", "图标_战斗选关_章节场景04_atlas0.png", "特效_战斗_强敌来袭.bin", "礼包_atlas0.png", "图标.bin", "特效_灵宠召唤.bin", "超值战令_atlas0.png", "图标_战斗选关_章节场景05_atlas0.png", "设置_atlas0.png", "登录_atlas0.png", "历练_atlas0.png", "邮件.bin", "图标_金币商店.bin", "通关礼包_atlas0.png", "共用组件_atlas0.png", "登录.bin", "登录账号_atlas0.png", "共用_字体.bin", "首页_atlas0.png", "登录_公用包.bin", "图标_商店_atlas0.png", "图标_皮肤_atlas0.png", "图标_主界面_atlas0.png"], "fguiConfig": ["fguipkginfo.json", "fguiUseImage_UIProject.json"], "image": ["登录启动图.jpg"], "material": ["blcx-2d-sprite.mtl", "viewMask.png", "viewMask.mtl", "blcx-2d-unlit.mtl"], "shader": ["blcx-2d-unlit.effect", "blcx-2d-sprite.effect", "blcx-viewMask.effect", "blcx-sample.effect"], "subproto": ["protoCompiled.json", "protoCache.json"], "塔防/动效/动效_城墙抖动": ["动效_城墙抖动.prefab"], "塔防/动效/动效_城墙抖动/不导出": ["动效_城墙_受伤.anim"], "塔防/场景/地图/不导出": ["地形_材质.mtl"], "塔防/场景/地图/不导出/地形": ["8.jpg", "2.jpg", "11.jpg", "12.jpg", "3.jpg", "6.jpg", "10.jpg", "泥地-前期.jpg", "绿草地-前期.jpg", "1.jpg", "5.jpg", "7.jpg", "13.jpg"], "塔防/场景/地图/地图_场景1": ["场景1.prefab"], "塔防/场景/地图/地图_场景1/不导出": ["地表（70%）.png"], "塔防/场景/地图/地图_场景10": ["场景10.prefab"], "塔防/场景/地图/地图_场景11": ["场景11.prefab"], "塔防/场景/地图/地图_场景12": ["场景12.prefab"], "塔防/场景/地图/地图_场景13": ["场景13.prefab"], "塔防/场景/地图/地图_场景2": ["场景2.prefab"], "塔防/场景/地图/地图_场景3": ["场景3.prefab"], "塔防/场景/地图/地图_场景5": ["场景5.prefab"], "塔防/场景/地图/地图_场景6": ["场景6.prefab"], "塔防/场景/地图/地图_场景7": ["场景7.prefab"], "塔防/场景/地图/地图_场景8": ["场景8.prefab"], "塔防/场景/地图/地图_场景_泥地_前期": ["场景_泥地_前期.prefab"], "塔防/场景/地图/地图_场景_绿草地_前期": ["场景_绿草地_前期.prefab"], "塔防/场景/城墙/城墙_1级": ["城墙_1级.prefab"], "塔防/场景/城墙/城墙_1级/不导出": ["城墙_第一级_03.png", "城墙_第一级_05.png", "城墙_第一级_01.png", "城墙_第一级_02.png", "城墙_第一级_04.png", "场景1_城墙_状态_受伤.anim"], "塔防/场景/城墙/城墙_2级": ["城墙_2级.prefab"], "塔防/场景/城墙/城墙_2级/不导出": ["场景2_城墙_状态3.anim", "城墙_第二级_02.png", "城墙_第二级_04.png", "场景2_城墙_状态5.anim", "场景2_城墙_状态4.anim", "场景2_城墙_状态1.anim", "城墙_第二级_03.png", "城墙_第二级_01.png", "场景2_城墙_状态2.anim", "城墙_第二级_05.png"], "塔防/场景/城墙/城墙_3级": ["城墙_3级.prefab"], "塔防/场景/城墙/城墙_3级/不导出": ["场景3_城墙_状态1.anim", "场景3_城墙_状态2.anim", "城墙_第三级_04.png", "场景3_城墙_状态5.anim", "城墙_第三级_02.png", "场景3_城墙_状态3.anim", "城墙_第三级_05.png", "城墙_第三级_01.png", "场景3_城墙_状态4.anim", "城墙_第三级_03.png"], "塔防/场景/城墙/城墙_4级": ["城墙_4级.prefab"], "塔防/场景/城墙/城墙_4级/不导出": ["城墙_第四级_03.png", "场景1_城墙_状态5.anim", "场景1_城墙_状态2.anim", "城墙_第四级_05.png", "场景1_城墙_状态3.anim", "城墙_第四级_02.png", "场景1_城墙_状态1.anim", "城墙_第四级_04.png", "场景1_城墙_状态4.anim", "城墙_第四级_01.png"], "塔防/场景/城墙/城墙_新": ["城墙_卷轴.prefab", "卷轴墙_1.png"], "塔防/场景/城墙/城墙_新/不导出": ["城墙卷轴.anim"], "塔防/场景/城墙/投影/不导出": ["城墙投影（横向九宫）.png"], "塔防/场景/城墙/血条": ["城墙_血条_红.prefab", "城墙_血条_绿.prefab", "城墙_血条_黄.prefab"], "塔防/场景/城墙/血条/不导出": ["公用_图标_血条03.png", "公用_九宫_血条底.png", "公用_图标_血条02.png", "公用_图标_血条.png", "公用_图标_血条01.png"], "塔防/字体": ["战斗文本_miss.prefab", "字体_飘动.anim", "战斗文本_回复.prefab", "战斗文本_秒杀.prefab", "战斗文本_暴击.prefab", "战斗文本_普通.prefab"], "塔防/字体/原图": ["字体散图.pac"], "塔防/字体/原图/回复字": ["回复字.fnt", "回复字.png"], "塔防/字体/原图/散图": ["数字_战斗_miss.png", "战斗_文字_秒杀.png"], "塔防/字体/原图/普通字": ["普通字.png", "普通字.fnt"], "塔防/字体/原图/暴击字": ["暴击字.fnt", "暴击字.png"], "塔防/怪物子弹": ["冰晶雪女_子弹.prefab", "枯骨鸟_子弹.prefab", "蛟龙_子弹.prefab", "白骨巫女_子弹.prefab", "蛟鱼精_子弹.prefab"], "塔防/怪物子弹/不导出": ["冰晶雪女_子弹.anim", "白骨巫女_子弹.anim"], "塔防/技能特效/_example": ["root _角色.prefab", "root.prefab"], "塔防/技能特效/万剑诀": ["万剑诀.prefab"], "塔防/技能特效/公用技能": ["公用_受击_单体冰冻.prefab", "公用_受击_麻痹.prefab", "公用_眩晕.prefab", "公用_回血.prefab", "公用_受击_爆炸.prefab", "公用_受击_持续燃烧.prefab", "公用_受击_毒爆.prefab"], "塔防/技能特效/公用技能/不导出": ["雷鸣咒_麻痹.anim", "公用_回血.anim", "公用_受击_毒爆.anim", "公用_受击_爆炸.anim", "公用_眩晕.anim", "火球术_受击_持续燃烧.anim"], "塔防/技能特效/剑林": ["剑林.prefab"], "塔防/技能特效/剑气斩": ["剑气斩_普通.prefab", "剑气斩_加强.prefab"], "塔防/技能特效/剑气斩/不导出": ["光剑术_普通.anim", "光剑术_加强.anim"], "塔防/技能特效/千羽云鹤_buff": ["千羽云鹤_buff.prefab"], "塔防/技能特效/千羽云鹤_飞剑": ["千羽云鹤_飞剑.prefab"], "塔防/技能特效/召唤出现": ["召唤出现.prefab"], "塔防/技能特效/召唤出现/不导出": ["召唤出现.anim"], "塔防/技能特效/天帝之拳": ["天帝之拳_毒拳_石头.prefab", "天帝之拳_普通_石头.prefab", "天帝之拳_普通_坑.prefab", "天帝之拳_毒拳_坑.prefab"], "塔防/技能特效/天帝之拳/不导出": ["天帝之拳_毒拳_坑.anim", "天帝之拳_普通_坑.anim", "天帝之拳_普通_石头.anim", "天帝之拳_毒拳_石头.anim"], "塔防/技能特效/天残脚": ["天残脚_普通_坑.prefab", "天残脚_普通_石头.prefab"], "塔防/技能特效/天残脚/不导出": ["天残脚_普通_坑.anim", "天残脚_普通_石头.anim"], "塔防/技能特效/天雷网": ["天雷网.prefab"], "塔防/技能特效/天雷网/不导出": ["天雷网.anim"], "塔防/技能特效/寒冰突刺": ["寒冰突刺_范围冰冻_出现.prefab", "寒冰突刺_范围冰冻_消失.prefab", "寒冰突刺_范围冰冻_循环.prefab"], "塔防/技能特效/寒冰突刺/不导出": ["冰封术_范围冰冻_消失.anim", "冰封术_范围冰冻_出现.anim", "冰封术_范围冰冻_循环.anim"], "塔防/技能特效/寒冰箭": ["寒冰箭_分裂子弹.prefab", "寒冰箭_子弹.prefab", "寒冰箭_受击.prefab", "寒冰箭_三菱冰箭.prefab", "寒冰箭_冰冻.prefab"], "塔防/技能特效/寒冰箭/不导出": ["寒冰术_子弹.anim", "寒冰术_受击.anim", "寒冰术_三菱冰箭.anim", "寒冰术_冰冻.anim"], "塔防/技能特效/巨石突刺": ["巨石突刺.prefab"], "塔防/技能特效/巨石突刺/不导出": ["巨石突刺.anim"], "塔防/技能特效/干饭妖分裂": ["干饭妖分裂_boss.prefab", "干饭妖分裂.prefab"], "塔防/技能特效/干饭妖分裂/不导出": ["干饭妖分裂.anim"], "塔防/技能特效/御剑术": ["御剑术_子弹.prefab", "御剑术_受击.prefab", "御剑术_火焰飞剑.prefab"], "塔防/技能特效/御剑术/不导出": ["御剑术_火焰飞剑.anim", "飞剑术_受击.anim"], "塔防/技能特效/怪物护盾": ["怪物护盾.prefab"], "塔防/技能特效/惊雷咒": ["惊雷咒_电磁场.prefab", "惊雷咒_雷电.prefab", "惊雷咒_爆炸.prefab", "惊雷咒_天雷轰顶.prefab", "惊雷咒_小电球.prefab"], "塔防/技能特效/惊雷咒/不导出": ["落雷术_小电球.anim", "落雷术_爆炸.anim", "落雷术_电磁场.anim", "落雷术_雷电.anim", "惊雷咒_天雷轰顶.anim"], "塔防/技能特效/投石术": ["投石术_石头.prefab", "投石术_坑.prefab"], "塔防/技能特效/投石术/不导出": ["投石术_坑.anim", "投石术_石头.anim"], "塔防/技能特效/旋风术": ["旋风术.prefab", "闪电旋风.prefab"], "塔防/技能特效/旋风术/不导出": ["闪电旋风.anim", "旋风术.anim"], "塔防/技能特效/死亡特效": ["死亡特效.prefab"], "塔防/技能特效/死亡特效/不导出": ["死亡特效.anim"], "塔防/技能特效/毒云": ["毒云.prefab"], "塔防/技能特效/毒云/不导出": ["毒云.anim"], "塔防/技能特效/毒墙": ["毒墙.prefab", "荆棘.prefab"], "塔防/技能特效/毒墙/不导出": ["毒墙.anim"], "塔防/技能特效/滚石": ["滚石_常态.prefab", "滚石_着火.prefab"], "塔防/技能特效/滚石/不导出": ["巨木术_常态.anim", "巨木术_着火.anim"], "塔防/技能特效/火墙": ["火墙.prefab"], "塔防/技能特效/火墙/不导出": ["火墙.anim"], "塔防/技能特效/火焰弹": ["火焰弹_溅射子弹.prefab", "火焰弹_子弹_低级.prefab", "火焰弹_受击_高级.prefab", "火焰弹_受击_低级.prefab", "火焰弹_子弹_高级.prefab"], "塔防/技能特效/火焰弹/不导出": ["火球术_受击_高级.anim", "火球术_子弹_高级.anim", "火球术_子弹_低级.anim", "火球术_受击_低级.anim"], "塔防/技能特效/落石术": ["落石术_普通_石头.prefab", "落石术_普通_坑.prefab", "落石术_燃烧陨石_石头.prefab", "落石术_燃烧陨石_坑.prefab", "落石术_坑.prefab"], "塔防/技能特效/落石术/不导出": ["落石术_燃烧陨石_坑.anim", "落石术_普通_石头.anim", "落石术_燃烧陨石_石头.anim", "落石术_普通_坑.anim", "落石术_坑.anim"], "塔防/技能特效/连环闪电": ["连环闪电_电链.prefab"], "塔防/技能特效/连环闪电/不导出": ["雷鸣咒_电链.anim"], "塔防/技能特效/金手掌": ["金手掌_普通_坑.prefab", "金手掌_普通_石头.prefab"], "塔防/技能特效/金手掌/不导出": ["金手掌_普通_石头.anim", "金手掌_普通_坑.anim"], "塔防/技能特效/长剑突刺": ["长剑突刺.prefab"], "塔防/技能特效/长剑突刺/不导出": ["长剑突刺.anim"], "塔防/技能特效/闪电球": ["闪电球_黑洞.prefab", "闪电球.prefab"], "塔防/技能特效/闪电球/不导出": ["闪电球.anim", "闪电球_黑洞.anim"], "塔防/技能特效/阵法陷阱": ["阵法陷阱.prefab"], "塔防/技能特效/阵法陷阱/不导出": ["阵法陷阱.anim"], "塔防/技能特效/风刃陷阱": ["风刃陷阱.prefab"], "塔防/技能特效/风刃陷阱/不导出": ["风刃陷阱.anim"], "塔防/技能特效/风暴术": ["风暴术_普通.prefab", "风暴术_闪电风暴.prefab"], "塔防/技能特效/风暴术/不导出": ["风暴术_闪电风暴.anim", "风暴术_普通.anim"], "塔防/技能特效/鬼火爆炸": ["鬼火爆炸.prefab"], "塔防/技能特效/鬼火爆炸/不导出": ["鬼火爆炸.anim"], "塔防/角色/主角": ["主角.prefab"], "塔防/角色/主角/不导出": ["主角.anim"], "塔防/角色/主角皮肤_冰霜鲛人": ["冰霜鲛人.prefab"], "塔防/角色/主角皮肤_冰霜鲛人/不导出": ["冰霜鲛人.anim"], "塔防/角色/主角皮肤_橙色土域灵狐": ["橙色土域灵狐.prefab"], "塔防/角色/主角皮肤_橙色土域灵狐/不导出": ["橙色土域灵狐.anim"], "塔防/角色/主角皮肤_炎龙帝君": ["炎龙帝君.prefab"], "塔防/角色/主角皮肤_炎龙帝君/不导出": ["炎龙帝君.anim"], "塔防/角色/主角皮肤_粉色土域灵狐": ["粉色土域灵狐.prefab"], "塔防/角色/主角皮肤_粉色土域灵狐/不导出": ["粉色土域灵狐.anim"], "塔防/角色/主角皮肤_绿色土域灵狐": ["绿色土域灵狐.prefab"], "塔防/角色/主角皮肤_绿色土域灵狐/不导出": ["绿色土域灵狐.anim"], "塔防/角色/亡魂鬼士": ["亡魂鬼士.prefab"], "塔防/角色/亡魂鬼士/不导出": ["亡魂鬼士_攻击.anim", "亡魂鬼士_移动.anim"], "塔防/角色/冰晶雪女": ["冰晶雪女.prefab"], "塔防/角色/冰晶雪女/不导出": ["冰晶雪女_攻击.anim", "冰晶雪女_移动.anim"], "塔防/角色/冰鬼": ["冰鬼.prefab"], "塔防/角色/冰鬼/不导出": ["冰鬼_移动.anim", "冰鬼_攻击.anim"], "塔防/角色/地灵蛛": ["地灵蛛.prefab"], "塔防/角色/地灵蛛/不导出": ["地灵蛛_攻击.anim", "地灵蛛_移动.anim"], "塔防/角色/干饭妖": ["干饭妖.prefab"], "塔防/角色/干饭妖/不导出": ["干饭妖_移动.anim", "干饭妖_攻击.anim"], "塔防/角色/幽冥鬼火": ["幽冥鬼火.prefab"], "塔防/角色/幽冥鬼火/不导出": ["幽冥鬼火_攻击.anim", "幽冥鬼火_移动.anim"], "塔防/角色/搬山巨猿": ["搬山巨猿.prefab"], "塔防/角色/搬山巨猿/不导出": ["搬山巨猿_攻击.anim", "搬山巨猿_移动.anim"], "塔防/角色/暗影白骨": ["暗影白骨.prefab"], "塔防/角色/暗影白骨/不导出": ["暗影白骨_攻击.anim", "暗影白骨_移动.anim"], "塔防/角色/木甲鬼士": ["木甲鬼士.prefab"], "塔防/角色/木甲鬼士/不导出": ["木甲鬼士_移动.anim", "木甲鬼士_攻击.anim"], "塔防/角色/枯骨鸟": ["枯骨鸟.prefab"], "塔防/角色/枯骨鸟/不导出": ["枯骨鸟_移动.anim", "枯骨鸟_攻击.anim"], "塔防/角色/灯精": ["灯精.prefab"], "塔防/角色/灯精/不导出": ["灯精_攻击.anim", "灯精_移动.anim"], "塔防/角色/灵兽_九尾灵狐": ["灵兽_九尾灵狐.prefab"], "塔防/角色/灵兽_九尾灵狐/不导出": ["灵兽_九尾灵狐_普攻.anim", "灵兽_九尾灵狐_移动.anim", "灵兽_九尾灵狐_技能.anim"], "塔防/角色/灵兽_千羽云鹤": ["灵兽_千羽云鹤.prefab"], "塔防/角色/灵兽_千羽云鹤/不导出": ["灵兽_千羽云鹤_技能.anim", "灵兽_千羽云鹤_移动.anim", "灵兽_千羽云鹤_普攻.anim"], "塔防/角色/灵兽_思雪": ["灵兽_思雪.prefab"], "塔防/角色/灵兽_思雪/不导出": ["灵兽_思雪_移动.anim", "灵兽_思雪_技能.anim", "灵兽_思雪_普攻.anim"], "塔防/角色/灵兽_貔貅": ["灵兽_貔貅.prefab"], "塔防/角色/灵兽_貔貅/不导出": ["灵兽_貔貅_移动.anim", "灵兽_貔貅_普攻.anim", "灵兽_貔貅_技能.anim"], "塔防/角色/灵兽_青龙": ["灵兽_青龙.prefab"], "塔防/角色/灵兽_青龙/不导出": ["灵兽_青龙_普攻.anim", "灵兽_青龙_技能.anim", "灵兽_青龙_移动.anim"], "塔防/角色/灵兽_鸩": ["灵兽_鸩.prefab"], "塔防/角色/灵兽_鸩/不导出": ["灵兽_鸩_技能.anim", "灵兽_鸩_普攻.anim", "灵兽_鸩_移动.anim"], "塔防/角色/狐狸": ["狐狸.prefab"], "塔防/角色/狐狸/不导出": ["狐狸_移动.anim", "狐狸_攻击.anim"], "塔防/角色/白骨": ["白骨.prefab"], "塔防/角色/白骨/不导出": ["白骨_攻击.anim", "白骨_移动.anim"], "塔防/角色/白骨女巫": ["白骨女巫.prefab"], "塔防/角色/白骨女巫/不导出": ["白骨女巫_攻击.anim", "白骨女巫_移动.anim"], "塔防/角色/白骨战士": ["白骨战士.prefab"], "塔防/角色/白骨战士/不导出": ["白骨战士_移动.anim", "白骨战士_攻击.anim"], "塔防/角色/白骨邪神": ["白骨邪神.prefab"], "塔防/角色/白骨邪神/不导出": ["白骨邪神_攻击02.anim", "白骨邪神_移动.anim", "白骨邪神_消失.anim", "白骨邪神_攻击01.anim"], "塔防/角色/盾甲白骨": ["盾甲白骨.prefab"], "塔防/角色/盾甲白骨/不导出": ["盾甲白骨_攻击.anim", "盾甲白骨_移动.anim"], "塔防/角色/石剑魔": ["石剑魔.prefab"], "塔防/角色/石剑魔/不导出": ["石剑魔_攻击.anim", "石剑魔_移动.anim"], "塔防/角色/米糕精": ["米糕精.prefab"], "塔防/角色/米糕精/不导出": ["米糕精_移动.anim", "米糕精_攻击.anim"], "塔防/角色/红炎灯鬼": ["红炎灯鬼.prefab"], "塔防/角色/红炎灯鬼/不导出": ["红炎灯鬼_移动.anim", "红炎灯鬼_攻击.anim"], "塔防/角色/蛟龙": ["蛟龙.prefab"], "塔防/角色/蛟龙/不导出": ["蛟龙_攻击.anim", "蛟龙_移动.anim"], "塔防/角色/蝎子": ["蝎子.prefab"], "塔防/角色/蝎子/不导出": ["蝎子_移动.anim", "蝎子_攻击.anim"], "塔防/角色/角色投影": ["角色投影.prefab"], "塔防/角色/铁石猪": ["铁石猪.prefab"], "塔防/角色/铁石猪/不导出": ["铁石猪_攻击.anim", "铁石猪_移动.anim"], "塔防/角色/镇墓玄龟": ["镇墓玄龟.prefab"], "塔防/角色/镇墓玄龟/不导出": ["镇墓玄龟_攻击.anim", "镇墓玄龟_移动.anim"], "塔防/角色/青木精": ["青木精.prefab"], "塔防/角色/青木精/不导出": ["青木精_攻击.anim", "青木精_移动.anim"], "塔防/角色/青藤侍女": ["青藤侍女.prefab"], "塔防/角色/青藤侍女/不导出": ["青藤侍女_移动.anim", "青藤侍女_攻击.anim"], "塔防/角色/青藤鬼后": ["青藤鬼后.prefab"], "塔防/角色/青藤鬼后/不导出": ["青藤鬼后_攻击.anim", "青藤鬼后_移动.anim"], "塔防/角色/青蛇": ["青蛇.prefab"], "塔防/角色/青蛇/不导出": ["青蛇_攻击.anim", "青蛇_移动.anim"], "塔防/角色/风蝇君王": ["风蝇君王.prefab"], "塔防/角色/风蝇君王/不导出": ["风蝇君王_移动.anim", "风蝇君王_攻击.anim"], "塔防/角色/风蝇虫": ["风蝇虫.prefab"], "塔防/角色/风蝇虫/不导出": ["风蝇虫_移动.anim", "风蝇虫_攻击.anim"], "塔防/角色/食灵": ["食灵.prefab"], "塔防/角色/食灵/不导出": ["食灵_移动.anim", "食灵_攻击.anim"], "塔防/角色/鲛鱼精": ["鲛鱼精.prefab"], "塔防/角色/鲛鱼精/不导出": ["鲛鱼精_移动.anim", "鲛鱼精_攻击.anim"], "塔防/角色/鸟精": ["鸟精.prefab"], "塔防/角色/鸟精/不导出": ["鸟精_攻击.anim", "鸟精_移动.anim"], "塔防/音效": ["角色背景音乐.mp3", "商店背景音乐.mp3", "登录背景音乐.mp3", "关卡背景音乐.mp3", "战斗背景音乐.mp3", "获得铜钱音效.mp3", "挑战背景音乐.mp3", "战斗失败音效.mp3", "战斗胜利音效.mp3", "点击音效.mp3", "获得奖励音效.mp3"], "塔防/音效/技能音效": ["惊雷咒.mp3", "滚石.mp3", "天雷网.mp3", "火焰弹.mp3", "一声狮吼.mp3", "闪电球.mp3", "寒冰箭受击.mp3", "寒冰突刺.mp3", "滚石受击.mp3", "万剑诀.mp3", "寒冰箭.mp3", "强敌来袭.mp3", "连环闪电.mp3", "风暴术.mp3", "落石术.mp3", "技能转盘.mp3", "巨石突刺.mp3", "旋风术.mp3", "御剑术.mp3", "剑气斩.mp3", "火焰弹受击.mp3", "御剑术受击.mp3"], "塔防_新/技能特效": ["此文件夹放新版技能特效.txt"], "塔防_新/技能特效/城墙回血": ["城墙回血.prefab"], "塔防_新/技能特效/城墙回血/不导出": ["城墙回血.anim"], "塔防_新/技能特效/城墙破碎": ["城墙破碎.prefab"], "塔防_新/技能特效/城墙破碎/不导出": ["城墙破碎.anim"], "塔防_新/技能特效/天雷网": ["天雷网_新.prefab"], "塔防_新/技能特效/天雷网/不导出": ["天雷网_新.anim"]}]], 0, 0, [], [], []]