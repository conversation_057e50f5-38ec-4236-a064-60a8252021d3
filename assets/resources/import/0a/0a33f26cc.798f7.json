[1, ["c7bePJJWJB2pFMRABkKMh+", "e4CaBhrYNDiq+lvGek2DxT", "c7mtDMAPpOaotGh5tduee+", "5akPDJTcBJ7ZhCIWNQU2K8", "c4FzCaDrBB25iZFXs0qPqk"], ["value", "_textureSetter"], ["cc.SpriteFrame", ["cc.AnimationClip", ["_name", "_duration", "sample", "wrapMode", "curveData"], -1, 11]], [[1, 0, 1, 2, 3, 4, 5]], [[[{"name": "毒云_001", "rect": [2, 138, 136, 94], "offset": [-6, -1], "originalSize": [210, 210], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[[0, "毒云", 1.0416666666666667, 24, 2, [{}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 0], [{"frame": 0.125}, "value", 6, 1], [{"frame": 0.25}, "value", 6, 2], [{"frame": 0.375}, "value", 6, 3], [{"frame": 0.5}, "value", 6, 4], [{"frame": 0.625}, "value", 6, 5], [{"frame": 0.75}, "value", 6, 6], [{"frame": 0.875}, "value", 6, 7], [{"frame": 1}, "value", 6, 8]], 11, 11, 11, 11, 11, 11, 11, 11, 11]]], "paths", 11, [{}, "01", 11, [{}, "props", 11, [{"position": [{"frame": 0, "value": [21.992, -23.946, 0]}, {"frame": 0.6666666666666666, "value": [59.178, -47.297, 0]}], "angle": [{"frame": 0, "value": 0}, {"frame": 0.6666666666666666, "value": 45.903999999999996}], "opacity": [{"frame": 0, "value": 0}, {"frame": 0.25, "value": 150}, {"frame": 0.6666666666666666, "value": 0}]}, "scale", 12, [[[{"frame": 0}, "value", 8, [1, 1.54375, 1.54375, 1.54375]], [{"frame": 0.6666666666666666}, "value", 8, [1, 2.093, 2.093, 2.093]]], 11, 11]]], "02", 11, [{}, "props", 11, [{"position": [{"frame": 0.125, "value": [-39.695, 1.757, 0]}, {"frame": 0.75, "value": [-78.233, 20.578, 0]}], "angle": [{"frame": 0.125, "value": 165.485}, {"frame": 0.75, "value": 105.805}], "opacity": [{"frame": 0, "value": 0}, {"frame": 0.125, "value": 0}, {"frame": 0.375, "value": 150}, {"frame": 0.75, "value": 0}]}, "scale", 12, [[[{"frame": 0.125}, "value", 8, [1, 1.317, 1.317, 1.317]], [{"frame": 0.75}, "value", 8, [1, 1.819, 1.819, 1.819]]], 11, 11]]], "03", 11, [{}, "props", 11, [{"opacity": [{"frame": 0, "value": 0}, {"frame": 0.25, "value": 0}, {"frame": 0.5416666666666666, "value": 150}, {"frame": 0.9166666666666666, "value": 0}], "position": [{"frame": 0.25, "value": [16.852, 23.605, 0]}, {"frame": 0.9166666666666666, "value": [44.635, 32.568, 0]}], "angle": [{"frame": 0.25, "value": -201.94899999999998}, {"frame": 0.9166666666666666, "value": -260.757}]}, "scale", 12, [[[{"frame": 0.25}, "value", 8, [1, 1.652, 1.652, 1.652]], [{"frame": 0.9166666666666666}, "value", 8, [1, 1.963, 1.963, 1.963]]], 11, 11]]], "04", 11, [{}, "props", 11, [{"opacity": [{"frame": 0, "value": 0}, {"frame": 0.16666666666666666, "value": 0}, {"frame": 0.4583333333333333, "value": 150}, {"frame": 0.8333333333333334, "value": 0}], "position": [{"frame": 0.16666666666666666, "value": [-22.988, -34.226, 0]}, {"frame": 0.8333333333333334, "value": [-38.224, -51.254, 0]}], "angle": [{"frame": 0.16666666666666666, "value": -201.94899999999998}, {"frame": 0.8333333333333334, "value": -271.907}]}, "scale", 12, [[[{"frame": 0.16666666666666666}, "value", 8, [1, 1.052, 1.052, 1.052]], [{"frame": 0.8333333333333334}, "value", 8, [1, 1.324, 1.324, 1.324]]], 11, 11]]]]]]], 0, 0, [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 3, 4, 1, 2, 3, 4, 1, 1]], [[{"name": "毒云_002", "rect": [2, 2, 134, 100], "offset": [-5, 1], "originalSize": [210, 210], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "毒云_000", "rect": [104, 2, 138, 92], "offset": [-6, 0], "originalSize": [210, 210], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "毒云_003", "rect": [150, 96, 138, 88], "offset": [-6, -4], "originalSize": [210, 210], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]]]]