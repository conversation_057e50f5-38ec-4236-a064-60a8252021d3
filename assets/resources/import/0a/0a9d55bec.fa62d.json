[1, ["3516MYBWtIIZjlI4n59rob", "1cT639u5hHlZKXQrwerhLz", "8419vI+hNMFZFYtgU08ceM", "abijftLORMooRX9BW3DxlE", "3cG/NQs8JNZriNtP+5FTfe", "5e/bkIkxpEyoCANI13mV+W", "33Z9tyUHNE26XqNU55LEnh", "8a6G5o+r1AU4B5qvvscc28"], ["_textureSetter", "value"], ["cc.SpriteFrame", ["cc.AnimationClip", ["_name", "_duration", "sample", "events", "curveData"], -1, 11]], [[1, 0, 1, 2, 3, 4, 5]], [[[{"name": "天帝之拳_001", "rect": [2, 450, 248, 216], "offset": [-1, 22], "originalSize": [350, 350], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "天帝之拳_006", "rect": [578, 290, 236, 158], "offset": [0, -3], "originalSize": [350, 350], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "天帝之拳_004", "rect": [300, 2, 292, 254], "offset": [1, 29], "originalSize": [350, 350], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "天帝之拳_005", "rect": [2, 2, 296, 200], "offset": [1, 1], "originalSize": [350, 350], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "天帝之拳_002", "rect": [292, 258, 284, 236], "offset": [1, 23], "originalSize": [350, 350], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "天帝之拳_007", "rect": [594, 98, 190, 126], "offset": [0, -3], "originalSize": [350, 350], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[[0, "天帝之拳_普通_坑", 1.0416666666666667, 24, [{"frame": 0.3333333333333333, "func": "Hit", "params": []}], [{}, "paths", 11, [{}, "图片_特效_落石术_001", 11, [{"props": {"active": [{"frame": 0, "value": false}, {"frame": 0.3333333333333333, "value": true}, {"frame": 0.9166666666666666, "value": false}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0.3333333333333333}, "value", 6, 0], [{"frame": 0.4166666666666667}, "value", 6, 1], [{"frame": 0.5}, "value", 6, 2], [{"frame": 0.5833333333333334}, "value", 6, 3], [{"frame": 0.6666666666666666}, "value", 6, 4], [{"frame": 0.75}, "value", 6, 5], [{"frame": 0.8333333333333334}, "value", 6, 6]], 11, 11, 11, 11, 11, 11, 11]]]], "烟01", 11, [{}, "props", 11, [{"active": [{"frame": 0, "value": false}, {"frame": 0.3333333333333333, "value": true}], "position": [{"frame": 0.3333333333333333, "value": [-85, 6.7], "motionPath": []}, {"frame": 1.0416666666666667, "value": [-123.2, 14.6, 0]}], "opacity": [{"frame": 0.3333333333333333, "value": 0}, {"frame": 0.625, "value": 150}, {"frame": 1.0416666666666667, "value": 0}], "angle": [{"frame": 0.3333333333333333, "value": 0}, {"frame": 1.0416666666666667, "value": -47.61000000000001}]}, "scale", 12, [[[{"frame": 0.3333333333333333}, "value", 8, [1, 1.408, 1.408, 1.408]], [{"frame": 1.0416666666666667}, "value", 8, [1, 2.112, 2.112, 2.112]]], 11, 11]]], "烟02", 11, [{}, "props", 11, [{"active": [{"frame": 0, "value": false}, {"frame": 0.3333333333333333, "value": true}], "position": [{"frame": 0.3333333333333333, "value": [69.816, -7.892, 0]}, {"frame": 0.875, "value": [94.707, -0.607, 0]}], "opacity": [{"frame": 0.3333333333333333, "value": 0}, {"frame": 0.5833333333333334, "value": 100}, {"frame": 0.875, "value": 0}], "angle": [{"frame": 0.3333333333333333, "value": 83.895}, {"frame": 0.875, "value": 134.452}]}, "scale", 12, [[[{"frame": 0.3333333333333333}, "value", 8, [1, 1.277, 1.277, 1.277]], [{"frame": 0.875}, "value", 8, [1, 1.571, 1.571, 1.571]]], 11, 11]]], "烟03", 11, [{}, "props", 11, [{"active": [{"frame": 0, "value": false}, {"frame": 0.3333333333333333, "value": true}], "position": [{"frame": 0.3333333333333333, "value": [-49.783, -21.249, 0]}, {"frame": 0.9583333333333334, "value": [-60.711, -19.428, 0]}], "angle": [{"frame": 0.3333333333333333, "value": 65.589}, {"frame": 0.9583333333333334, "value": 27.385999999999996}], "opacity": [{"frame": 0.3333333333333333, "value": 0}, {"frame": 0.625, "value": 100}, {"frame": 0.9583333333333334, "value": 0}]}, "scale", 12, [[[{"frame": 0.3333333333333333}, "value", 8, [1, 1.701, 1.701, 1.701]], [{"frame": 0.9583333333333334}, "value", 8, [1, 2.142, 2.142, 2.142]]], 11, 11]]], "烟04", 11, [{}, "props", 11, [{"active": [{"frame": 0, "value": false}, {"frame": 0.3333333333333333, "value": true}], "position": [{"frame": 0.3333333333333333, "value": [-9.106, -13.963, 0]}, {"frame": 1.0416666666666667, "value": [-6.071, -1.214, 0]}], "angle": [{"frame": 0.3333333333333333, "value": 0}, {"frame": 1.0416666666666667, "value": 26.006}], "opacity": [{"frame": 0.3333333333333333, "value": 0}, {"frame": 0.625, "value": 200}, {"frame": 1.0416666666666667, "value": 0}]}, "scale", 12, [[[{"frame": 0.3333333333333333}, "value", 8, [1, 1.663, 1.663, 1.663]], [{"frame": 1.0416666666666667}, "value", 8, [1, 2.176, 2.176, 2.176]]], 11, 11]]], "烟05", 11, [{}, "props", 11, [{"active": [{"frame": 0, "value": false}, {"frame": 0.3333333333333333, "value": true}], "position": [{"frame": 0.3333333333333333, "value": [38.247, -24.891, 0]}, {"frame": 0.875, "value": [63.103, -18.393, 0]}], "angle": [{"frame": 0.3333333333333333, "value": 173.897}, {"frame": 0.875, "value": 214.51999999999998}], "opacity": [{"frame": 0.3333333333333333, "value": 0}, {"frame": 0.625, "value": 90}, {"frame": 0.875, "value": 0}]}, "scale", 12, [[[{"frame": 0.3333333333333333}, "value", 8, [1, 1.154, 1.154, 1.154]], [{"frame": 0.875}, "value", 8, [1, 1.551, 1.551, 1.551]]], 11, 11]]]]]]], 0, 0, [0, 0, 0, 0, 0, 0, 0], [1, 1, 1, 1, 1, 1, 1], [1, 2, 3, 4, 5, 6, 7]], [[{"name": "天帝之拳_003", "rect": [2, 204, 288, 244], "offset": [1, 25], "originalSize": [350, 350], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]]]]