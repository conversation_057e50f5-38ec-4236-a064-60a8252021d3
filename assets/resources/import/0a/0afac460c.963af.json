[1, ["ecpdLyjvZBwrvm+cedCcQy", "5c8HZOo+BFi60zKvEbh2fE", "251ooX3b1PuowpnHu1Ggvo", "d7mWcadkJFo71KIoCRYRYo", "2avkM0hyFJ6oh64GPl32qH", "80Y+Sa9Q5MxoUd+m4oj1uI", "98C01Omn9PN5wRc834BgXj", "caHfhuYYZIZ7szmd88W5oL", "7aoMpsjKZNkp2roI4Z66Fl", "76XxpB5npCyI2b3cKNlsVm", "77HEGBXBhPcIyY/Q0okKCu", "2ajfZni7NMeI6+eFPfm2Xv", "e7t0UIgvpD849Vg3vqnc3O", "7fLB+AbGpGuK2XTScqpP8o", "aeLvPLOJxDUqZCBbhlyMpI", "65EXVhwO1ErZStuUSk4vTK"], ["node", "_textureSetter", "_spriteFrame", "_parent", "root", "data", "_defaultClip"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_opacity", "_prefab", "_children", "_trs", "_components", "_parent", "_contentSize", "_color"], 1, 4, 2, 7, 9, 1, 5, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_dstBlendFactor", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.AnimationClip", ["_name", "_duration", "curveData"], 1, 11], ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6]], [[3, 0, 1, 2, 2], [1, 0, 6, 5, 2, 7, 4, 2], [2, 0, 1, 3, 4, 5, 3], [4, 0, 1, 2, 3], [5, 0, 2], [1, 0, 3, 2, 2], [1, 0, 3, 2, 4, 2], [1, 0, 3, 5, 2, 2], [1, 0, 6, 3, 2, 2], [1, 0, 1, 6, 5, 2, 8, 7, 4, 3], [3, 1, 2, 1], [2, 0, 1, 3, 4, 3], [2, 2, 0, 1, 3, 4, 5, 4], [6, 0, 1, 2, 1]], [[[[3, "城墙卷轴", 1.2166666666666666, [{}, "paths", 11, [{"卷轴/城墙_11": {"props": {"position": [{"frame": 0, "value": [-3.326, -441.689, 0]}, {"frame": 0.16666666666666666, "value": [-320.738, -441.689, 0]}, {"frame": 0.5, "value": [-333.496, -441.689, 0]}]}}, "卷轴/城墙_10": {"props": {"position": [{"frame": 0, "value": [5.846, -441.759, 0]}, {"frame": 0.16666666666666666, "value": [318.29, -441.759, 0]}, {"frame": 0.5, "value": [333.545, -441.759, 0]}]}}, "卷轴/城墙_3": {"props": {"opacity": [{"frame": 0.06666666666666667, "value": 0}, {"frame": 0.5166666666666667, "value": 255}, {"frame": 1.1, "value": 0}], "position": [{"frame": 0.06666666666666667, "value": [-80.299, -472.797, 0]}, {"frame": 1.1, "value": [-80.299, -453.322, 0]}]}}, "卷轴/城墙_4": {"props": {"position": [{"frame": 0.05, "value": [77.054, -455.234, 0]}, {"frame": 1.0833333333333333, "value": [77.054, -439.993, 0]}], "opacity": [{"frame": 0.05, "value": 0}, {"frame": 0.5, "value": 255}, {"frame": 1.0833333333333333, "value": 0}]}}, "卷轴/城墙_7": {"props": {"position": [{"frame": 0.06666666666666667, "value": [112.594, -442.23, 0]}, {"frame": 1.1666666666666667, "value": [112.594, -398.199, 0]}], "opacity": [{"frame": 0.06666666666666667, "value": 0}, {"frame": 0.5333333333333333, "value": 255}, {"frame": 1.1666666666666667, "value": 0}]}}, "卷轴/城墙_8": {"props": {"position": [{"frame": 0.08333333333333333, "value": [148.801, -450.666, 0]}, {"frame": 1.2, "value": [148.801, -427.804, 0]}], "opacity": [{"frame": 0.08333333333333333, "value": 0}, {"frame": 0.5333333333333333, "value": 255}, {"frame": 1.2, "value": 0}]}}, "卷轴/城墙_6": {"props": {"position": [{"frame": 0.1, "value": [-180.502, -433.016, 0]}, {"frame": 1.2166666666666666, "value": [-180.502, -401.686, 0]}], "opacity": [{"frame": 0.1, "value": 0}, {"frame": 0.6166666666666667, "value": 255}, {"frame": 1.2166666666666666, "value": 0}]}}, "卷轴/城墙_5": {"props": {"position": [{"frame": 0.13333333333333333, "value": [233.909, -425.793, 0]}, {"frame": 1.1166666666666667, "value": [233.909, -395.311, 0]}], "opacity": [{"frame": 0.13333333333333333, "value": 0}, {"frame": 0.5833333333333334, "value": 255}, {"frame": 1.1166666666666667, "value": 0}]}}, "卷轴/城墙_9": {"props": {"position": [{"frame": 0.16666666666666666, "value": [279.697, -450.238, 0]}, {"frame": 1.15, "value": [279.697, -431.61, 0]}], "opacity": [{"frame": 0.16666666666666666, "value": 0}, {"frame": 0.6166666666666667, "value": 255}, {"frame": 1.15, "value": 0}]}}, "卷轴/城墙_2": {"props": {"position": [{"frame": 0.13333333333333333, "value": [-227.958, -441.785, 0]}, {"frame": 1.15, "value": [-227.958, -420.616, 0]}], "opacity": [{"frame": 0.13333333333333333, "value": 0}, {"frame": 0.5833333333333334, "value": 255}, {"frame": 1.15, "value": 0}]}}, "卷轴/城墙_1": {"props": {"position": [{"frame": 0.16666666666666666, "value": [-284.701, -462.291, 0]}, {"frame": 1.1333333333333333, "value": [-284.701, -441.122, 0]}], "opacity": [{"frame": 0.16666666666666666, "value": 0}, {"frame": 0.6166666666666667, "value": 255}, {"frame": 1.1333333333333333, "value": 0}]}}}, "卷轴/城墙_12", 11, [{}, "props", 11, [{}, "scale", 12, [[[{"frame": 0, "curve": "linear"}, "value", 8, [1, 0.04, 1, 1]], [{"frame": 0.16666666666666666, "curve": "cubicOut"}, "value", 8, [1, 0.966, 1, 1]], [{"frame": 0.5}, "value", 8, [1, 1, 1, 1]]], 11, 11, 11]]], "卷轴/glow", 11, [{}, "props", 11, [{"opacity": [{"frame": 0, "value": 0}, {"frame": 0.08333333333333333, "value": 0}, {"frame": 0.2833333333333333, "value": 100}, {"frame": 1.2166666666666666, "value": 0}]}, "scale", 12, [[[{"frame": 0.08333333333333333}, "value", 8, [1, 0.648, 4.2, 1]], [{"frame": 0.2833333333333333}, "value", 8, [1, 7.195, 4.2, 1]]], 11, 11]]]]]]], 0, 0, [], [], []], [[{"name": "城墙_7", "rect": [40, 659, 25, 53], "offset": [0, 0], "originalSize": [25, 53], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [1]], [[{"name": "城墙_11", "rect": [2, 497, 47, 96], "offset": [0, 0], "originalSize": [47, 96], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [1]], [[[4, "城墙_卷轴"], [5, "root", [-2], [10, -1, 0]], [6, "卷轴", [-3, -4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15], [0, "c8F9TK4apJT5/lW/93Ig3k", 1, 0], [0, 490.057, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "sprite", [2], [[11, 0, false, -16, [26]], [13, -17, [28], 27]], [0, "24MVL74RhOqINJoHjo0+ow", 1, 0]], [8, "position", 1, [3], [0, "1768HiGARNaoyAvM2o3PlD", 1, 0]], [1, "城墙_12", 2, [[2, 0, false, -18, [0], 1]], [0, "e3lu7r2SdPGYeiBi0C9Gtz", 1, 0], [5, 704, 138], [1.474, -429.321, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "城墙_11", 2, [[2, 0, false, -19, [2], 3]], [0, "c8JBCiKj9BDYIv6JHjauEa", 1, 0], [5, 67, 137], [-333.458, -441.894, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "城墙_10", 2, [[2, 0, false, -20, [4], 5]], [0, "a8aXOYpiBGPJ00Mkcghmys", 1, 0], [5, 67, 137], [333.545, -441.759, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "城墙_1", 2, [[2, 0, false, -21, [6], 7]], [0, "23P5aPKRRF5qWRSlfrvNWN", 1, 0], [5, 35, 111], [-284.701, -462.291, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "城墙_2", 2, [[2, 0, false, -22, [8], 9]], [0, "789wSTvTlHU4aU/HQ58bll", 1, 0], [5, 34, 98], [-227.958, -431.624, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "城墙_3", 2, [[2, 0, false, -23, [10], 11]], [0, "e4vtehxPNF8KhD48tFypij", 1, 0], [5, 31, 88], [-80.299, -472.797, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "城墙_6", 2, [[2, 0, false, -24, [12], 13]], [0, "82ozzEV/9OFo8aR9G/RweZ", 1, 0], [5, 36, 89], [-180.502, -411.847, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "城墙_4", 2, [[2, 0, false, -25, [14], 15]], [0, "474TFn9N1Ea4/DqpFPzroI", 1, 0], [5, 35, 111], [77.054, -455.234, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "城墙_7", 2, [[2, 0, false, -26, [16], 17]], [0, "6ef7QW1S1Ax5sL2skwLmDE", 1, 0], [5, 35, 75], [112.594, -415.134, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "城墙_8", 2, [[2, 0, false, -27, [18], 19]], [0, "cccxqCnmhGDLhEfkt67SNc", 1, 0], [5, 38, 98], [148.801, -450.666, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "城墙_9", 2, [[2, 0, false, -28, [20], 21]], [0, "fdDNuPYJdJLofrvnYO1REs", 1, 0], [5, 35, 111], [279.697, -450.238, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "城墙_5", 2, [[2, 0, false, -29, [22], 23]], [0, "04CS1zFb5OV7hNXKJofGNm", 1, 0], [5, 33, 100], [233.909, -407.165, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "glow", 50, 2, [[12, 1, 0, false, -30, [24], 25]], [0, "4fYS1k6adIC7eKTeH/Q9yy", 1, 0], [4, 4288475647], [5, 146, 51], [0, -436.967, 0, 0, 0, 0, 1, 0.648, 3.651, 1]]], 0, [0, 4, 1, 0, -1, 4, 0, -1, 5, 0, -2, 6, 0, -3, 7, 0, -4, 8, 0, -5, 9, 0, -6, 10, 0, -7, 11, 0, -8, 12, 0, -9, 13, 0, -10, 14, 0, -11, 15, 0, -12, 16, 0, -13, 17, 0, 0, 3, 0, 0, 3, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, 0, 15, 0, 0, 16, 0, 0, 17, 0, 5, 1, 2, 3, 3, 3, 3, 4, 30], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 6, -1], [0, 3, 0, 4, 0, 5, 0, 6, 0, 7, 0, 8, 0, 9, 0, 10, 0, 11, 0, 12, 0, 13, 0, 14, 0, 15, 0, 2, 2]], [[{"name": "城墙_13", "rect": [2, 595, 103, 36], "offset": [0, 1], "originalSize": [119, 42], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [1]], [[{"name": "城墙_6", "rect": [40, 595, 25, 62], "offset": [0, 0], "originalSize": [25, 62], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [1]], [[{"name": "城墙_4", "rect": [2, 700, 25, 78], "offset": [0, 0], "originalSize": [25, 78], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [1]], [[{"name": "城墙_3", "rect": [67, 595, 22, 62], "offset": [0, 0], "originalSize": [22, 62], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [1]], [[{"name": "城墙_9", "rect": [2, 700, 25, 78], "offset": [0, 0], "originalSize": [25, 78], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [1]], [[{"name": "城墙_10", "rect": [2, 546, 47, 96], "offset": [0, 0], "originalSize": [47, 96], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [1]], [[{"name": "城墙_1", "rect": [2, 700, 25, 78], "offset": [0, 0], "originalSize": [25, 78], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [1]], [[{"name": "城墙_5", "rect": [29, 755, 23, 70], "offset": [0, 0], "originalSize": [23, 70], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [1]], [[{"name": "城墙_2", "rect": [29, 729, 24, 69], "offset": [0, 0], "originalSize": [24, 69], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [1]], [[{"name": "城墙_12", "rect": [2, 2, 493, 97], "offset": [0, 0], "originalSize": [493, 97], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [1]], [[{"name": "城墙_8", "rect": [29, 700, 27, 69], "offset": [0, 0], "originalSize": [27, 69], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [1]]]]