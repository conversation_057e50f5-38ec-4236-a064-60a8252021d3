[1, ["b17FP//ktMAJgY9AAKlTE8", "02hW8SoaNN8bH15Z3bujJD", "a4etRAVtFK/4S8sjTZmqJE", "18gK5q1vtD/oep8ATqiwRH", "a4OaxIPTxJcq+ELW/65gvd"], ["value", "_textureSetter"], ["cc.SpriteFrame", ["cc.AnimationClip", ["_name", "_duration", "sample", "wrapMode", "curveData"], -1, 11]], [[1, 0, 1, 2, 3, 4, 5]], [[[{"name": "图片_特效_天雷网_004", "rect": [2, 2, 242, 248], "offset": [0, 2], "originalSize": [252, 252], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[[0, "天雷网", 1.1666666666666667, 24, 0, [{}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 0], [{"frame": 0.08333333333333333}, "value", 6, 1], [{"frame": 0.16666666666666666}, "value", 6, 2], [{"frame": 0.25}, "value", 6, 3], [{"frame": 0.3333333333333333}, "value", 6, 4]], 11, 11, 11, 11, 11]]], "props", 11, [{"opacity": [{"frame": 0.25, "value": 255}, {"frame": 0.3333333333333333, "value": 255, "curve": [0.46116352201257865, -0.002672955974842628, 0.8257861635220125, 0.664622641509434]}, {"frame": 1.1666666666666667, "value": 0}]}, "scale", 12, [[[{"frame": 0.041666666666666664}, "value", 8, [1, 1, 1, 1]], [{"frame": 0.08333333333333333}, "value", 8, [1, 1.298, 1.298, 1.298]]], 11, 11]]]]], 0, 0, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [2, 3, 4, 1, 1]], [[{"name": "图片_特效_天雷网_002", "rect": [2, 252, 232, 214], "offset": [7, -19], "originalSize": [252, 252], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "图片_特效_天雷网_003", "rect": [2, 468, 228, 228], "offset": [1, -1], "originalSize": [252, 252], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "图片_特效_天雷网_001", "rect": [2, 698, 108, 104], "offset": [2, -69], "originalSize": [252, 252], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]]]]