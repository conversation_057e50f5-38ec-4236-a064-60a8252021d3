[1, ["ecpdLyjvZBwrvm+cedCcQy", "8eh8evg7lIX5rxZzhJUGYo", "79ajN2tCBLqJ6S3s95pLsT", "43r7jkFdpGJqIGmXf/wVnj", "7fjvZirYBAFJiC9I6EKbaV", "6dpKjZP2tPibHRM/R6zr72", "a5cuK5Fv9BCLCjmPP6AWCF", "02V5gDq8BF8LMNQzgkkUrA"], ["node", "_spriteFrame", "_textureSetter", "root", "data", "_parent", "_defaultClip"], [["cc.Node", ["_name", "_opacity", "_prefab", "_parent", "_contentSize", "_components", "_trs", "_children", "_anchorPoint", "_color", "_eulerAngles"], 1, 4, 1, 5, 9, 7, 2, 5, 5, 5], "cc.SpriteFrame", ["cc.Sprite", ["_dstBlendFactor", "_sizeMode", "_isTrimmedMode", "_srcBlendFactor", "node", "_materials", "_spriteFrame"], -1, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.AnimationClip", ["_name", "_duration", "sample", "wrapMode", "curveData"], -1, 11], ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6]], [[3, 0, 1, 2, 2], [2, 0, 4, 5, 6, 2], [2, 4, 5, 6, 1], [0, 0, 3, 7, 5, 2, 4, 8, 6, 10, 2], [0, 0, 3, 5, 2, 9, 4, 8, 6, 2], [0, 0, 3, 5, 2, 9, 4, 6, 2], [0, 0, 1, 3, 5, 2, 9, 4, 6, 10, 3], [0, 0, 3, 5, 2, 4, 6, 10, 2], [2, 0, 1, 4, 5, 6, 3], [0, 0, 3, 7, 2, 6, 2], [0, 0, 3, 7, 5, 2, 4, 8, 6, 2], [0, 0, 3, 5, 2, 4, 6, 2], [4, 0, 1, 2, 3, 4, 5], [5, 0, 2], [0, 0, 7, 2, 2], [0, 0, 7, 5, 2, 4, 6, 2], [0, 0, 3, 7, 2, 6, 10, 2], [0, 0, 3, 7, 2, 4, 2], [0, 0, 3, 5, 2, 9, 4, 8, 2], [0, 0, 1, 3, 5, 2, 9, 4, 8, 3], [3, 1, 2, 1], [2, 1, 2, 4, 5, 3], [2, 3, 0, 4, 5, 6, 3], [6, 0, 1, 2, 3, 2]], [[[{"name": "寒冰突刺_爆_002", "rect": [2, 214, 227, 200], "offset": [2, 1], "originalSize": [231, 224], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [5]], [[{"name": "4", "rect": [85, 875, 90, 88], "offset": [0, 1], "originalSize": [90, 90], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [5]], [[{"name": "1", "rect": [2, 875, 81, 116], "offset": [0, 0], "originalSize": [81, 116], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [5]], [[{"name": "5", "rect": [384, 614, 84, 76], "offset": [-1, 2], "originalSize": [90, 90], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [5]], [[{"name": "2", "rect": [384, 418, 80, 194], "offset": [0, 0], "originalSize": [80, 194], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [5]], [[[12, "冰封术_范围冰冻_循环", 1.0833333333333333, 24, 2, [{}, "paths", 11, [{"10": {"props": {"scale": [], "angle": [], "opacity": [], "active": [{"frame": 0, "value": true}]}}, "11": {"props": {"scale": [], "position": [], "opacity": [], "active": [{"frame": 0, "value": true}]}}, "12": {"props": {"scale": [], "position": [], "opacity": [], "active": [{"frame": 0, "value": true}]}}, "13": {"props": {"scale": [], "opacity": [], "active": [{"frame": 0, "value": true}]}}, "14": {"props": {"scale": [], "opacity": [], "active": [{"frame": 0, "value": true}]}}, "15": {"props": {"scale": [], "opacity": [], "active": [{"frame": 0, "value": true}]}}, "04": {"props": {"scale": [], "opacity": [], "active": [{"frame": 0, "value": true}]}}, "09": {"props": {"scale": [], "opacity": [], "angle": [], "active": [{"frame": 0, "value": true}]}}, "08": {"props": {"scale": [], "angle": [], "position": [], "opacity": [], "active": [{"frame": 0, "value": true}]}}, "03": {"props": {"scale": [], "angle": [], "opacity": [], "active": [{"frame": 0, "value": true}]}}, "02": {"props": {"scale": [], "opacity": [], "angle": [], "active": [{"frame": 0, "value": true}]}}, "07": {"props": {"scale": [], "angle": [], "opacity": [], "position": [], "active": [{"frame": 0, "value": true}]}}, "01": {"props": {"scale": [], "opacity": [], "active": [{"frame": 0, "value": true}]}}, "05": {"props": {"scale": [], "position": [], "opacity": [], "active": []}}, "08/08_1": {"props": {"opacity": [{"frame": 0.125, "value": 0}, {"frame": 0.2916666666666667, "value": 136}, {"frame": 0.5, "value": 0}]}}, "09/09_1": {"props": {"opacity": [{"frame": 0.08333333333333333, "value": 0}, {"frame": 0.25, "value": 140.35}, {"frame": 0.4583333333333333, "value": 0}]}}, "14/14_1": {"props": {"opacity": [{"frame": 0.16666666666666666, "value": 0}, {"frame": 0.3333333333333333, "value": 76.35}, {"frame": 0.5416666666666666, "value": 0}]}}, "11/11_1": {"props": {"opacity": [{"frame": 0.08333333333333333, "value": 0}, {"frame": 0.25, "value": 44.349999999999994}, {"frame": 0.4583333333333333, "value": 0}]}}, "03/03_1": {"props": {"opacity": [{"frame": 0.25, "value": 0}, {"frame": 0.4166666666666667, "value": 84}, {"frame": 0.625, "value": 0}]}}, "02/02_1": {"props": {"opacity": [{"frame": 0.125, "value": 0}, {"frame": 0.2916666666666667, "value": 168.35}, {"frame": 0.5, "value": 0}]}}, "10/10_1": {"props": {"opacity": [{"frame": 0.16666666666666666, "value": 0}, {"frame": 0.3333333333333333, "value": 112.35}, {"frame": 0.5416666666666666, "value": 0}]}}, "05/05_1": {"props": {"opacity": [{"frame": 0.08333333333333333, "value": 0}, {"frame": 0.25, "value": 36}, {"frame": 0.4583333333333333, "value": 0}]}}, "01/01_1": {"props": {"opacity": [{"frame": 0.2916666666666667, "value": 0}, {"frame": 0.4583333333333333, "value": 100}, {"frame": 0.6666666666666666, "value": 0}]}}, "07/07_1": {"props": {"opacity": [{"frame": 0.08333333333333333, "value": 0}, {"frame": 0.25, "value": 136.35}, {"frame": 0.4583333333333333, "value": 0}]}}, "13/13_1": {"props": {"opacity": [{"frame": 0.20833333333333334, "value": 0}, {"frame": 0.375, "value": 24.349999999999994}, {"frame": 0.5833333333333334, "value": 0}]}}, "12/12_1": {"props": {"opacity": [{"frame": 0, "value": 0}, {"frame": 0.16666666666666666, "value": 60.349999999999994}, {"frame": 0.375, "value": 0}]}}, "04/04_1": {"props": {"opacity": [{"frame": 0.20833333333333334, "value": 0}, {"frame": 0.375, "value": 104.35}, {"frame": 0.5833333333333334, "value": 0}]}}, "15/15_1": {"props": {"opacity": [{"frame": 0.20833333333333334, "value": 0}, {"frame": 0.375, "value": 160.35}, {"frame": 0.5833333333333334, "value": 0}]}}, "06/06_1": {"props": {"opacity": [{"frame": 0.041666666666666664, "value": 0}, {"frame": 0.20833333333333334, "value": 108.35}, {"frame": 0.4166666666666667, "value": 0}], "position": []}}, "烟/烟05": {"props": {"position": [], "scale": [], "opacity": [], "angle": []}}, "烟/烟04": {"props": {"position": [], "opacity": [], "scale": [], "angle": []}}, "烟/烟03": {"props": {"position": [], "scale": [], "opacity": [], "angle": []}}, "烟/烟02": {"props": {"position": [], "scale": [], "opacity": [], "angle": []}}, "烟/烟01": {"props": {"position": [], "scale": [], "opacity": [], "angle": []}}, "烟/烟06": {"props": {"scale": [], "position": [], "angle": [], "opacity": []}}, "寒冰突刺_爆_002": {"props": {"active": [{"frame": 0, "value": false}]}, "comps": {"cc.Sprite": {"spriteFrame": []}}}, "爆飞/爆飞01": {"props": {"position": [], "active": [], "scale": []}}, "爆飞/爆飞02": {"props": {"position": [], "scale": [], "active": []}}, "爆飞/爆飞03": {"props": {"position": [], "scale": [], "active": []}}, "爆飞/爆飞04": {"props": {"position": [], "scale": [], "active": []}}, "爆飞/爆飞05": {"props": {"position": [], "scale": [], "active": []}}, "爆飞/爆飞06": {"props": {"position": [], "scale": [], "active": []}}, "爆飞/爆飞07": {"props": {"position": [], "scale": [], "active": []}}, "烟": {"props": {"active": [{"frame": 0, "value": false}]}}, "爆飞": {"props": {"active": [{"frame": 0, "value": false}]}}}, "06", 11, [{}, "props", 11, [{"position": [], "opacity": [], "active": [{"frame": 0, "value": true}]}, "scale", 12, [[[{"frame": 0}, "value", 8, [1, 0.453, 0.531, 0.531]]], 11]]], "星/4", 11, [{}, "props", 11, [{}, "scale", 12, [[[{"frame": 0}, "value", 8, [1, 0, 0, 0.467]], [{"frame": 0.16666666666666666}, "value", 8, [1, 0.467, 0.467, 0.467]], [{"frame": 0.3333333333333333}, "value", 8, [1, 0, 0, 0.467]]], 11, 11, 11]]], "星/5", 11, [{}, "props", 11, [{}, "scale", 12, [[[{"frame": 0.041666666666666664}, "value", 8, [1, 0, 0, 0.467]], [{"frame": 0.20833333333333334}, "value", 8, [1, 0.467, 0.467, 0.467]], [{"frame": 0.375}, "value", 8, [1, 0, 0, 0.467]]], 11, 11, 11]]], "星/6", 11, [{}, "props", 11, [{}, "scale", 12, [[[{"frame": 0.125}, "value", 8, [1, 0, 0, 0.467]], [{"frame": 0.2916666666666667}, "value", 8, [1, 0.467, 0.467, 0.467]], [{"frame": 0.4583333333333333}, "value", 8, [1, 0, 0, 0.467]]], 11, 11, 11]]], "星/7", 11, [{}, "props", 11, [{"position": [{"frame": 0.16666666666666666, "curve": "constant", "value": [-8.108, 51.347, 0]}]}, "scale", 12, [[[{"frame": 0.16666666666666666}, "value", 8, [1, 0, 0, 0.467]], [{"frame": 0.3333333333333333}, "value", 8, [1, 0.467, 0.467, 0.467]], [{"frame": 0.5}, "value", 8, [1, 0, 0, 0.467]]], 11, 11, 11]]], "星/8", 11, [{}, "props", 11, [{}, "scale", 12, [[[{"frame": 0.25}, "value", 8, [1, 0, 0, 0.467]], [{"frame": 0.4166666666666667}, "value", 8, [1, 0.467, 0.467, 0.467]], [{"frame": 0.5833333333333334}, "value", 8, [1, 0, 0, 0.467]]], 11, 11, 11]]], "星/9", 11, [{}, "props", 11, [{}, "scale", 12, [[[{"frame": 0.2916666666666667}, "value", 8, [1, 0, 0, 0.467]], [{"frame": 0.4583333333333333}, "value", 8, [1, 0.467, 0.467, 0.467]], [{"frame": 0.625}, "value", 8, [1, 0, 0, 0.467]]], 11, 11, 11]]], "星/10", 11, [{}, "props", 11, [{"position": [{"frame": 0.3333333333333333, "curve": "constant", "value": [56.42, 103.686, 0]}]}, "scale", 12, [[[{"frame": 0.3333333333333333}, "value", 8, [1, 0, 0, 0.467]], [{"frame": 0.5}, "value", 8, [1, 0.467, 0.467, 0.467]], [{"frame": 0.6666666666666666}, "value", 8, [1, 0, 0, 0.467]]], 11, 11, 11]]]]]]], 0, 0, [], [], []], [[[13, "寒冰突刺_范围冰冻_循环"], [14, "root", [-2], [20, -1, 0]], [15, "sprite", [-5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15, -16, -17, -18, -19, -20, -21, -22, -23], [[21, 0, false, -3, [102]], [23, true, -4, [104], 103]], [0, "24MVL74RhOqINJoHjo0+ow", 1, 0], [5, 270, 270], [0, -8, 0, 0, 0, 0, 1, 0.5, 0.5, 0.855]], [9, "星", 2, [-24, -25, -26, -27, -28, -29, -30], [0, "f9lcWrVORNtJgk5MikdMl3", 1, 0], [0, 36.25731, 0, 0, 0, 0, 1, 1.16959, 1.16959, 1.16959]], [16, "爆飞", 2, [-31, -32, -33, -34, -35, -36, -37], [0, "3a51sbFUVJwbjFzcJy5kjv", 1, 0], [-118.68304, 115.48304, 0, 0, 0, 0.5518205475068424, 0.8339628788796589, 0.2386, 0.2386, 0.2386], [1, 0, 0, 66.984]], [9, "烟", 2, [-38, -39, -40, -41, -42, -43], [0, "a8gE90c9VBkKqPjpolSMBz", 1, 0], [0, 36.25731, 0, 0, 0, 0, 1, 1.16959, 1.16959, 1.16959]], [10, "01", 2, [-45], [[2, -44, [2], 3]], [0, "0el3zSxPRHIYZ/cpGD1b1j", 1, 0], [5, 80, 194], [0, 0.4782375, 0.08043298969072166], [-0.108, 12.741, 0, 0, 0, 0, 1, 1.348, 1.348, 1.348]], [3, "02", 2, [-47], [[2, -46, [6], 7]], [0, "baLro06WRIL4RRK8axr5o3", 1, 0], [5, 80, 194], [0, 0.4782375, 0.08043298969072166], [3.279, 7.661, 0, 0, 0, -0.25566511662710434, 0.9667654049147855, 1.107, 1.107, 1.107], [1, 0, 0, -29.626]], [3, "03", 2, [-49], [[2, -48, [10], 11]], [0, "8fYsyiB1BA37/bGitUabcO", 1, 0], [5, 80, 194], [0, 0.4782375, 0.08043298969072166], [-3.678, 7.438, 0, 0, 0, 0.24678760040857078, 0.969069595170842, 1.056, 1.056, 1.056], [1, 0, 0, 28.574999999999996]], [10, "04", 2, [-51], [[2, -50, [14], 15]], [0, "fdw8wZKzhEiZr2zlL750JP", 1, 0], [5, 81, 116], [0, 0.4677530864197531, 0.06097413793103451], [0.726, 6.679, 0, 0, 0, 0, 1, 0.794, 0.794, 0.794]], [3, "05", 2, [-53], [[2, -52, [18], 19]], [0, "2cQTmwICtLRYyJYhEkWRe5", 1, 0], [5, 80, 194], [0, 0.4782375, 0.08043298969072166], [93.252, 18.98, 0, 0, 0, 0.6977404508318431, -0.7163506566430831, 0.453, 0.531, 0.531], [1, 0, 0, 271.508]], [3, "06", 2, [-55], [[2, -54, [22], 23]], [0, "77RtTr97RDbaqC/1bc1rIq", 1, 0], [5, 80, 194], [0, 0.4782375, 0.08043298969072166], [-88.442, 23.24, 0, 0, 0, -0.715948668198072, -0.698152923438273, 0.453, 0.531, 0.531], [1, 0, 0, 451.44199999999995]], [3, "07", 2, [-57], [[2, -56, [26], 27]], [0, "94gL7uwqlNL75j4bOECIN8", 1, 0], [5, 80, 194], [0, 0.4782375, 0.08043298969072166], [25.158, 22.86, 0, 0, 0, -0.5374467997386614, 0.8432976564954222, 1, 1, 1], [1, 0, 0, -65.02]], [3, "08", 2, [-59], [[2, -58, [30], 31]], [0, "863Jg8dllHUL+4x/ECrewB", 1, 0], [5, 80, 194], [0, 0.4782375, 0.08043298969072166], [-21.596, 18.23, 0, 0, 0, 0.5343302203036624, 0.8452758222439818, 1, 1, 1], [1, 0, 0, 64.597]], [3, "09", 2, [-61], [[2, -60, [34], 35]], [0, "56kJNlokZEz7ZHJXHSEsFC", 1, 0], [5, 80, 194], [0, 0.4782375, 0.08043298969072166], [-21.596, 18.23, 0, 0, 0, 0.8039087656367648, 0.5947526347418505, 0.714, 0.837, 0.837], [1, 0, 0, 107.00999999999999]], [3, "10", 2, [-63], [[2, -62, [38], 39]], [0, "a2EpQMPHJB6om4sIaiLv6M", 1, 0], [5, 80, 194], [0, 0.4782375, 0.08043298969072166], [23.282, 20.77, 0, 0, 0, 0.8033686542579109, -0.5954819941491373, 0.714, 0.837, 0.837], [1, 0, 0, 253.094]], [3, "11", 2, [-65], [[2, -64, [42], 43]], [0, "1er4hzO3dH6oKgLwHiel7/", 1, 0], [5, 81, 116], [0, 0.4677530864197531, 0.06097413793103451], [1.826, 8.072, 0, 0, 0, -0.5061617150307995, 0.8624385880960335, 0.63, 0.934, 0.63], [1, 0, 0, -60.81700000000001]], [3, "12", 2, [-67], [[2, -66, [46], 47]], [0, "f8oEIj5aVJ4LZsUnRBgMpW", 1, 0], [5, 81, 116], [0, 0.4677530864197531, 0.06097413793103451], [-4.948, 3.839, 0, 0, 0, 0.4456901059405064, 0.895187315295933, 0.63, 0.934, 0.63], [1, 0, 0, 52.934999999999995]], [3, "13", 2, [-69], [[2, -68, [50], 51]], [0, "beFuwA9CFHUaWy8MWCXfKW", 1, 0], [5, 81, 116], [0, 0.4677530864197531, 0.06097413793103451], [13.552, 2.123, 0, 0, 0, 0.8506780996286886, -0.5256869513428338, 0.53, 0.606, 0.606], [1, 0, 0, 243.429]], [3, "14", 2, [-71], [[2, -70, [54], 55]], [0, "6cf378FztA6JzNbquWd6IW", 1, 0], [5, 81, 116], [0, 0.4677530864197531, 0.06097413793103451], [-13.544, 2.969, 0, 0, 0, 0.8704931726010589, 0.4921804917456025, 0.53, 0.606, 0.606], [1, 0, 0, 121.03199999999998]], [3, "15", 2, [-73], [[2, -72, [58], 59]], [0, "3dj3oHcadBi5ve7DGkgJdF", 1, 0], [5, 81, 116], [0, 0.4677530864197531, 0.06097413793103451], [2.419, 5.832, 0, 0, 0, 1, 6.123233995736766e-17, 0.639, 0.639, 0.639], [1, 0, 0, 180]], [17, "position", 1, [2], [0, "1768HiGARNaoyAvM2o3PlD", 1, 0], [5, 130, 130]], [4, "01_1", 6, [[1, 1, -74, [0], 1]], [0, "f9Kc2GJkRHuLdpoUtfFlS2", 1, 0], [4, 4294957985], [5, 80, 194], [0, 0.4782375, 0.08043298969072166], [0, 0, 0, 0, 0, 0, 1, 1, 1, 1.348]], [4, "02_1", 7, [[22, 1, 1, -75, [4], 5]], [0, "81onRCxUJHIq1jAgOYr8ic", 1, 0], [4, 4294957985], [5, 80, 194], [0, 0.4782375, 0.08043298969072166], [0, 0, 0, 0, 0, 0, 1, 1, 1, 1.107]], [4, "03_1", 8, [[1, 1, -76, [8], 9]], [0, "47p4Z7fb1NuKCAQXghVqjG", 1, 0], [4, 4294957985], [5, 80, 194], [0, 0.4782375, 0.08043298969072166], [0, 0, 0, 0, 0, 0, 1, 1, 1, 1.056]], [4, "04_1", 9, [[1, 1, -77, [12], 13]], [0, "930I6fCMdHM5AuHUE1CGbm", 1, 0], [4, 4294957985], [5, 81, 116], [0, 0.4677530864197531, 0.06097413793103451], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0.794]], [4, "05_1", 10, [[1, 1, -78, [16], 17]], [0, "6f0CmYAqVGYJS5zGSTzerR", 1, 0], [4, 4294957985], [5, 80, 194], [0, 0.4782375, 0.08043298969072166], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0.531]], [4, "06_1", 11, [[1, 1, -79, [20], 21]], [0, "b4XTJXfsVLb79SqIK9bod9", 1, 0], [4, 4294957985], [5, 80, 194], [0, 0.4782375, 0.08043298969072166], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0.531]], [18, "07_1", 12, [[1, 1, -80, [24], 25]], [0, "c0LMJgFhFKL62veIeYHrik", 1, 0], [4, 4294957985], [5, 80, 194], [0, 0.4782375, 0.08043298969072166]], [19, "08_1", 196.35, 13, [[1, 1, -81, [28], 29]], [0, "0e3ByDlqpJDboqtHYLHJ6G", 1, 0], [4, 4294957985], [5, 80, 194], [0, 0.4782375, 0.08043298969072166]], [4, "09_1", 14, [[1, 1, -82, [32], 33]], [0, "abe3yDFWlJy4wYI6RNHwuI", 1, 0], [4, 4294957985], [5, 80, 194], [0, 0.4782375, 0.08043298969072166], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0.837]], [4, "10_1", 15, [[1, 1, -83, [36], 37]], [0, "e5nePXH+9OBJav0lZ93w0B", 1, 0], [4, 4294957985], [5, 80, 194], [0, 0.4782375, 0.08043298969072166], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0.837]], [4, "11_1", 16, [[1, 1, -84, [40], 41]], [0, "d5+SnVv4pLiKyIDYw4J80G", 1, 0], [4, 4294957985], [5, 81, 116], [0, 0.4677530864197531, 0.06097413793103451], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0.63]], [4, "12_1", 17, [[1, 1, -85, [44], 45]], [0, "7bojo8zphDTIcH2RjNXMEF", 1, 0], [4, 4294957985], [5, 81, 116], [0, 0.4677530864197531, 0.06097413793103451], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0.63]], [4, "13_1", 18, [[1, 1, -86, [48], 49]], [0, "27hYu1ZGJJk6kwDVF1Kr+L", 1, 0], [4, 4294957985], [5, 81, 116], [0, 0.4677530864197531, 0.06097413793103451], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0.606]], [4, "14_1", 19, [[1, 1, -87, [52], 53]], [0, "d7TwfUADhGs5N4KVVN4fLz", 1, 0], [4, 4294957985], [5, 81, 116], [0, 0.4677530864197531, 0.06097413793103451], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0.606]], [4, "15_1", 20, [[1, 1, -88, [56], 57]], [0, "0051CzBpRKWqCBsO23VIgK", 1, 0], [4, 4294957985], [5, 81, 116], [0, 0.4677530864197531, 0.06097413793103451], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0.639]], [5, "4", 3, [[8, 1, 0, -89, [60], 61]], [0, "d2GbV27U1Nm49ck8BL5+OX", 1, 0], [4, 4294962639], [5, 70, 70], [-91.056, 65.792, 0, 0, 0, 0, 1, 0.467, 0.467, 0.467]], [5, "5", 3, [[8, 1, 0, -90, [62], 63]], [0, "46z5IB95ZAcpCPMZ6yP5ES", 1, 0], [4, 4294962639], [5, 40, 40], [86.533, 16.215, 0, 0, 0, 0, 1, 0.21809, 0.21809, 0.21809]], [5, "6", 3, [[1, 1, -91, [64], 65]], [0, "2eO4ZpMNRNXLjPiAxe3WJk", 1, 0], [4, 4294962639], [5, 90, 88], [-108.485, 3.309, 0, 0, 0, 0, 1, 0.328, 0.328, 0.328]], [5, "7", 3, [[8, 1, 0, -92, [66], 67]], [0, "964K0VrztHBYj/0rf7QGES", 1, 0], [4, 4294962639], [5, 50, 50], [-8.108, 51.347, 0, 0, 0, 0, 1, 0.285, 0.285, 0.285]], [5, "8", 3, [[1, 1, -93, [68], 69]], [0, "faM3CHhD9EiqPu1dUMgPmB", 1, 0], [4, 4294962639], [5, 90, 88], [90.835, -11.031, 0, 0, 0, 0, 1, 0.408, 0.408, 0.408]], [5, "9", 3, [[8, 1, 0, -94, [70], 71]], [0, "3epcEQe/ZEubUc/MArdUHD", 1, 0], [4, 4294962639], [5, 70, 70], [-8.108, -25.37, 0, 0, 0, 0, 1, 0.328, 0.328, 0.328]], [5, "10", 3, [[1, 1, -95, [72], 73]], [0, "88Gx6Y6dBPQYLURy9Yg10c", 1, 0], [4, 4294962639], [5, 90, 88], [56.42, 103.686, 0, 0, 0, 0, 1, 0.328, 0.328, 0.328]], [6, "烟06", 200, 5, [[2, -96, [74], 75]], [0, "3bLeBqDh1MSrRXz2QfaUUM", 1, 0], [4, 4294960053], [5, 84, 76], [0.416, -2.189, 0, 0, 0, 0.09633218340457214, -0.9953492404380022, 1.725, 1.725, 1.725], [1, 0, 0, 348.94399999999996]], [6, "烟01", 200, 5, [[2, -97, [76], 77]], [0, "f8xU+qW6lAC4NAwGGbOnMH", 1, 0], [4, 4294960053], [5, 84, 76], [109.377, -22.227, 0, 0, 0, 0.9893575764626353, 0.1455045906354181, 1.037, 1.037, 1.037], [1, 0, 0, 163.267]], [6, "烟02", 200, 5, [[2, -98, [78], 79]], [0, "bbg8uKLMtLHbJVQ7sk6+uZ", 1, 0], [4, 4294960053], [5, 84, 76], [54.17, -40.151, 0, 0, 0, 0.07450880609514031, 0.9972203556959097, 0.901, 0.901, 0.901], [1, 0, 0, 8.545999999999992]], [6, "烟03", 200, 5, [[2, -99, [80], 81]], [0, "49dy8A6C1NpLqKBv+gf7/o", 1, 0], [4, 4294960053], [5, 84, 76], [-120.773, -10.038, 0, 0, 0, -0.6517145940486008, 0.758464295734524, 0.857, 0.857, 0.857], [1, 0, 0, -81.34200000000001]], [6, "烟04", 200, 5, [[2, -100, [82], 83]], [0, "8bvhr0VZFId4ZfAFeQAvWH", 1, 0], [4, 4294960053], [5, 84, 76], [-69.867, -31.547, 0, 0, 0, -0.6517145940486008, 0.758464295734524, 0.987, 0.987, 0.987], [1, 0, 0, -81.34200000000001]], [6, "烟05", 200, 5, [[2, -101, [84], 85]], [0, "ffA09EpiZC8LOc0QfyxZO6", 1, 0], [4, 4294960053], [5, 84, 76], [5.416, -50.189, 0, 0, 0, 0.9402376203405972, 0.3405190410186353, 1.195, 1.195, 1.195], [1, 0, 0, 140.183]], [11, "寒冰突刺_爆_002", 2, [[2, -102, [86], 87]], [0, "38C8Zxho1ATotpMOTgHrb5", 1, 0], [5, 227, 200], [0, 90.887, 0, 0, 0, 0, 1, 1.596, 1.596, 1.596]], [11, "爆飞01", 4, [[1, 1, -103, [88], 89]], [0, "a7UuzfjOtF4LwjqvZCCpe/", 1, 0], [5, 80, 194], [-9.937, -52.721, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "爆飞02", 4, [[1, 1, -104, [90], 91]], [0, "aaaAZhQMBE760w/S/tPiqG", 1, 0], [5, 80, 194], [149.478, -793.329, 0, 0, 0, -0.8699944968091363, 0.4930614317930553, 1.228, 1.228, 1.228], [1, 0, 0, -120.916]], [7, "爆飞03", 4, [[1, 1, -105, [92], 93]], [0, "1dR3cIkStDD6qMVAMkDMmG", 1, 0], [5, 80, 194], [222.621, -280.494, 0, 0, 0, -0.34940620061666133, 0.9369713479987686, 1.228, 1.228, 1.228], [1, 0, 0, -40.902]], [7, "爆飞04", 4, [[1, 1, -106, [94], 95]], [0, "ddB1nuuTtH56IWK+JC6AVC", 1, 0], [5, 80, 194], [-424.50427, -314.17908, 0, 0, 0, 0.36332440421044493, 0.9316626950270818, 0.82644, 1.50798, 0.82644], [1, 0, 0, 42.608999999999995]], [7, "爆飞05", 4, [[1, 1, -107, [96], 97]], [0, "bbMI4vIL9BXKvzNh9ZE/Fl", 1, 0], [5, 80, 194], [-160.482, -297.484, 0, 0, 0, 0.03623382872252232, 0.9993433392263676, 1.773, 2.171, 1.773], [1, 0, 0, 4.152999999999992]], [7, "爆飞06", 4, [[1, 1, -108, [98], 99]], [0, "2awVsHX8tMT79t0VSUdFcI", 1, 0], [5, 80, 194], [-103.876, -925.326, 0, 0, 0, 0.07242007077704346, 0.9973742193122138, 1.521, 2.281, 1.521], [1, 0, 0, 8.305999999999983]], [7, "爆飞07", 4, [[1, 1, -109, [100], 101]], [0, "3ap6qmUSRHWJh7qS1mKrms", 1, 0], [5, 80, 194], [97.071, -427.563, 0, 0, 0, 0.9179589844097572, 0.39667531173669873, 1.283, 1.924, 1.283], [1, 0, 0, 133.259]]], 0, [0, 3, 1, 0, -1, 21, 0, 0, 2, 0, 0, 2, 0, -1, 6, 0, -2, 7, 0, -3, 8, 0, -4, 9, 0, -5, 10, 0, -6, 11, 0, -7, 12, 0, -8, 13, 0, -9, 14, 0, -10, 15, 0, -11, 16, 0, -12, 17, 0, -13, 18, 0, -14, 19, 0, -15, 20, 0, -16, 3, 0, -17, 5, 0, -18, 50, 0, -19, 4, 0, -1, 37, 0, -2, 38, 0, -3, 39, 0, -4, 40, 0, -5, 41, 0, -6, 42, 0, -7, 43, 0, -1, 51, 0, -2, 52, 0, -3, 53, 0, -4, 54, 0, -5, 55, 0, -6, 56, 0, -7, 57, 0, -1, 44, 0, -2, 45, 0, -3, 46, 0, -4, 47, 0, -5, 48, 0, -6, 49, 0, 0, 6, 0, -1, 22, 0, 0, 7, 0, -1, 23, 0, 0, 8, 0, -1, 24, 0, 0, 9, 0, -1, 25, 0, 0, 10, 0, -1, 26, 0, 0, 11, 0, -1, 27, 0, 0, 12, 0, -1, 28, 0, 0, 13, 0, -1, 29, 0, 0, 14, 0, -1, 30, 0, 0, 15, 0, -1, 31, 0, 0, 16, 0, -1, 32, 0, 0, 17, 0, -1, 33, 0, 0, 18, 0, -1, 34, 0, 0, 19, 0, -1, 35, 0, 0, 20, 0, -1, 36, 0, 0, 22, 0, 0, 23, 0, 0, 24, 0, 0, 25, 0, 0, 26, 0, 0, 27, 0, 0, 28, 0, 0, 29, 0, 0, 30, 0, 0, 31, 0, 0, 32, 0, 0, 33, 0, 0, 34, 0, 0, 35, 0, 0, 36, 0, 0, 37, 0, 0, 38, 0, 0, 39, 0, 0, 40, 0, 0, 41, 0, 0, 42, 0, 0, 43, 0, 0, 44, 0, 0, 45, 0, 0, 46, 0, 0, 47, 0, 0, 48, 0, 0, 49, 0, 0, 50, 0, 0, 51, 0, 0, 52, 0, 0, 53, 0, 0, 54, 0, 0, 55, 0, 0, 56, 0, 0, 57, 0, 4, 1, 2, 5, 21, 109], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 6, -1], [0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 0, 2, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 3, 0, 3, 0, 3, 0, 3, 0, 3, 0, 3, 0, 3, 0, 4, 0, 4, 0, 4, 0, 4, 0, 4, 0, 4, 0, 7, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 6, 6]]]]