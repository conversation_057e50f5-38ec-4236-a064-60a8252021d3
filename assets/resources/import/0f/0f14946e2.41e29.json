[1, ["ecpdLyjvZBwrvm+cedCcQy", "9ejdpgvXFH6ojzNWnTdO9t", "e77zaeoRpIQ5GJcH95xnjT", "054s+UPPpKv4Jay/wRdyg9"], ["node", "_textureSetter", "root", "data", "_parent", "_spriteFrame", "_defaultClip"], [["cc.Node", ["_name", "_prefab", "_children", "_components", "_parent", "_contentSize", "_anchorPoint", "_trs"], 2, 4, 2, 9, 1, 5, 5, 7], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Sprite", ["_isTrimmedMode", "_sizeMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], "cc.SpriteFrame", ["cc.AnimationClip", ["_name", "_duration", "sample", "events", "curveData"], -2], ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6]], [[1, 0, 1, 2, 2], [4, 0, 1, 2, 3, 4, 6], [5, 0, 2], [0, 0, 2, 1, 2], [0, 0, 2, 3, 1, 2], [0, 0, 4, 2, 1, 5, 2], [0, 0, 4, 3, 1, 5, 6, 7, 2], [1, 1, 2, 1], [2, 0, 2, 3, 2], [2, 1, 0, 2, 3, 4, 3], [6, 0, 1, 2, 3, 2]], [[[{"name": "落石术_006", "rect": [2, 1282, 121, 227], "offset": [0, 2], "originalSize": [163, 231], "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [1], [2]], [[[1, "落石术_普通_石头", 0.375, 24, [{"frame": 0.3333333333333333, "func": "Hit", "params": []}], {"paths": {"图片_特效_落石术_007": {"props": {"position": [{"frame": 0, "value": [0, 500, 0]}, {"frame": 0.3333333333333333, "value": [0, 22, 0]}], "active": [{"frame": 0, "value": true}, {"frame": 0.375, "value": false}]}}}}]], 0, 0, [], [], []], [[[2, "落石术_普通_石头"], [3, "root", [-2], [7, -1, 0]], [4, "sprite", [-5], [[8, false, -3, [2]], [10, true, -4, [4], 3]], [0, "24MVL74RhOqINJoHjo0+ow", 1, 0]], [5, "position", 1, [2], [0, "1768HiGARNaoyAvM2o3PlD", 1, 0], [5, 100, 100]], [6, "图片_特效_落石术_007", 2, [[9, 0, false, -6, [0], 1]], [0, "9dMDI0/j5EOaNeheiq7r5v", 1, 0], [5, 233, 330], [0, 0.5, 0.09393939393939393], [0, 0, 0, 0, 0, 0, 1, 0.7, 0.7, 0.738]]], 0, [0, 2, 1, 0, -1, 3, 0, 0, 2, 0, 0, 2, 0, -1, 4, 0, 0, 4, 0, 3, 1, 2, 4, 3, 6], [0, 0, 0, 0, 0], [-1, 5, -1, 6, -1], [0, 3, 0, 1, 1]]]]