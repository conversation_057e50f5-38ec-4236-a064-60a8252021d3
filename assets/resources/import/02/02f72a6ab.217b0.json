[1, ["f5beDDuz5BTrz2SyTyXsnL", "a8m43/PlRBF62PExbNkHST", "f3EbP5SrpKhpdpGzsMu/Dp", "bc06j6u59C/bxi0qu9Z7Vs", "c1R0fk0oFHpZ3QuIL3irUI", "3cpKtOO9dJlbo11oJSIUcQ", "b5ZZC1PrNEVLLTuotP9B9G", "c37/mE0NBO1p3dpX6WfBOG", "6eowYCjWRPuqLTEluxplsa"], ["value", "_textureSetter"], ["cc.SpriteFrame", ["cc.AnimationClip", ["_name", "_duration", "sample", "wrapMode", "curveData"], -1, 11]], [[1, 0, 1, 2, 3, 4, 5]], [[[{"name": "主角_新_003", "rect": [1412, 2, 200, 224], "offset": [-11, 13], "originalSize": [280, 280], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "主角_新_006", "rect": [1008, 2, 200, 224], "offset": [-11, 12], "originalSize": [280, 280], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[[0, "主角", 1.0416666666666667, 24, 2, [{}, "paths", 11, [{}, "图片_特效_主角_000", 11, [{}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 0], [{"frame": 0.125}, "value", 6, 1], [{"frame": 0.25}, "value", 6, 2], [{"frame": 0.375}, "value", 6, 3], [{"frame": 0.5}, "value", 6, 4], [{"frame": 0.625}, "value", 6, 5], [{"frame": 0.75}, "value", 6, 6], [{"frame": 0.875}, "value", 6, 7], [{"frame": 1}, "value", 6, 8]], 11, 11, 11, 11, 11, 11, 11, 11, 11]]]], "图片_特效_主角_004", 11, [{}, "props", 11, [{"opacity": [{"frame": 0, "value": 180}, {"frame": 0.5, "value": 220}, {"frame": 1, "value": 180}], "position": [{"frame": 0, "value": [0, 114.462, 0]}, {"frame": 1, "value": [0, 114.462, 0]}]}, "scale", 12, [[[{"frame": 0}, "value", 8, [1, 0.771, 0.771, 0.771]], [{"frame": 0.041666666666666664}, "value", 8, [1, 0.95, 0.95, 0.95]], [{"frame": 0.08333333333333333}, "value", 8, [1, 0.86, 0.86, 0.86]], [{"frame": 0.125}, "value", 8, [1, 1.157, 1.157, 1.157]], [{"frame": 0.16666666666666666}, "value", 8, [1, 0.959, 0.959, 0.959]], [{"frame": 0.20833333333333334}, "value", 8, [1, 1.279, 1.279, 1.279]], [{"frame": 0.25}, "value", 8, [1, 0.928, 0.928, 0.928]], [{"frame": 0.2916666666666667}, "value", 8, [1, 1.205, 1.205, 1.205]], [{"frame": 0.3333333333333333}, "value", 8, [1, 1.112, 1.112, 1.112]], [{"frame": 0.375}, "value", 8, [1, 1.356, 1.356, 1.356]], [{"frame": 0.4166666666666667}, "value", 8, [1, 1.046, 1.046, 1.046]], [{"frame": 0.4583333333333333}, "value", 8, [1, 1.179, 1.179, 1.179]], [{"frame": 0.5}, "value", 8, [1, 1.052, 1.052, 1.052]], [{"frame": 0.5416666666666666}, "value", 8, [1, 0.86, 0.86, 0.86]], [{"frame": 0.5833333333333334}, "value", 8, [1, 1.157, 1.157, 1.157]], [{"frame": 0.625}, "value", 8, [1, 1.279, 1.279, 1.279]], [{"frame": 0.6666666666666666}, "value", 8, [1, 1.356, 1.356, 1.356]], [{"frame": 0.7083333333333334}, "value", 8, [1, 0.959, 0.959, 0.959]], [{"frame": 0.75}, "value", 8, [1, 1.279, 1.279, 1.279]], [{"frame": 0.7916666666666666}, "value", 8, [1, 0.928, 0.928, 0.928]], [{"frame": 0.8333333333333334}, "value", 8, [1, 1.205, 1.205, 1.205]], [{"frame": 0.875}, "value", 8, [1, 1.112, 1.112, 1.112]], [{"frame": 0.9166666666666666}, "value", 8, [1, 1.279, 1.279, 1.279]], [{"frame": 0.9583333333333334}, "value", 8, [1, 1.356, 1.356, 1.356]], [{"frame": 1}, "value", 8, [1, 0.959, 0.959, 0.959]]], 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11]]]]]]], 0, 0, [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 3, 4, 5, 6, 7, 8, 1, 1]], [[{"name": "主角_新_007", "rect": [204, 2, 198, 228], "offset": [-10, 12], "originalSize": [280, 280], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "主角_新_004", "rect": [804, 2, 202, 224], "offset": [-12, 13], "originalSize": [280, 280], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "主角_新_001", "rect": [404, 2, 198, 228], "offset": [-10, 13], "originalSize": [280, 280], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "主角_新_002", "rect": [2, 2, 200, 228], "offset": [-11, 12], "originalSize": [280, 280], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "主角_新_005", "rect": [1210, 2, 200, 224], "offset": [-11, 13], "originalSize": [280, 280], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "主角_新_000", "rect": [604, 2, 198, 228], "offset": [-10, 12], "originalSize": [280, 280], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]]]]