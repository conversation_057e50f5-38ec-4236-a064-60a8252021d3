[1, ["ecpdLyjvZBwrvm+cedCcQy", "08uC5CC65FQLzClJXZov2d", "425bfYrrJAGZIbczR4hPq8"], ["node", "_spriteFrame", "_textureSetter", "root", "data", "_parent"], [["cc.Node", ["_name", "_prefab", "_children", "_trs", "_components", "_parent", "_contentSize"], 2, 4, 2, 7, 9, 1, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["node"], 3, 1]], [[1, 0, 1, 2, 2], [0, 0, 5, 4, 1, 6, 3, 2], [2, 0, 1, 2, 3, 4, 3], [4, 0, 2], [0, 0, 2, 1, 2], [0, 0, 2, 4, 1, 3, 2], [0, 0, 5, 2, 1, 6, 3, 2], [1, 1, 2, 1], [2, 0, 1, 2, 3, 3], [5, 0, 1]], [[[{"name": "图片_特效_剑林_001", "rect": [2, 2, 199, 173], "offset": [0, -1], "originalSize": [203, 175], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [2], [2]], [[[3, "剑林"], [4, "root", [-2], [7, -1, 0]], [5, "sprite", [-5, -6, -7, -8, -9], [[8, 0, false, -3, [10]], [9, -4]], [0, "24MVL74RhOqINJoHjo0+ow", 1, 0], [0, -77.946, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "position", 1, [2], [0, "1768HiGARNaoyAvM2o3PlD", 1, 0], [5, 1100, 220], [0, 109.832, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "03", 2, [[2, 0, false, -10, [0], 1]], [0, "9d9niqu19GY5emxXFYcDnL", 1, 0], [5, 290, 250], [-406.668, 71, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "01", 2, [[2, 0, false, -11, [2], 3]], [0, "328UzVc6dBWYEC5mOyDThw", 1, 0], [5, 290, 250], [0, 71, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "02", 2, [[2, 0, false, -12, [4], 5]], [0, "11VKTbp5tKT7j+BMBBPUot", 1, 0], [5, 290, 250], [-179.511, 71, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "04", 2, [[2, 0, false, -13, [6], 7]], [0, "41gnZ0zzhGq4OdLxsViEG1", 1, 0], [5, 290, 250], [216.122, 71, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "05", 2, [[2, 0, false, -14, [8], 9]], [0, "fdQxGWCJZAIq+EtcAg18C0", 1, 0], [5, 290, 250], [412.968, 71, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 3, 1, 0, -1, 3, 0, 0, 2, 0, 0, 2, 0, -1, 4, 0, -2, 5, 0, -3, 6, 0, -4, 7, 0, -5, 8, 0, 0, 4, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 4, 1, 2, 5, 3, 14], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1], [0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0]]]]