[1, ["6dpKjZP2tPibHRM/R6zr72", "02V5gDq8BF8LMNQzgkkUrA", "99IQtutkRKa6IFCT7KxeW2", "843iPZcFVNhrGfUeSWHsxQ", "2cYXWRokpOaa6zng822d0f"], ["value", "_textureSetter"], ["cc.SpriteFrame", ["cc.AnimationClip", ["_name", "_duration", "sample", "curveData"], 0, 11]], [[1, 0, 1, 2, 3, 4]], [[[[0, "冰封术_范围冰冻_消失", 0.7083333333333334, 24, [{}, "paths", 11, [{"11": {"props": {"scale": [], "position": [{"frame": 0, "value": [1.826, 8.072, 0]}, {"frame": 0.2916666666666667, "value": [2.554, -4.301, 0]}], "opacity": [{"frame": 0, "value": 255}, {"frame": 0.2916666666666667, "value": 0}], "active": [{"frame": 0, "value": false}]}}, "12": {"props": {"scale": [], "position": [{"frame": 0, "value": [-4.948, 3.839, 0]}, {"frame": 0.2916666666666667, "value": [-7.859, -8.534, 0]}], "opacity": [{"frame": 0, "value": 255}, {"frame": 0.2916666666666667, "value": 0}], "active": [{"frame": 0, "value": false}]}}, "08": {"props": {"scale": [], "angle": [{"frame": 0, "value": 64.597}, {"frame": 0.2916666666666667, "value": 74.157}], "position": [{"frame": 0, "value": [-21.596, 18.23, 0]}, {"frame": 0.2916666666666667, "value": [-20.868, 3.674, 0]}], "opacity": [{"frame": 0, "value": 255}, {"frame": 0.2916666666666667, "value": 0}], "active": [{"frame": 0, "value": false}]}}, "03": {"props": {"scale": [], "angle": [{"frame": 0, "value": 28.574999999999996}, {"frame": 0.2916666666666667, "value": 40.90899999999999}], "opacity": [{"frame": 0, "value": 255}, {"frame": 0.2916666666666667, "value": 0}], "active": [{"frame": 0, "value": false}]}}, "02": {"props": {"scale": [], "opacity": [{"frame": 0, "value": 255}, {"frame": 0.2916666666666667, "value": 0}], "angle": [{"frame": 0, "value": -29.626}, {"frame": 0.2916666666666667, "value": -47.478}], "active": [{"frame": 0, "value": false}]}}, "07": {"props": {"scale": [], "angle": [{"frame": 0, "value": -65.02}, {"frame": 0.2916666666666667, "value": -73.783}], "opacity": [{"frame": 0, "value": 255}, {"frame": 0.2916666666666667, "value": 0}], "position": [{"frame": 0, "value": [25.158, 22.86, 0]}, {"frame": 0.2916666666666667, "value": [25.158, 11.215, 0]}], "active": [{"frame": 0, "value": false}]}}, "06": {"props": {"scale": [], "position": [{"frame": 0, "value": [-88.442, 23.24, 0]}, {"frame": 0.2916666666666667, "value": [-88.186, 13.057, 0]}], "opacity": [{"frame": 0, "value": 255}, {"frame": 0.2916666666666667, "value": 0}], "active": [{"frame": 0, "value": false}]}}, "05": {"props": {"scale": [], "position": [{"frame": 0, "value": [93.252, 18.98, 0]}, {"frame": 0.2916666666666667, "value": [93.462, 10.999, 0]}], "opacity": [{"frame": 0, "value": 255}, {"frame": 0.2916666666666667, "value": 0}], "active": [{"frame": 0, "value": false}]}}, "08/08_1": {"props": {"opacity": []}}, "09/09_1": {"props": {"opacity": []}}, "14/14_1": {"props": {"opacity": []}}, "11/11_1": {"props": {"opacity": []}}, "03/03_1": {"props": {"opacity": []}}, "02/02_1": {"props": {"opacity": []}}, "10/10_1": {"props": {"opacity": []}}, "05/05_1": {"props": {"opacity": []}}, "01/01_1": {"props": {"opacity": [{"frame": 0, "value": 0}]}}, "07/07_1": {"props": {"opacity": []}}, "13/13_1": {"props": {"opacity": []}}, "12/12_1": {"props": {"opacity": []}}, "04/04_1": {"props": {"opacity": []}}, "15/15_1": {"props": {"opacity": []}}, "06/06_1": {"props": {"opacity": [], "position": [{"frame": 0, "value": [0, 0, 0]}, {"frame": 0.2916666666666667, "value": [0, 0, 0]}]}}}, "10", 11, [{}, "props", 11, [{"angle": [{"frame": 0, "value": 253.094}, {"frame": 0.2916666666666667, "value": 248.932}], "opacity": [{"frame": 0, "value": 255}, {"frame": 0.2916666666666667, "value": 0}], "active": [{"frame": 0, "value": false}]}, "scale", 12, [[[{"frame": 0}, "value", 8, [1, 0.714, 0.85, 0.837]], [{"frame": 0.2916666666666667}, "value", 8, [1, 0.581, 0.903, 0.837]]], 11, 11]]], "13", 11, [{}, "props", 11, [{"opacity": [{"frame": 0, "value": 255}, {"frame": 0.2916666666666667, "value": 0}], "active": [{"frame": 0, "value": false}]}, "scale", 12, [[[{"frame": 0}, "value", 8, [1, 0.53, 0.606, 0.606]], [{"frame": 0.2916666666666667}, "value", 8, [1, 0.42, 0.557, 0.653]]], 11, 11]]], "14", 11, [{}, "props", 11, [{"opacity": [{"frame": 0, "value": 255}, {"frame": 0.2916666666666667, "value": 0}], "active": [{"frame": 0, "value": false}]}, "scale", 12, [[[{"frame": 0}, "value", 8, [1, 0.53, 0.606, 0.606]], [{"frame": 0.2916666666666667}, "value", 8, [1, 0.425, 0.52, 0.606]]], 11, 11]]], "15", 11, [{}, "props", 11, [{"opacity": [{"frame": 0, "value": 255}, {"frame": 0.2916666666666667, "value": 0}], "active": [{"frame": 0, "value": false}]}, "scale", 12, [[[{"frame": 0}, "value", 8, [1, 0.639, 0.639, 0.639]], [{"frame": 0.2916666666666667}, "value", 8, [1, 0.578, 0.47, 0.639]]], 11, 11]]], "04", 11, [{}, "props", 11, [{"opacity": [{"frame": 0, "value": 255}, {"frame": 0.2916666666666667, "value": 0}], "active": [{"frame": 0, "value": false}]}, "scale", 12, [[[{"frame": 0}, "value", 8, [1, 0.794, 0.794, 0.794]], [{"frame": 0.2916666666666667}, "value", 8, [1, 0.794, 0.565, 0.794]]], 11, 11]]], "09", 11, [{}, "props", 11, [{"opacity": [{"frame": 0, "value": 255}, {"frame": 0.2916666666666667, "value": 0}], "angle": [{"frame": 0, "value": 107.00999999999999}, {"frame": 0.2916666666666667, "value": 110.94099999999999}], "active": [{"frame": 0, "value": false}]}, "scale", 12, [[[{"frame": 0}, "value", 8, [1, 0.714, 0.812, 0.837]], [{"frame": 0.2916666666666667}, "value", 8, [1, 0.604, 0.907, 0.837]]], 11, 11]]], "01", 11, [{}, "props", 11, [{"opacity": [{"frame": 0, "value": 255}, {"frame": 0.2916666666666667, "value": 0}], "active": [{"frame": 0, "value": false}]}, "scale", 12, [[[{"frame": 0}, "value", 8, [1, 1.348, 1.348, 1.348]], [{"frame": 0.2916666666666667}, "value", 8, [1, 1.348, 1.057, 1.348]]], 11, 11]]], "星/4", 11, [{}, "props", 11, [{}, "scale", 12, [[[{"frame": 0}, "value", 8, [1, 0, 0, 0.467]], [{"frame": 0.16666666666666666}, "value", 8, [1, 0.467, 0.467, 0.467]], [{"frame": 0.3333333333333333}, "value", 8, [1, 0, 0, 0.467]]], 11, 11, 11]]], "星/5", 11, [{}, "props", 11, [{}, "scale", 12, [[[{"frame": 0.041666666666666664}, "value", 8, [1, 0, 0, 0.467]], [{"frame": 0.20833333333333334}, "value", 8, [1, 0.467, 0.467, 0.467]], [{"frame": 0.375}, "value", 8, [1, 0, 0, 0.467]]], 11, 11, 11]]], "星/6", 11, [{}, "props", 11, [{}, "scale", 12, [[[{"frame": 0.125}, "value", 8, [1, 0, 0, 0.467]], [{"frame": 0.2916666666666667}, "value", 8, [1, 0.467, 0.467, 0.467]], [{"frame": 0.4583333333333333}, "value", 8, [1, 0, 0, 0.467]]], 11, 11, 11]]], "星/7", 11, [{}, "props", 11, [{"position": [{"frame": 0.20833333333333334, "value": [-51.292, -40.948, 0]}]}, "scale", 12, [[[{"frame": 0.20833333333333334}, "value", 8, [1, 0, 0, 0.467]], [{"frame": 0.375}, "value", 8, [1, 0.467, 0.467, 0.467]], [{"frame": 0.5416666666666666}, "value", 8, [1, 0, 0, 0.467]]], 11, 11, 11]]], "星/8", 11, [{}, "props", 11, [{}, "scale", 12, [[[{"frame": 0.25}, "value", 8, [1, 0, 0, 0.467]], [{"frame": 0.4166666666666667}, "value", 8, [1, 0.467, 0.467, 0.467]], [{"frame": 0.5833333333333334}, "value", 8, [1, 0, 0, 0.467]]], 11, 11, 11]]], "星/9", 11, [{}, "props", 11, [{}, "scale", 12, [[[{"frame": 0.375}, "value", 8, [1, 0, 0, 0.467]], [{"frame": 0.5416666666666666}, "value", 8, [1, 0.467, 0.467, 0.467]], [{"frame": 0.7083333333333334}, "value", 8, [1, 0, 0, 0.467]]], 11, 11, 11]]], "星/10", 11, [{}, "props", 11, [{"position": [{"frame": 0.2916666666666667, "value": [75.24, -57.802, 0]}]}, "scale", 12, [[[{"frame": 0.2916666666666667}, "value", 8, [1, 0, 0, 0.467]], [{"frame": 0.4583333333333333}, "value", 8, [1, 0.467, 0.467, 0.467]], [{"frame": 0.625}, "value", 8, [1, 0, 0, 0.467]]], 11, 11, 11]]], "烟/烟05", 11, [{}, "props", 11, [{"position": [{"frame": 0, "value": [1.999, -34.041, 0]}, {"frame": 0.625, "value": [-10.097, -74.955, 0]}], "opacity": [{"frame": 0, "value": 0}, {"frame": 0.25, "value": 200}, {"frame": 0.625, "value": 0}], "angle": [{"frame": 0, "value": 140.183}, {"frame": 0.625, "value": 81.28999999999999}]}, "scale", 12, [[[{"frame": 0}, "value", 8, [1, 0.724, 0.724, 0.724]], [{"frame": 0.625}, "value", 8, [1, 1.195, 1.195, 1.195]]], 11, 11]]], "烟/烟04", 11, [{}, "props", 11, [{"position": [{"frame": 0, "value": [-41.078, -23.926, 0]}, {"frame": 0.5833333333333334, "value": [-75.794, -43.401, 0]}], "opacity": [{"frame": 0, "value": 0}, {"frame": 0.25, "value": 200}, {"frame": 0.5833333333333334, "value": 0}], "angle": [{"frame": 0, "value": 140.183}, {"frame": 0.5833333333333334, "value": 121.59299999999999}]}, "scale", 12, [[[{"frame": 0}, "value", 8, [1, 0.504, 0.504, 0.504]], [{"frame": 0.5833333333333334}, "value", 8, [1, 0.913, 0.913, 0.913]]], 11, 11]]], "烟/烟03", 11, [{}, "props", 11, [{"position": [{"frame": 0, "value": [-74.202, -7.498, 0]}, {"frame": 0.625, "value": [-147.022, -19.352, 0]}], "opacity": [{"frame": 0, "value": 0}, {"frame": 0.25, "value": 200}, {"frame": 0.625, "value": 0}], "angle": [{"frame": 0, "value": 140.183}, {"frame": 0.625, "value": -12.96799999999999}]}, "scale", 12, [[[{"frame": 0}, "value", 8, [1, 0.724, 0.724, 0.724]], [{"frame": 0.625}, "value", 8, [1, 1.195, 1.195, 1.195]]], 11, 11]]], "烟/烟02", 11, [{}, "props", 11, [{"position": [{"frame": 0, "value": [16.066, -20.676, 0]}, {"frame": 0.6666666666666666, "value": [72.798, -46.078, 0]}], "opacity": [{"frame": 0, "value": 0}, {"frame": 0.2916666666666667, "value": 200}, {"frame": 0.6666666666666666, "value": 0}], "angle": [{"frame": 0, "value": 140.183}, {"frame": 0.6666666666666666, "value": 305.505}]}, "scale", 12, [[[{"frame": 0}, "value", 8, [1, 0.447, 0.447, 0.447]], [{"frame": 0.6666666666666666}, "value", 8, [1, 0.847, 0.847, 0.847]]], 11, 11]]], "烟/烟01", 11, [{}, "props", 11, [{"position": [{"frame": 0, "value": [65.346, -12.913, 0]}, {"frame": 0.6666666666666666, "value": [145.787, -31.541, 0]}], "opacity": [{"frame": 0, "value": 0}, {"frame": 0.2916666666666667, "value": 200}, {"frame": 0.6666666666666666, "value": 0}], "angle": [{"frame": 0, "value": 140.183}, {"frame": 0.6666666666666666, "value": 302.98699999999997}]}, "scale", 12, [[[{"frame": 0}, "value", 8, [1, 0.391, 0.391, 0.391]], [{"frame": 0.6666666666666666}, "value", 8, [1, 1.453, 1.453, 1.453]]], 11, 11]]], "烟/烟06", 11, [{}, "props", 11, [{"position": [{"frame": 0, "value": [-4.584, -10.189, 0]}, {"frame": 0.625, "value": [-14.584, -19.189, 0]}], "angle": [{"frame": 0, "value": 348.94399999999996}, {"frame": 0.625, "value": 400.32599999999996}], "opacity": [{"frame": 0, "value": 0}, {"frame": 0.2916666666666667, "value": 200}, {"frame": 0.625, "value": 0}]}, "scale", 12, [[[{"frame": 0}, "value", 8, [1, 0.979, 0.979, 0.979]], [{"frame": 0.625}, "value", 8, [1, 1.306, 1.306, 1.306]]], 11, 11]]], "寒冰突刺_爆_002", 11, [{"props": {"active": [{"frame": 0, "value": true}, {"frame": 0.2916666666666667, "value": false}]}}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 0], [{"frame": 0.08333333333333333}, "value", 6, 1], [{"frame": 0.16666666666666666}, "value", 6, 2], [{"frame": 0.25}, "value", 6, 3]], 11, 11, 11, 11]]]], "爆飞/爆飞01", 11, [{}, "props", 11, [{"position": [{"frame": 0, "value": [-9.937, -242.575, 0]}, {"frame": 0.20833333333333334, "value": [-9.937, 216.481, 0]}], "active": [{"frame": 0, "value": true}, {"frame": 0.25, "value": false}]}, "scale", 12, [[[{"frame": 0.125}, "value", 8, [1, 1.42, 1.42, 1.42]], [{"frame": 0.25}, "value", 8, [1, 0, 0, 1]]], 11, 11]]], "爆飞/爆飞02", 11, [{}, "props", 11, [{"position": [{"frame": 0, "value": [126.459, -779.544, 0]}, {"frame": 0.25, "value": [563.942, -1041.538, 0]}], "active": [{"frame": 0, "value": true}, {"frame": 0.2916666666666667, "value": false}]}, "scale", 12, [[[{"frame": 0.125}, "value", 8, [1, 1.641, 2.385, 1.641]], [{"frame": 0.25}, "value", 8, [1, 0, 0, 1.228]]], 11, 11]]], "爆飞/爆飞03", 11, [{}, "props", 11, [{"position": [{"frame": 0, "value": [123.932, -394.416, 0]}, {"frame": 0.25, "value": [410.763, -63.312, 0]}], "active": [{"frame": 0, "value": true}, {"frame": 0.25, "value": false}]}, "scale", 12, [[[{"frame": 0.125}, "value", 8, [1, 1.228, 1.228, 1.228]], [{"frame": 0.25}, "value", 8, [1, 0, 0, 1.228]]], 11, 11]]], "爆飞/爆飞04", 11, [{}, "props", 11, [{"position": [{"frame": 0, "value": [-205.059, -552.748, 0]}, {"frame": 0.2916666666666667, "value": [-518.422, -212.076, 0]}], "active": [{"frame": 0, "value": true}, {"frame": 0.25, "value": false}]}, "scale", 12, [[[{"frame": 0.16666666666666666}, "value", 8, [1, 0.82644, 1.50798, 0.82644]], [{"frame": 0.2916666666666667}, "value", 8, [1, 0, 0, 0.82644]]], 11, 11]]], "爆飞/爆飞05", 11, [{}, "props", 11, [{"position": [{"frame": 0, "value": [-160.482, -297.484, 0]}, {"frame": 0.2916666666666667, "value": [-218.061, 495.498, 0]}], "active": [{"frame": 0, "value": true}, {"frame": 0.25, "value": false}]}, "scale", 12, [[[{"frame": 0.08333333333333333}, "value", 8, [1, 2.427, 2.972, 2.427]], [{"frame": 0.2916666666666667}, "value", 8, [1, 0, 0, 1.773]]], 11, 11]]], "爆飞/爆飞06", 11, [{}, "props", 11, [{"position": [{"frame": 0, "value": [-127.408, -764.141, 0]}, {"frame": 0.2916666666666667, "value": [-21.109, -1492.255, 0]}], "active": [{"frame": 0, "value": true}, {"frame": 0.3333333333333333, "value": false}]}, "scale", 12, [[[{"frame": 0.125}, "value", 8, [1, 2.141, 3.212, 2.141]], [{"frame": 0.2916666666666667}, "value", 8, [1, 0, 0, 1.521]]], 11, 11]]], "爆飞/爆飞07", 11, [{}, "props", 11, [{"position": [{"frame": 0, "value": [97.071, -427.563, 0]}, {"frame": 0.2916666666666667, "value": [747.492, 184.485, 0]}], "active": [{"frame": 0, "value": true}, {"frame": 0.25, "value": false}]}, "scale", 12, [[[{"frame": 0.08333333333333333}, "value", 8, [1, 1.848, 2.771, 1.848]], [{"frame": 0.2916666666666667}, "value", 8, [1, 0, 0, 1.283]]], 11, 11]]]]]]], 0, 0, [0, 0, 0, 0], [0, 0, 0, 0], [1, 2, 3, 4]], [[{"name": "寒冰突刺_爆_002", "rect": [2, 214, 227, 200], "offset": [2, 1], "originalSize": [231, 224], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "寒冰突刺_爆_005", "rect": [2, 2, 227, 210], "offset": [2, -6], "originalSize": [231, 224], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "寒冰突刺_爆_004", "rect": [231, 2, 227, 208], "offset": [2, -3], "originalSize": [231, 224], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "寒冰突刺_爆_003", "rect": [231, 212, 227, 204], "offset": [2, -1], "originalSize": [231, 224], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]]]]