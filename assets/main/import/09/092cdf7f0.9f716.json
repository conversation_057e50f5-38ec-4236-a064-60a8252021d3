[1, ["ecpdLyjvZBwrvm+cedCcQy", "cf73jxyN9Jt47QTJU6ziYh", "99FwsL0hBG8bITfZ4/IwmK", "8855/VlrRKd6H0MSRnFxAU", "675ovJ2tVK2aLYfgPUWOMv"], ["node", "_textureSetter", "_parent", "_spriteFrame", "_N$barSprite", "scene"], [["cc.Node", ["_name", "_id", "_groupIndex", "_components", "_parent", "_contentSize", "_trs", "_children"], 0, 9, 1, 5, 7, 2], ["cc.Sprite", ["_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], "cc.SpriteFrame", ["cc.Widget", ["_alignFlags", "_left", "_right", "node"], 0, 1], ["cc.SceneAsset", ["_name", "asyncLoadAssets"], 1], ["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_parent", "_components", "_contentSize", "_anchorPoint", "_trs"], 1, 1, 2, 5, 5, 7], ["cc.<PERSON>", ["node", "_designResolution"], 3, 1, 5], ["cc.ProgressBar", ["_N$totalLength", "_N$progress", "node", "_N$barSprite"], 1, 1, 1], ["cc.Scene", ["_name", "_active", "_children", "_anchorPoint", "_trs"], 1, 2, 5, 7], ["b3266uY4ZNMcJ82rFmeUA84", ["node"], 3, 1], ["cc.Camera", ["_clearFlags", "_depth", "node"], 1, 1], ["cc.Label", ["_N$horizontalAlign", "_N$verticalAlign", "node", "_materials"], 1, 1, 3], ["19caaFZEFZK3ZPSiPsaBAIP", ["node"], 3, 1]], [[4, 0, 1, 3], [0, 0, 1, 7, 3, 5, 6, 3], [0, 0, 7, 3, 6, 2], [0, 0, 4, 7, 3, 5, 6, 2], [0, 0, 4, 7, 3, 6, 2], [0, 0, 4, 3, 5, 2], [0, 0, 2, 4, 3, 5, 3], [0, 0, 4, 3, 5, 6, 2], [0, 0, 1, 4, 3, 3], [5, 0, 1, 2, 3, 4, 5, 6, 3], [6, 0, 1, 1], [3, 0, 3, 2], [3, 0, 1, 2, 3, 4], [1, 0, 1, 2, 3, 4, 3], [1, 2, 3, 1], [1, 0, 1, 2, 3, 3], [7, 0, 1, 2, 3, 3], [8, 0, 1, 2, 3, 4, 3], [9, 0, 1], [10, 0, 1, 2, 3], [11, 0, 1, 2, 3, 3], [12, 0, 1]], [[[{"name": "default_progressbar", "rect": [0, 0, 30, 15], "offset": [0, 0], "originalSize": [30, 15], "capInsets": [10, 4, 10, 4]}], [2], 0, [0], [1], [1]], [[{"name": "default_progressbar_bg", "rect": [0, 0, 60, 15], "offset": [0, 0], "originalSize": [60, 15], "capInsets": [10, 4, 10, 4]}], [2], 0, [0], [1], [2]], [[[0, "start", null], [1, "<PERSON><PERSON>", "54cy285ZpIi78pXD4Bx/TX", [-3, -4, -5], [[10, -1, [5, 720, 1280]], [11, 45, -2]], [5, 720, 1280], [360, 640, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "update_panel", [-7, -8], [[12, 44, 350.472, -350.472, -6]], [350.472, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "update_process", 2, [-12], [[13, 1, 0, -9, [3], 4], [16, 600, 0, -11, -10]], [5, 600, 15], [0, 70.094, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "New Node", false, [1, -13], [0, 0, 0], [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "Start", 1, [2], [[18, -14]], [-360, -640, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "Main Camera", 1, [[19, 7, -1, -15]], [5, 720, 1280]], [6, "启动", 1, 1, [[14, -16, [0]]], [5, 720, 1680]], [7, "update_title", 2, [[20, 1, 1, -17, [1]]], [5, 0, 50.4], [0, 119.928, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "bar", 512, 3, [-18], [5, 0, 15], [0, 0, 0.5], [-300, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [15, 1, 0, 9, [2]], [8, "newScreenAdapter", "28bDtzs6tFp76WzCj79ol7", 4, [[21, -19]]]], 0, [0, 0, 1, 0, 0, 1, 0, -1, 6, 0, -2, 7, 0, -3, 5, 0, 0, 2, 0, -1, 8, 0, -2, 3, 0, 0, 3, 0, 4, 10, 0, 0, 3, 0, -1, 9, 0, -2, 11, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, -1, 10, 0, 0, 11, 0, 5, 4, 1, 2, 4, 2, 2, 5, 19], [0, 0, 0, 0, 0, 10], [-1, -1, -1, -1, 3, 3], [0, 0, 0, 0, 3, 4]]]]