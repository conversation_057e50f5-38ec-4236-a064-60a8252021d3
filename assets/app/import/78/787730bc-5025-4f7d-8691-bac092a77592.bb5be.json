[1, 0, 0, [["cc.TextAsset", ["_name", "text"], 1]], [[0, 0, 1, 3]], [[0, "ref_type_all.d", "interface IRefBase{}\r\n\r\ninterface IRefactivity extends IRefBase\r\n{\r\nname:IRefactivity_name\r\nplan:number\r\nresId:IRefactivity_resId\r\ntype:IRefactivity_type\r\n}\r\ninterface refGen{activity:IRefactivity}\r\ntype IRefactivity_name='充值好礼'|'充值豪礼'|'月卡特权'|'每日挑战'|'至尊月卡'\r\ntype IRefactivity_resId=100101|100201|100202|100301|100401|100501\r\ntype IRefactivity_type=1001|1002|1003|1004|1005|1006\r\n\r\ninterface IRefactivityOpen extends IRefBase\r\n{\r\nactivityResId:number\r\nendTime:string\r\nopenType:number\r\nresId:IRefactivityOpen_resId\r\nstartTime:string\r\n}\r\ninterface refGen{activityOpen:IRefactivityOpen}\r\ntype IRefactivityOpen_resId=1001001|1001002|1001003|1001004|1001005|1001006\r\n\r\ninterface IRefactivityTab extends IRefBase\r\n{\r\ndesc:string\r\nid:IRefactivityTab_id\r\nkey:IRefactivityTab_key\r\nname:IRefactivityTab_name\r\ntlType:any[]\r\n}\r\ninterface refGen{activityTab:IRefactivityTab}\r\ntype IRefactivityTab_id=1|2|3|4\r\ntype IRefactivityTab_key='dailyTask'|'monthCard'|'payGift1'|'payGift2'\r\ntype IRefactivityTab_name='充值好礼'|'充值豪礼'|'月卡豪礼'|'每日任务'\r\n\r\ninterface IRefad extends IRefBase\r\n{\r\ncdTime:number\r\ndesc:string\r\nhuaweiAdUnitId:string\r\nkey:IRefad_key\r\nlimit:number\r\nrewards:any[]\r\ntype:IRefad_type\r\n}\r\ninterface refGen{ad:IRefad}\r\ntype IRefad_key='buyEnd'|'commonBox'|'factoryGem'|'factoryGold'|'factoryPaper'|'goldShop'|'lingchong'|'randomSkill'|'skillBreak'|'superBox'|'sweep'|'winDouble'\r\ntype IRefad_type=1|10|11|12|2|3|4|5|6|7|8|9\r\n\r\ninterface IRefapprentice extends IRefBase\r\n{\r\nicon:string\r\nid:IRefapprentice_id\r\nname:IRefapprentice_name\r\nroleLevel:number\r\n}\r\ninterface refGen{apprentice:IRefapprentice}\r\ntype IRefapprentice_id=1|2|3|4|5\r\ntype IRefapprentice_name='学徒1'|'学徒2'|'学徒3'|'学徒4'|'学徒5'\r\n\r\ninterface IRefapprenticeLevel extends IRefBase\r\n{\r\nattack:number\r\nconsumeId:number\r\nlevel:IRefapprenticeLevel_level\r\nnumber:number\r\n}\r\ninterface refGen{apprenticeLevel:IRefapprenticeLevel}\r\ntype IRefapprenticeLevel_level=0|1|10|11|12|13|14|15|16|17|18|19|2|20|21|22|23|24|25|26|27|28|29|3|30|31|32|33|34|35|36|37|38|39|4|40|41|42|43|44|45|46|47|48|49|5|50|6|7|8|9\r\n\r\ninterface IRefaudioConfig extends IRefBase\r\n{\r\nkey:IRefaudioConfig_key\r\nvalue:string\r\n\r\nm4a?:string\r\n}\r\ninterface refGen{audioConfig:IRefaudioConfig}\r\ntype IRefaudioConfig_key='一声狮吼'|'万剑诀'|'主城背景音乐'|'关卡背景音乐'|'剑气斩'|'商店背景音乐'|'天雷网'|'寒冰突刺'|'寒冰箭'|'寒冰箭受击'|'巨石突刺'|'强敌来袭'|'御剑术'|'御剑术受击'|'惊雷咒'|'战斗失败音效'|'战斗背景音乐'|'战斗胜利音效'|'技能转盘'|'挑战背景音乐'|'旋风术'|'滚石'|'滚石受击'|'火焰弹'|'火焰弹受击'|'点击音效'|'登录背景音乐'|'获得奖励音效'|'获得铜钱音效'|'落石术'|'角色背景音乐'|'连环闪电'|'闪电球'|'风暴术'\r\n\r\ninterface IRefbattleModel extends IRefBase\r\n{\r\ncollisionType:number\r\nkey:IRefbattleModel_key\r\nmodel:string\r\nprototypeSizeX:number\r\n\r\nangle?:number\r\nprototypeSizeY?:number\r\n}\r\ninterface refGen{battleModel:IRefbattleModel}\r\ntype IRefbattleModel_key='塔防_城墙_回血'|'塔防_怪物子弹_冰晶雪女_子弹'|'塔防_怪物子弹_枯骨鸟_子弹'|'塔防_怪物子弹_白骨巫女_子弹'|'塔防_怪物子弹_蛟鱼精_子弹'|'塔防_怪物子弹_蛟龙_子弹'|'塔防_技能_万剑诀'|'塔防_技能_公用_单体冰冻'|'塔防_技能_公用_受击_持续燃烧'|'塔防_技能_公用_回血'|'塔防_技能_公用_毒爆'|'塔防_技能_公用_毒爆140'|'塔防_技能_公用_爆炸'|'塔防_技能_公用_爆炸140'|'塔防_技能_公用_眩晕'|'塔防_技能_公用_麻痹'|'塔防_技能_剑林'|'塔防_技能_剑气斩_加强'|'塔防_技能_剑气斩_普通'|'塔防_技能_千羽云鹤_飞剑'|'塔防_技能_天帝之拳_普通_坑'|'塔防_技能_天帝之拳_普通_坑80'|'塔防_技能_天帝之拳_普通_小坑'|'塔防_技能_天帝之拳_普通_小落石'|'塔防_技能_天帝之拳_普通_石头'|'塔防_技能_天帝之拳_毒拳_坑'|'塔防_技能_天帝之拳_毒拳_坑80'|'塔防_技能_天帝之拳_毒拳_石头'|'塔防_技能_天残脚_普通_坑'|'塔防_技能_天残脚_普通_坑80'|'塔防_技能_天残脚_普通_小坑'|'塔防_技能_天残脚_普通_小落石'|'塔防_技能_天残脚_普通_石头'|'塔防_技能_天雷网'|'塔防_技能_天雷网70'|'塔防_技能_寒冰突刺_范围冰冻_出现'|'塔防_技能_寒冰突刺_范围冰冻_循环'|'塔防_技能_寒冰突刺_范围冰冻_消失'|'塔防_技能_寒冰箭_三菱冰箭'|'塔防_技能_寒冰箭_冰冻'|'塔防_技能_寒冰箭_分裂子弹'|'塔防_技能_寒冰箭_受击'|'塔防_技能_寒冰箭_子弹'|'塔防_技能_小旋风术'|'塔防_技能_巨石突刺'|'塔防_技能_巨石突刺100'|'塔防_技能_御剑术_受击'|'塔防_技能_御剑术_子弹'|'塔防_技能_御剑术_火焰飞剑'|'塔防_技能_怪物护盾'|'塔防_技能_惊雷咒_天雷轰顶'|'塔防_技能_惊雷咒_小电球'|'塔防_技能_惊雷咒_爆炸'|'塔防_技能_惊雷咒_电磁场'|'塔防_技能_惊雷咒_雷电'|'塔防_技能_旋风术'|'塔防_技能_旋风术_闪电旋风'|'塔防_技能_毒云'|'塔防_技能_毒墙'|'塔防_技能_滚石_巨木_常态'|'塔防_技能_滚石_巨木_常态256'|'塔防_技能_滚石_巨木_常态60'|'塔防_技能_滚石_巨木_着火'|'塔防_技能_火墙'|'塔防_技能_火焰弹_余烬_坑'|'塔防_技能_火焰弹_受击_低级'|'塔防_技能_火焰弹_受击_高级'|'塔防_技能_火焰弹_子弹_低级'|'塔防_技能_火焰弹_子弹_高级'|'塔防_技能_火焰弹_溅射子弹'|'塔防_技能_荆棘'|'塔防_技能_落石术_余烬_坑'|'塔防_技能_落石术_普通_坑'|'塔防_技能_落石术_普通_坑80'|'塔防_技能_落石术_普通_小坑'|'塔防_技能_落石术_普通_小落石'|'塔防_技能_落石术_普通_石头'|'塔防_技能_落石术_燃烧陨石_坑'|'塔防_技能_落石术_燃烧陨石_坑80'|'塔防_技能_落石术_燃烧陨石_石头'|'塔防_技能_连环闪电_电链'|'塔防_技能_金手掌_普通_坑'|'塔防_技能_金手掌_普通_坑80'|'塔防_技能_金手掌_普通_小坑'|'塔防_技能_金手掌_普通_小落石'|'塔防_技能_金手掌_普通_石头'|'塔防_技能_长剑突刺'|'塔防_技能_闪电球'|'塔防_技能_闪电球50'|'塔防_技能_闪电球_黑洞'|'塔防_技能_阵法陷阱'|'塔防_技能_风刃陷阱'|'塔防_技能_风暴术_普通'|'塔防_技能_风暴术_普通-20'|'塔防_技能_风暴术_普通60'|'塔防_技能_风暴术_闪电风暴'\r\n\r\ninterface IRefbattleTokenConfig extends IRefBase\r\n{\r\nkey:IRefbattleTokenConfig_key\r\nvalue:number\r\n}\r\ninterface refGen{battleTokenConfig:IRefbattleTokenConfig}\r\ntype IRefbattleTokenConfig_key='levelUpCostEnd'|'maxLevel'|'resetNeedCostEnd'\r\n\r\ninterface IRefbattleTokenPlan extends IRefBase\r\n{\r\nopenCostItemNum:number\r\nopenCostItemResId:number\r\norder:number\r\nresId:IRefbattleTokenPlan_resId\r\nrewardPlan:number\r\n}\r\ninterface refGen{battleTokenPlan:IRefbattleTokenPlan}\r\ntype IRefbattleTokenPlan_resId=1|2|3\r\n\r\ninterface IRefbattleTokenReward extends IRefBase\r\n{\r\nlevel:IRefbattleTokenReward_level\r\nplan:number\r\nreward:any[]\r\n}\r\ninterface refGen{battleTokenReward:IRefbattleTokenReward}\r\ntype IRefbattleTokenReward_level=1|10|11|12|13|14|15|16|17|18|19|2|20|21|22|23|24|25|26|27|28|29|3|30|31|32|33|34|35|36|37|38|39|4|40|41|42|43|44|45|46|47|48|49|5|50|6|7|8|9\r\n\r\ninterface IRefbigNum extends IRefBase\r\n{\r\nflag:string\r\nminNum:string\r\nnum:string\r\n}\r\ninterface refGen{bigNum:IRefbigNum}\r\n\r\ninterface IRefbuySta extends IRefBase\r\n{\r\nbuyTimes:number\r\niconKey:string\r\nid:IRefbuySta_id\r\nnumbers:any[]\r\npayType:number\r\nresId:IRefbuySta_resId\r\nrewards:any[]\r\n}\r\ninterface refGen{buySta:IRefbuySta}\r\ntype IRefbuySta_id=1|2\r\ntype IRefbuySta_resId=0|6\r\n\r\ninterface IRefchapterAttrAddition extends IRefBase\r\n{\r\nattrStr:any[]\r\nnextDay:number\r\nstuckDay:number\r\n}\r\ninterface refGen{chapterAttrAddition:IRefchapterAttrAddition}\r\n\r\ninterface IRefdayChallengeActTask extends IRefBase\r\n{\r\nname:IRefdayChallengeActTask_name\r\norder:number\r\nparam:{ [key in string]: any }\r\nplan:number\r\nresId:IRefdayChallengeActTask_resId\r\nreward:any[]\r\ntargetType:number\r\nunlockChapter:number\r\n}\r\ninterface refGen{dayChallengeActTask:IRefdayChallengeActTask}\r\ntype IRefdayChallengeActTask_name='合成一次宝石'|'累计击杀2500只怪物'|'累计消耗50点体力'|'观看3次广告（含特权月卡跳过广告）'\r\ntype IRefdayChallengeActTask_resId=1|2|3|4\r\n\r\ninterface IRefequip extends IRefBase\r\n{\r\nconsumeId:number\r\nequipType:number\r\nicon:string\r\nkey:IRefequip_key\r\nname:IRefequip_name\r\nresId:IRefequip_resId\r\ntype:IRefequip_type\r\n}\r\ninterface refGen{equip:IRefequip}\r\ntype IRefequip_key='clothes'|'hat'|'necklace'|'ring'|'shoes'|'weapon'\r\ntype IRefequip_name='发冠'|'戒指'|'武器'|'道袍'|'靴子'|'项链'\r\ntype IRefequip_resId=40001|40002|40003|40004|40005|40006\r\ntype IRefequip_type=4\r\n\r\ninterface IRefequipConsume extends IRefBase\r\n{\r\nattribute:number\r\ndrawings:number\r\nequipLevel:number\r\ngold:number\r\n}\r\ninterface refGen{equipConsume:IRefequipConsume}\r\n\r\ninterface IReffirstRechargeShop extends IRefBase\r\n{\r\ncost:number\r\ndesc:string\r\nid:IReffirstRechargeShop_id\r\ninterval:number\r\npriceType:number\r\nrewards:any[]\r\n}\r\ninterface refGen{firstRechargeShop:IReffirstRechargeShop}\r\ntype IReffirstRechargeShop_id=1|2|3\r\n\r\ninterface IReffundPlan extends IRefBase\r\n{\r\nopenCostItemNum:number\r\nopenCostItemResId:number\r\norder:number\r\nresId:IReffundPlan_resId\r\nrewardPlan:number\r\nstage:number\r\ntype:IReffundPlan_type\r\n}\r\ninterface refGen{fundPlan:IReffundPlan}\r\ntype IReffundPlan_resId=1|2|3\r\ntype IReffundPlan_type=1\r\n\r\ninterface IReffundReward extends IRefBase\r\n{\r\nlevel:IReffundReward_level\r\nplan:number\r\nreward:any[]\r\n}\r\ninterface refGen{fundReward:IReffundReward}\r\ntype IReffundReward_level=1|2\r\n\r\ninterface IRefgameCircleConfig extends IRefBase\r\n{\r\nlikeNum:number\r\nlikeReward:any[]\r\n}\r\ninterface refGen{gameCircleConfig:IRefgameCircleConfig}\r\n\r\ninterface IRefgameConfig extends IRefBase\r\n{\r\nkey:IRefgameConfig_key\r\nvalue:any\r\n}\r\ninterface refGen{gameConfig:IRefgameConfig}\r\ntype IRefgameConfig_key='apprenticeNum'|'battlePerWaveGold'|'battlePerWaveItem'|'battleRewardWave'|'battleSixWaveBase'|'battleSixWaveGold'|'battleStartGold'|'battleTimeLimit'|'bossRewardWeight'|'bossSkillLevelUpId'|'changeDay'|'chanllengeOpen'|'defaultHeadFrameIcon'|'defaultHeadIcon'|'defaultSkillLevelUpId'|'defaultSkin'|'douYinReward'|'endCD'|'endLimit'|'endMailHourList'|'endMailSendDay'|'endMailValue'|'failTipShowLevel'|'freeRenameCount'|'gemQuality'|'gemTabOpen'|'invite_out_time'|'invite_reward1'|'invite_reward2'|'lockMonsterBoxRefreshSec'|'lockTowerOpen'|'mailValid'|'maxMailNum'|'miningBuyCostCoin'|'miningEndRatio'|'miningMaxGem'|'monsterOutTime'|'openWall'|'renameCostGem'|'renameItemId'|'roleDefaultAtk'|'seckillLimit'|'secretRealmOpenOrder'|'secretRealmRefreshDay'|'selectTotal'|'serverRankKeepDay'|'serverRankShowDay'|'sevenTaskShowDay'|'shareReward'|'singleSelectNums'|'spiritAnimalUnlockLevel'|'sweepFreeTimes'|'wallHpState'|'workShopAdd'|'workShopSpeedCost'|'xinhaofuli1'|'xinhaofuli2'|'xinhaofuli3'\r\n\r\ninterface IRefgem extends IRefBase\r\n{\r\ndesc:string\r\nequipId:number\r\ngemType:number\r\nicon:string\r\nresId:IRefgem_resId\r\ntype:IRefgem_type\r\n}\r\ninterface refGen{gem:IRefgem}\r\ntype IRefgem_resId=1000000|1010000|1020000|1030000|1040000|1050000\r\ntype IRefgem_type=7\r\n\r\ninterface IRefgemAttribute extends IRefBase\r\n{\r\ngemIds:any[]\r\nid:IRefgemAttribute_id\r\nmodify:any[]\r\nmodifyDesc:string\r\npro:number\r\nquality:IRefgemAttribute_quality\r\ntype:IRefgemAttribute_type\r\n\r\nskillId?:number\r\n}\r\ninterface refGen{gemAttribute:IRefgemAttribute}\r\ntype IRefgemAttribute_id=1|10|1001|1002|1003|1004|1005|1006|1007|1008|1009|1010|1012|1013|1015|1017|1018|1019|1023|1026|1031|1054|1055|1059|1060|12|13|15|17|18|19|2|2001|2002|2003|2004|2005|2006|2007|2008|2009|2010|2011|2012|2013|2014|2015|2016|2017|2018|2019|2020|2021|2023|2024|2026|2031|2054|2055|2058|2059|2060|2061|2064|2067|2071|2073|2074|2075|2084|2093|2101|23|3|3001|3002|3003|3004|3005|3006|3007|3008|3009|3010|3011|3012|3013|3014|3015|3016|3017|3018|3019|3020|3021|3022|3023|3024|3025|3026|3027|3031|3036|3037|3038|3039|3044|3045|3046|3047|3048|3049|3050|3051|3052|3053|3054|3055|3058|3059|3060|3061|3062|3063|3064|3065|3066|3067|3069|3070|3071|3073|3074|3075|3076|3078|3079|3080|3084|3085|3086|3088|3092|3093|3095|3096|3098|31|3101|3102|4|4001|4002|4003|4004|4005|4006|4007|4008|4009|4010|4011|4012|4013|4014|4015|4016|4017|4018|4019|4020|4021|4022|4023|4024|4025|4026|4027|4028|4031|4033|4034|4036|4037|4038|4039|4044|4045|4046|4047|4048|4049|4050|4051|4052|4053|4054|4055|4056|4057|4058|4060|4061|4062|4063|4064|4065|4066|4067|4068|4069|4070|4071|4072|4073|4074|4075|4076|4077|4078|4079|4080|4081|4082|4084|4085|4088|4092|4093|4095|4096|4097|4098|4100|4101|4102|4103|4104|4105|4106|4107|5|5001|5002|5003|5004|5005|5006|5007|5008|5009|5010|5011|5012|5013|5015|5016|5017|5018|5019|5020|5021|5022|5023|5024|5025|5026|5027|5028|5029|5031|5033|5034|5035|5036|5037|5038|5039|5044|5045|5046|5047|5048|5049|5050|5051|5052|5053|5054|5055|5056|5057|5058|5060|5061|5062|5064|5065|5066|5069|5070|5071|5072|5075|5076|5077|5078|5079|5080|5084|5087|5088|5089|5091|5092|5093|5095|5096|5098|5099|5100|5101|5103|5108|54|59|6|60|6001|6002|6003|6004|6005|6006|6007|6008|6009|6010|6011|6012|6013|6015|6016|6017|6018|6019|6023|6024|6025|6026|6027|6028|6029|6030|6031|6032|6033|6034|6035|6036|6037|6038|6039|6044|6045|6046|6047|6048|6049|6050|6051|6052|6053|6054|6055|6060|6061|6062|6064|6066|6069|6071|6072|6080|6082|6083|6084|6088|6090|6093|6094|6098|6099|6100|6101|6103|7|8|9\r\ntype IRefgemAttribute_quality=1|2|3|4|5|6|7\r\ntype IRefgemAttribute_type=1|10|1000|1001|1002|11|12|13|14|15|16|17|18|19|2|20|21|22|23|24|25|26|27|28|29|3|31|32|33|34|35|36|37|39|4|40|41|42|43|44|45|46|48|49|5|50|51|52|53|55|56|57|58|59|6|60|62|63|64|65|66|68|69|7|70|71|72|73|75|76|77|78|79|8|80|81|83|84|85|86|87|89|9|90|91|92|93|94|96|97|98|99\r\n\r\ninterface IRefgemAttrModifier extends IRefBase\r\n{\r\ndesc:string\r\nid:IRefgemAttrModifier_id\r\nmodify:any[]\r\n}\r\ninterface refGen{gemAttrModifier:IRefgemAttrModifier}\r\ntype IRefgemAttrModifier_id=10000101|10000102|10000103|10000104|10000105|10000106|10000107\r\n\r\ninterface IRefgemBox extends IRefBase\r\n{\r\ncost:number\r\ncost2:number\r\ncost3:number\r\ndesc:string\r\nfreeTimes:number\r\nid:IRefgemBox_id\r\nlimit:number\r\npriceType:number\r\npriceType3:number\r\nquality:IRefgemBox_quality\r\nquality2:number\r\nrewards:any[]\r\n}\r\ninterface refGen{gemBox:IRefgemBox}\r\ntype IRefgemBox_id=1|2\r\ntype IRefgemBox_quality=3|4\r\n\r\ninterface IRefgemQuality extends IRefBase\r\n{\r\nconsume:number\r\nconsume1:number\r\ndesc:string\r\nicon:string\r\nquality:IRefgemQuality_quality\r\n}\r\ninterface refGen{gemQuality:IRefgemQuality}\r\ntype IRefgemQuality_quality=1|2|3|4|5|6|7\r\n\r\ninterface IRefgemShop extends IRefBase\r\n{\r\ncost:number\r\ndesc:string\r\ndoubleTimes:number\r\nid:IRefgemShop_id\r\nlimit:number\r\npriceType:number\r\nrewards:any[]\r\n}\r\ninterface refGen{gemShop:IRefgemShop}\r\ntype IRefgemShop_id=1|2|3|4|5|6\r\n\r\ninterface IRefgiftShop extends IRefBase\r\n{\r\ncost:number\r\ndesc:string\r\ndiscount:number\r\nid:IRefgiftShop_id\r\nlimit:number\r\npriceType:number\r\nrewards:any[]\r\ntype:IRefgiftShop_type\r\ntype2:number\r\n\r\nextend?:{ [key in string]: any }\r\ntitle?:string\r\n}\r\ninterface refGen{giftShop:IRefgiftShop}\r\ntype IRefgiftShop_id=1|101|102|103|104|105|2|201|202|204|3|301|302|303|304|311|312|313|314|321|322|323|324|331|332|333|334|341|342|343|344|345|351|352|353|354|355|4|5\r\ntype IRefgiftShop_type=1|2|3|4\r\n\r\ninterface IRefgoldShop extends IRefBase\r\n{\r\nadTimes:number\r\ncost:number\r\ndesc:string\r\ndiscount:number\r\nfreeTimes:number\r\nicon:string\r\nid:IRefgoldShop_id\r\ninterval:number\r\nlimit:number\r\npriceType:number\r\nrewards:any[]\r\n}\r\ninterface refGen{goldShop:IRefgoldShop}\r\ntype IRefgoldShop_id=1|2|3\r\n\r\ninterface IRefguanka extends IRefBase\r\n{\r\nattrRate:number\r\norder:number\r\ntime:number\r\ntlMonsterNum:any[]\r\ntlRate:any[]\r\ntype:IRefguanka_type\r\n}\r\ninterface refGen{guanka:IRefguanka}\r\ntype IRefguanka_type=1|2|3|4\r\n\r\ninterface IRefguanka1 extends IRefBase\r\n{\r\nattrRate:number\r\norder:number\r\ntime:number\r\ntlMonsterNum:any[]\r\ntlRate:any[]\r\ntype:IRefguanka1_type\r\n}\r\ninterface refGen{guanka1:IRefguanka1}\r\ntype IRefguanka1_type=1|2\r\n\r\ninterface IRefguankaChapter extends IRefBase\r\n{\r\nchapter:number\r\nchapterIcon:string\r\npool:number\r\nstar:IRefguankaChapter_star\r\n}\r\ninterface refGen{guankaChapter:IRefguankaChapter}\r\ntype IRefguankaChapter_star=12|15|6|9\r\n\r\ninterface IRefguankaGift extends IRefBase\r\n{\r\ncost:number\r\nid:IRefguankaGift_id\r\nlevel:IRefguankaGift_level\r\npriceType:number\r\nquality:IRefguankaGift_quality\r\ntime:number\r\n}\r\ninterface refGen{guankaGift:IRefguankaGift}\r\ntype IRefguankaGift_id=1|2|3|4|5|6|7\r\ntype IRefguankaGift_level=133|183|233|283|33|333|83\r\ntype IRefguankaGift_quality=4|5|6|7\r\n\r\ninterface IRefguankaReward extends IRefBase\r\n{\r\nbattleTime:number\r\nbuff:{ [key in string]: any }\r\nchapter:number\r\ndiamond:any[]\r\ndiamondNum:any[]\r\nend:number\r\nequipItem:number\r\nexp:number\r\ngemNum:any[]\r\ngemQuality:any[]\r\ngold:number\r\nguanKaType:number\r\nguankaHp:number\r\nlevel:IRefguankaReward_level\r\npetFood:any[]\r\nshowPer:any[]\r\nskillItem:number\r\nstage:number\r\ntlBoss:any[]\r\ntlMonster:any[]\r\n}\r\ninterface refGen{guankaReward:IRefguankaReward}\r\ntype IRefguankaReward_level=1|10|100|101|102|103|104|105|106|107|108|109|11|110|111|112|113|114|115|116|117|118|119|12|120|121|122|123|124|125|126|127|128|129|13|130|131|132|133|134|135|136|137|138|139|14|140|141|142|143|144|145|146|147|148|149|15|150|151|152|153|154|155|156|157|158|159|16|160|161|162|163|164|165|166|167|168|169|17|170|171|172|173|174|175|176|177|178|179|18|180|181|182|183|184|185|186|187|188|189|19|190|191|192|193|194|195|196|197|198|199|2|20|200|201|202|203|204|205|206|207|208|209|21|210|211|212|213|214|215|216|217|218|219|22|220|221|222|223|224|225|226|227|228|229|23|230|231|232|233|234|235|236|237|238|239|24|240|241|242|243|244|245|246|247|248|249|25|250|251|252|253|254|255|256|257|258|259|26|260|261|262|263|264|265|266|267|268|269|27|270|271|272|273|274|275|276|277|278|279|28|280|281|282|283|284|285|286|287|288|289|29|290|291|292|293|294|295|296|297|298|299|3|30|300|301|302|303|304|305|306|307|308|309|31|310|311|312|313|314|315|316|317|318|319|32|320|321|322|323|324|325|326|327|328|329|33|330|331|332|333|334|335|336|337|338|339|34|340|341|342|343|344|345|346|347|348|349|35|350|351|352|353|354|355|356|357|358|359|36|360|361|362|363|364|365|366|367|368|369|37|370|371|372|373|374|375|376|377|378|379|38|380|381|382|383|39|4|40|41|42|43|44|45|46|47|48|49|5|50|51|52|53|54|55|56|57|58|59|6|60|61|62|63|64|65|66|67|68|69|7|70|71|72|73|74|75|76|77|78|79|8|80|81|82|83|84|85|86|87|88|89|9|90|91|92|93|94|95|96|97|98|99\r\n\r\ninterface IRefguankaReward1 extends IRefBase\r\n{\r\nbattleTime:number\r\nbuff:{ [key in string]: any }\r\nguanKaType:number\r\nguankaHp:number\r\nlevel:IRefguankaReward1_level\r\ntlBoss:any[]\r\ntlMonster:any[]\r\n}\r\ninterface refGen{guankaReward1:IRefguankaReward1}\r\ntype IRefguankaReward1_level=1|10|100|101|102|103|104|105|106|107|108|109|11|110|111|112|113|114|115|116|117|118|119|12|120|121|122|123|124|125|126|127|128|129|13|130|131|132|133|134|135|136|137|138|139|14|140|141|142|143|144|145|146|147|148|149|15|150|151|152|153|154|155|156|157|158|159|16|160|161|162|163|164|165|166|167|168|169|17|170|171|172|173|174|175|176|177|178|179|18|180|181|182|183|184|185|186|187|188|189|19|190|191|192|193|194|195|196|197|198|199|2|20|200|201|202|203|204|205|206|207|208|209|21|210|211|212|213|214|215|216|217|218|219|22|220|221|222|223|224|225|226|227|228|229|23|230|231|232|233|234|235|236|237|238|239|24|240|241|242|243|244|245|246|247|248|249|25|250|251|252|253|254|255|256|257|258|259|26|260|261|262|263|264|265|266|267|268|269|27|270|271|272|273|274|275|276|277|278|279|28|280|281|282|283|284|285|286|287|288|289|29|290|291|292|293|294|295|296|297|298|299|3|30|300|31|32|33|34|35|36|37|38|39|4|40|41|42|43|44|45|46|47|48|49|5|50|51|52|53|54|55|56|57|58|59|6|60|61|62|63|64|65|66|67|68|69|7|70|71|72|73|74|75|76|77|78|79|8|80|81|82|83|84|85|86|87|88|89|9|90|91|92|93|94|95|96|97|98|99\r\n\r\ninterface IRefguankaReward2 extends IRefBase\r\n{\r\nbattleTime:number\r\nbuff:{ [key in string]: any }\r\nguanKaType:number\r\nguankaHp:number\r\nlevel:IRefguankaReward2_level\r\nstage:number\r\ntlBoss:any[]\r\ntlMonster:any[]\r\n}\r\ninterface refGen{guankaReward2:IRefguankaReward2}\r\ntype IRefguankaReward2_level=1|10|100|10001|10002|10003|10004|10005|10006|10007|10008|10009|10010|10011|10012|10013|10014|10015|10016|10017|10018|10019|10020|10021|10022|10023|10024|10025|10026|10027|10028|10029|10030|10031|10032|10033|10034|10035|10036|10037|10038|10039|10040|10041|10042|10043|10044|10045|10046|10047|10048|10049|10050|10051|10052|10053|10054|10055|10056|10057|10058|10059|10060|10061|10062|10063|10064|10065|10066|10067|10068|10069|10070|10071|10072|10073|10074|10075|10076|10077|10078|10079|10080|10081|10082|10083|10084|10085|10086|10087|10088|10089|10090|10091|10092|10093|10094|10095|10096|10097|10098|10099|10100|11|12|13|14|15|16|17|18|19|2|20|20001|20002|20003|20004|20005|20006|20007|20008|20009|20010|20011|20012|20013|20014|20015|20016|20017|20018|20019|20020|20021|20022|20023|20024|20025|20026|20027|20028|20029|20030|20031|20032|20033|20034|20035|20036|20037|20038|20039|20040|20041|20042|20043|20044|20045|20046|20047|20048|20049|20050|20051|20052|20053|20054|20055|20056|20057|20058|20059|20060|20061|20062|20063|20064|20065|20066|20067|20068|20069|20070|20071|20072|20073|20074|20075|20076|20077|20078|20079|20080|20081|20082|20083|20084|20085|20086|20087|20088|20089|20090|20091|20092|20093|20094|20095|20096|20097|20098|20099|20100|21|22|23|24|25|26|27|28|29|3|30|30001|30002|30003|30004|30005|30006|30007|30008|30009|30010|30011|30012|30013|30014|30015|30016|30017|30018|30019|30020|30021|30022|30023|30024|30025|30026|30027|30028|30029|30030|30031|30032|30033|30034|30035|30036|30037|30038|30039|30040|30041|30042|30043|30044|30045|30046|30047|30048|30049|30050|30051|30052|30053|30054|30055|30056|30057|30058|30059|30060|30061|30062|30063|30064|30065|30066|30067|30068|30069|30070|30071|30072|30073|30074|30075|30076|30077|30078|30079|30080|30081|30082|30083|30084|30085|30086|30087|30088|30089|30090|30091|30092|30093|30094|30095|30096|30097|30098|30099|30100|31|32|33|34|35|36|37|38|39|4|40|40001|40002|40003|40004|40005|40006|40007|40008|40009|40010|40011|40012|40013|40014|40015|40016|40017|40018|40019|40020|40021|40022|40023|40024|40025|40026|40027|40028|40029|40030|40031|40032|40033|40034|40035|40036|40037|40038|40039|40040|40041|40042|40043|40044|40045|40046|40047|40048|40049|40050|40051|40052|40053|40054|40055|40056|40057|40058|40059|40060|40061|40062|40063|40064|40065|40066|40067|40068|40069|40070|40071|40072|40073|40074|40075|40076|40077|40078|40079|40080|40081|40082|40083|40084|40085|40086|40087|40088|40089|40090|40091|40092|40093|40094|40095|40096|40097|40098|40099|40100|41|42|43|44|45|46|47|48|49|5|50|50001|50002|50003|50004|50005|50006|50007|50008|50009|50010|50011|50012|50013|50014|50015|50016|50017|50018|50019|50020|50021|50022|50023|50024|50025|50026|50027|50028|50029|50030|50031|50032|50033|50034|50035|50036|50037|50038|50039|50040|50041|50042|50043|50044|50045|50046|50047|50048|50049|50050|50051|50052|50053|50054|50055|50056|50057|50058|50059|50060|50061|50062|50063|50064|50065|50066|50067|50068|50069|50070|50071|50072|50073|50074|50075|50076|50077|50078|50079|50080|50081|50082|50083|50084|50085|50086|50087|50088|50089|50090|50091|50092|50093|50094|50095|50096|50097|50098|50099|50100|51|52|53|54|55|56|57|58|59|6|60|61|62|63|64|65|66|67|68|69|7|70|71|72|73|74|75|76|77|78|79|8|80|81|82|83|84|85|86|87|88|89|9|90|91|92|93|94|95|96|97|98|99\r\n\r\ninterface IRefguankaSkillLevelUp extends IRefBase\r\n{\r\nexp:number\r\nlevel:IRefguankaSkillLevelUp_level\r\ntype:IRefguankaSkillLevelUp_type\r\n}\r\ninterface refGen{guankaSkillLevelUp:IRefguankaSkillLevelUp}\r\ntype IRefguankaSkillLevelUp_level=1|10|11|12|13|14|15|16|17|18|19|2|3|4|5|6|7|8|9\r\ntype IRefguankaSkillLevelUp_type=1|2|3\r\n\r\ninterface IRefguankaStar extends IRefBase\r\n{\r\npool:number\r\nreward:any[]\r\nstar:IRefguankaStar_star\r\n}\r\ninterface refGen{guankaStar:IRefguankaStar}\r\ntype IRefguankaStar_star=1|10|11|12|13|14|15|2|3|4|5|6|7|8|9\r\n\r\ninterface IRefhead extends IRefBase\r\n{\r\ndesc:string\r\nicon:string\r\nname:IRefhead_name\r\nquality:IRefhead_quality\r\nresId:IRefhead_resId\r\ntype:IRefhead_type\r\n}\r\ninterface refGen{head:IRefhead}\r\ntype IRefhead_name='灵狐•玄珠'|'灵狐•红莲'|'灵狐•青叶'|'鲛女'|'默认头像'\r\ntype IRefhead_quality=4\r\ntype IRefhead_resId=70001|70002|70003|70004|70005\r\ntype IRefhead_type=2\r\n\r\ninterface IReficon extends IRefBase\r\n{\r\nkey:IReficon_key\r\npath:string\r\n}\r\ninterface refGen{icon:IReficon}\r\ntype IReficon_key='主城_图标_图鉴1'|'主城_图标_图鉴2'|'主城_图标_城墙1'|'主城_图标_城墙2'|'主城_图标_灵兽1'|'主城_图标_灵兽2'|'主城_图标_锻造1'|'主城_图标_锻造2'|'图标_九天息壤'|'图标_体力'|'图标_体力02'|'图标_图纸_帽子'|'图标_图纸_戒指'|'图标_图纸_武器'|'图标_图纸_衣服'|'图标_图纸_问号'|'图标_图纸_鞋子'|'图标_图纸_项链'|'图标_头像_主角'|'图标_头像_冰雪鲛女'|'图标_头像_土域灵狐_01'|'图标_头像_土域灵狐_02'|'图标_头像_土域灵狐_03'|'图标_宝石_帽子'|'图标_宝石_戒指'|'图标_宝石_橙色'|'图标_宝石_武器'|'图标_宝石_白色'|'图标_宝石_粉色'|'图标_宝石_紫色'|'图标_宝石_红色'|'图标_宝石_绿色'|'图标_宝石_蓝色'|'图标_宝石_衣服'|'图标_宝石_金色'|'图标_宝石_鞋子'|'图标_宝石_项链'|'图标_宝石抽取券'|'图标_幻灵丹_01'|'图标_幻灵丹_02'|'图标_幻灵丹_03'|'图标_幻灵丹_04'|'图标_幻灵丹_05'|'图标_幻灵丹_06'|'图标_幻灵玉'|'图标_战斗选关_按钮_历练'|'图标_战斗选关_按钮_战令'|'图标_战斗选关_按钮_排名'|'图标_战斗选关_按钮_活动'|'图标_战斗选关_按钮_设置'|'图标_战斗选关_按钮_邮件'|'图标_战斗选关_按钮_首充礼包'|'图标_技能_万剑诀'|'图标_技能_伤害减免'|'图标_技能_伤害反弹'|'图标_技能_冰系法术精通'|'图标_技能_剑气斩'|'图标_技能_剑系精通'|'图标_技能_召唤石狮'|'图标_技能_城墙修复'|'图标_技能_天雷网'|'图标_技能_寒冰突刺'|'图标_技能_寒冰箭'|'图标_技能_巨石突刺'|'图标_技能_御剑术'|'图标_技能_惊雷咒'|'图标_技能_旋风术'|'图标_技能_毒障剑林'|'图标_技能_流沙阵法'|'图标_技能_滚石'|'图标_技能_火焰弹'|'图标_技能_落石术'|'图标_技能_连环闪电'|'图标_技能_长剑破空'|'图标_技能_闪电球'|'图标_技能_雷系法术精通'|'图标_技能_风暴术'|'图标_技能_风雷锯齿'|'图标_改名卡'|'图标_新手道书'|'图标_法术卷轴_万剑诀'|'图标_法术卷轴_剑气斩'|'图标_法术卷轴_天雷网'|'图标_法术卷轴_寒冰突刺'|'图标_法术卷轴_寒冰箭'|'图标_法术卷轴_巨石突刺'|'图标_法术卷轴_御剑术'|'图标_法术卷轴_惊雷咒'|'图标_法术卷轴_技能'|'图标_法术卷轴_旋风术'|'图标_法术卷轴_滚石'|'图标_法术卷轴_火焰弹'|'图标_法术卷轴_空白'|'图标_法术卷轴_落石术'|'图标_法术卷轴_连环闪电'|'图标_法术卷轴_闪电球'|'图标_法术卷轴_风暴术'|'图标_洗炼石'|'图标_灵兽抽取券'|'图标_灵宠口粮'|'图标_灵宠口粮_概率'|'图标_灵宠技能_九天惊雷'|'图标_灵宠技能_冰封千里'|'图标_灵宠技能_剑定天下'|'图标_灵宠技能_剑气扩散'|'图标_灵宠技能_剑荡四方'|'图标_灵宠技能_双重陨石'|'图标_灵宠技能_天外陨石'|'图标_灵宠技能_天雷降世'|'图标_灵宠技能_夺命冰息'|'图标_灵宠技能_寒冰扩散'|'图标_灵宠技能_无双剑气'|'图标_灵宠技能_无限风暴'|'图标_灵宠技能_毒雾缭绕'|'图标_灵宠技能_混沌陨石'|'图标_灵宠技能_爆裂冰晶'|'图标_灵宠技能_致命之雾'|'图标_灵宠技能_迷雾扩散'|'图标_灵宠技能_陨石风暴'|'图标_灵宠技能_雷神之力'|'图标_灵宠技能_雷霆万钧'|'图标_灵宠技能_风之祝愿'|'图标_灵宠技能_风卷残云'|'图标_灵宠技能_风起云涌'|'图标_灵宠技能_魔雾缥缈'|'图标_灵石'|'图标_灵石_概率'|'图标_突破丹'|'图标_经验值'|'图标_补天神石'|'图标_装备_帽子'|'图标_装备_戒指'|'图标_装备_武器'|'图标_装备_衣服'|'图标_装备_靴子'|'图标_装备_项链'|'图标_货币_几个铜钱'|'图标_货币_大量铜钱'|'图标_货币_少量铜钱'|'图标_铜币'|'图标_随机传奇宝石'|'图标_随机史诗宝石'|'图标_随机宝石箱'|'图标_随机神话宝石'|'图标_随机稀有宝石'|'图标_随机绝世宝石'|'城墙_第一级_01'|'城墙_第三级_01'|'城墙_第二级_01'|'城墙_第四级_01'|'战斗选关_按钮_七日狂欢'|'战斗选关_按钮_开服冲榜'|'战斗选关_按钮_欢度元旦'|'战斗选关_按钮_每日分享'|'战斗选关_按钮_皮肤礼包'|'战斗选关_按钮_礼包'|'战斗选关_按钮_签到豪礼'|'战斗选关_按钮_通关礼包'|'战斗选关_按钮_限时活动'|'战斗选关_章节场景01'|'战斗选关_章节场景02'|'战斗选关_章节场景03'|'战斗选关_章节场景04'|'战斗选关_章节场景05'|'灵宠_灵宠全身_九尾灵狐'|'灵宠_灵宠全身_千羽灵鹤'|'灵宠_灵宠全身_司雪'|'灵宠_灵宠全身_灵石貔貅'|'灵宠_灵宠全身_青龙'|'灵宠_灵宠全身_鸩'|'灵宠_灵宠头像_九尾灵狐'|'灵宠_灵宠头像_千羽灵鹤'|'灵宠_灵宠头像_司雪'|'灵宠_灵宠头像_灵石貔貅'|'灵宠_灵宠头像_青龙'|'灵宠_灵宠头像_鸩'|'灵宠_灵宠技能_九尾灵狐'|'灵宠_灵宠技能_千羽灵鹤'|'灵宠_灵宠技能_司雪'|'灵宠_灵宠技能_灵石貔貅'|'灵宠_灵宠技能_青龙'|'灵宠_灵宠技能_鸩'|'皮肤_主角'|'皮肤_冰雪鲛女'|'皮肤_卡牌_主角'|'皮肤_卡牌_冰雪鲛女'|'皮肤_卡牌_土域灵狐_01'|'皮肤_卡牌_土域灵狐_02'|'皮肤_卡牌_土域灵狐_03'|'皮肤_土域灵狐_01'|'皮肤_土域灵狐_02'|'皮肤_土域灵狐_03'\r\n\r\ninterface IRefidConfig extends IRefBase\r\n{\r\ncacheName:string\r\nfileName:string\r\nflag:string\r\nid:IRefidConfig_id\r\nkey:IRefidConfig_key\r\nmax:number\r\nmin:number\r\n}\r\ninterface refGen{idConfig:IRefidConfig}\r\ntype IRefidConfig_id=1|10|2|3|4|5|6|7|8|9\r\ntype IRefidConfig_key='equip'|'gem'|'head'|'item'|'monster'|'playerAtt'|'skill'|'skin'|'spiritAnimal'|'task'\r\n\r\ninterface IRefitem extends IRefBase\r\n{\r\nicon:string\r\nname:IRefitem_name\r\nquality:IRefitem_quality\r\nresId:IRefitem_resId\r\ntype:IRefitem_type\r\nuseType:number\r\n\r\ndesc?:string\r\nkey?:IRefitem_key\r\n}\r\ninterface refGen{item:IRefitem}\r\ntype IRefitem_key='suijijinengjuanzhou'|'suijizhuangbeituzhi'|'tupodan'|'yuanlingguo'|'zhangjiesuijibaoshixiangzi'|'zhangjiesuijijingshixiangzi'|'zhangjiesuijilingchongkouliang'\r\ntype IRefitem_name='1元代金券'|'万剑诀卷轴'|'五色土'|'剑气斩卷轴'|'发冠升级图纸'|'天雷网卷轴'|'女娲石'|'宝石抽取券'|'寒冰突刺卷轴'|'寒冰箭卷轴'|'巨石突刺卷轴'|'御剑术卷轴'|'惊雷咒卷轴'|'戒指升级图纸'|'技能卷轴自选宝箱'|'改名卡'|'新手道书'|'旋风术卷轴'|'普通宝石箱'|'晶石掉落'|'武器升级图纸'|'毒气弹卷轴'|'泰山压顶卷轴'|'灵宠口粮'|'灵宠抽卡箱'|'灵宠抽取券'|'璀璨宝石箱'|'石狮子卷轴'|'突破丹'|'连环闪电卷轴'|'道袍升级图纸'|'闪电球卷轴'|'随机多彩宝石宝箱'|'随机宝石箱子'|'随机技能卷轴'|'随机稀有宝石'|'随机紫色宝石宝箱'|'随机红色宝石宝箱'|'随机蓝色宝石宝箱'|'随机装备图纸'|'随机金色宝石宝箱'|'靴子升级图纸'|'项链升级图纸'|'风暴术卷轴'\r\ntype IRefitem_quality=3|4|5|6|7\r\ntype IRefitem_resId=10001|10002|10003|10004|10005|10006|10007|10008|10009|10010|10011|10012|10013|10014|10015|10016|10017|10018|10019|10020|10021|10022|10023|10024|10101|10102|10103|10104|10105|10106|10107|10108|10109|10110|10111|10112|10113|10114|10115|10116|10117|10118|10119|10120|10121|10122|10123|10999\r\ntype IRefitem_type=1|11|3|5|8\r\n\r\ninterface IRefitemBox extends IRefBase\r\n{\r\nboxResId:number\r\nnumber:number\r\nnumber2:number\r\ntype:IRefitemBox_type\r\n}\r\ninterface refGen{itemBox:IRefitemBox}\r\ntype IRefitemBox_type=1|2\r\n\r\ninterface IRefitemBoxPro extends IRefBase\r\n{\r\nboxResId:number\r\nmaxNumber:number\r\nminNumber:number\r\nprobability:number\r\nresId:IRefitemBoxPro_resId\r\nshowPer:string\r\n}\r\ninterface refGen{itemBoxPro:IRefitemBoxPro}\r\ntype IRefitemBoxPro_resId=1|10001|10002|10003|10004|10005|10006|10101|10102|10103|10104|10105|10106|10107|10108|10109|10110|10111|10112|10113|10114|10115|2|3|4|5|6|7\r\n\r\ninterface IRefitemType extends IRefBase\r\n{\r\nkey:IRefitemType_key\r\nname:IRefitemType_name\r\ntype:IRefitemType_type\r\n}\r\ninterface refGen{itemType:IRefitemType}\r\ntype IRefitemType_key='baoshi'|'baoxiang'|'jinengshu'|'jueseziyuan'|'kehuduanzhanshileixing'|'lingshou'|'pifu'|'putong'|'touxiang'|'zhuangbei'|'zhuangbeituzhi'\r\ntype IRefitemType_name='头像'|'宝石'|'宝箱'|'客户端展示类型'|'技能书'|'普通物品'|'灵兽'|'皮肤'|'装备'|'装备图纸'|'角色资源'\r\ntype IRefitemType_type=1|10|11|2|3|4|5|6|7|8|9\r\n\r\ninterface IReflevel extends IRefBase\r\n{\r\nexp:number\r\nlevel:IReflevel_level\r\n}\r\ninterface refGen{level:IReflevel}\r\ntype IReflevel_level=1|10|100|11|12|13|14|15|16|17|18|19|2|20|21|22|23|24|25|26|27|28|29|3|30|31|32|33|34|35|36|37|38|39|4|40|41|42|43|44|45|46|47|48|49|5|50|51|52|53|54|55|56|57|58|59|6|60|61|62|63|64|65|66|67|68|69|7|70|71|72|73|74|75|76|77|78|79|8|80|81|82|83|84|85|86|87|88|89|9|90|91|92|93|94|95|96|97|98|99\r\n\r\ninterface IReflimitTimeCard extends IRefBase\r\n{\r\nbuyCostCoinNum:number\r\ncontinueDays:number\r\nname:IReflimitTimeCard_name\r\nresId:IReflimitTimeCard_resId\r\ntype:IReflimitTimeCard_type\r\n}\r\ninterface refGen{limitTimeCard:IReflimitTimeCard}\r\ntype IReflimitTimeCard_name='至尊月卡'|'豪华月卡'\r\ntype IReflimitTimeCard_resId=1|2\r\ntype IReflimitTimeCard_type=1|2\r\n\r\ninterface IReflockMonsterLevel extends IRefBase\r\n{\r\nboxRewards:any[]\r\nfirstPassRewards:any[]\r\nlevel:IReflockMonsterLevel_level\r\n}\r\ninterface refGen{lockMonsterLevel:IReflockMonsterLevel}\r\ntype IReflockMonsterLevel_level=1|10|100|101|102|103|104|105|106|107|108|109|11|110|111|112|113|114|115|116|117|118|119|12|120|121|122|123|124|125|126|127|128|129|13|130|131|132|133|134|135|136|137|138|139|14|140|141|142|143|144|145|146|147|148|149|15|150|151|152|153|154|155|156|157|158|159|16|160|161|162|163|164|165|166|167|168|169|17|170|171|172|173|174|175|176|177|178|179|18|180|181|182|183|184|185|186|187|188|189|19|190|191|192|193|194|195|196|197|198|199|2|20|200|201|202|203|204|205|206|207|208|209|21|210|211|212|213|214|215|216|217|218|219|22|220|221|222|223|224|225|226|227|228|229|23|230|231|232|233|234|235|236|237|238|239|24|240|241|242|243|244|245|246|247|248|249|25|250|251|252|253|254|255|256|257|258|259|26|260|261|262|263|264|265|266|267|268|269|27|270|271|272|273|274|275|276|277|278|279|28|280|281|282|283|284|285|286|287|288|289|29|290|291|292|293|294|295|296|297|298|299|3|30|300|31|32|33|34|35|36|37|38|39|4|40|41|42|43|44|45|46|47|48|49|5|50|51|52|53|54|55|56|57|58|59|6|60|61|62|63|64|65|66|67|68|69|7|70|71|72|73|74|75|76|77|78|79|8|80|81|82|83|84|85|86|87|88|89|9|90|91|92|93|94|95|96|97|98|99\r\n\r\ninterface IReflockMonsterRefreshCost extends IRefBase\r\n{\r\ncost:number\r\nrefreshNum:number\r\n}\r\ninterface refGen{lockMonsterRefreshCost:IReflockMonsterRefreshCost}\r\n\r\ninterface IRefmapTerrian extends IRefBase\r\n{\r\nid:IRefmapTerrian_id\r\nlevel:IRefmapTerrian_level\r\nmodel:string\r\n}\r\ninterface refGen{mapTerrian:IRefmapTerrian}\r\ntype IRefmapTerrian_id=1|10|100|101|102|103|104|105|106|107|108|109|11|110|111|112|113|114|115|116|117|118|119|12|120|121|122|123|124|125|126|127|128|129|13|130|131|132|133|134|135|136|137|138|139|14|140|141|142|143|144|145|146|147|148|149|15|150|151|152|153|154|155|156|157|158|159|16|160|161|162|163|164|165|166|167|168|169|17|170|171|172|173|174|175|176|177|178|179|18|180|181|182|183|184|185|186|187|188|189|19|190|191|192|193|194|195|196|197|198|199|2|20|200|201|202|203|204|205|206|207|208|209|21|210|211|212|213|214|215|216|217|218|219|22|220|221|222|223|224|225|226|227|228|229|23|230|231|232|233|234|235|236|237|238|239|24|240|241|242|243|244|245|246|247|248|249|25|250|251|252|253|254|255|256|257|258|259|26|260|261|262|263|264|265|266|267|268|269|27|270|271|272|273|274|275|276|277|278|279|28|280|281|282|283|284|285|286|287|288|289|29|290|291|292|293|294|295|296|297|298|299|3|30|300|301|302|303|304|305|306|307|308|309|31|310|311|312|313|314|315|316|317|318|319|32|320|321|322|323|324|325|326|327|328|329|33|330|331|332|333|334|335|336|337|338|339|34|340|341|342|343|344|345|346|347|348|349|35|350|351|352|353|354|355|356|357|358|359|36|360|361|362|363|364|365|366|367|368|369|37|370|371|372|373|374|375|376|377|378|379|38|380|381|382|383|39|4|40|41|42|43|44|45|46|47|48|49|5|50|51|52|53|54|55|56|57|58|59|6|60|61|62|63|64|65|66|67|68|69|7|70|71|72|73|74|75|76|77|78|79|8|80|81|82|83|84|85|86|87|88|89|9|90|91|92|93|94|95|96|97|98|99\r\ntype IRefmapTerrian_level=1|10|100|101|102|103|104|105|106|107|108|109|11|110|111|112|113|114|115|116|117|118|119|12|120|121|122|123|124|125|126|127|128|129|13|130|131|132|133|134|135|136|137|138|139|14|140|141|142|143|144|145|146|147|148|149|15|150|151|152|153|154|155|156|157|158|159|16|160|161|162|163|164|165|166|167|168|169|17|170|171|172|173|174|175|176|177|178|179|18|180|181|182|183|184|185|186|187|188|189|19|190|191|192|193|194|195|196|197|198|199|2|20|200|201|202|203|204|205|206|207|208|209|21|210|211|212|213|214|215|216|217|218|219|22|220|221|222|223|224|225|226|227|228|229|23|230|231|232|233|234|235|236|237|238|239|24|240|241|242|243|244|245|246|247|248|249|25|250|251|252|253|254|255|256|257|258|259|26|260|261|262|263|264|265|266|267|268|269|27|270|271|272|273|274|275|276|277|278|279|28|280|281|282|283|284|285|286|287|288|289|29|290|291|292|293|294|295|296|297|298|299|3|30|300|301|302|303|304|305|306|307|308|309|31|310|311|312|313|314|315|316|317|318|319|32|320|321|322|323|324|325|326|327|328|329|33|330|331|332|333|334|335|336|337|338|339|34|340|341|342|343|344|345|346|347|348|349|35|350|351|352|353|354|355|356|357|358|359|36|360|361|362|363|364|365|366|367|368|369|37|370|371|372|373|374|375|376|377|378|379|38|380|381|382|383|39|4|40|41|42|43|44|45|46|47|48|49|5|50|51|52|53|54|55|56|57|58|59|6|60|61|62|63|64|65|66|67|68|69|7|70|71|72|73|74|75|76|77|78|79|8|80|81|82|83|84|85|86|87|88|89|9|90|91|92|93|94|95|96|97|98|99\r\n\r\ninterface IRefmL extends IRefBase\r\n{\r\nid:IRefmL_id\r\nkey:IRefmL_key\r\nname:IRefmL_name\r\n}\r\ninterface refGen{mL:IRefmL}\r\ntype IRefmL_id=1|10|100|101|102|103|104|105|106|107|108|109|11|110|111|112|113|114|115|116|117|118|119|12|120|121|122|123|124|125|126|127|128|129|13|130|131|132|133|134|135|136|137|138|139|14|140|141|142|143|144|145|146|147|148|149|15|150|151|152|153|154|155|156|157|158|159|16|160|161|162|163|164|165|166|167|168|169|17|170|171|172|173|174|175|176|177|178|179|18|180|181|182|183|184|185|186|187|188|189|19|190|191|192|193|194|195|196|197|198|199|2|20|200|201|202|203|204|205|206|207|208|209|21|210|211|212|213|214|215|216|217|218|219|22|220|221|222|223|224|225|226|227|228|229|23|230|231|232|233|234|235|236|237|238|239|24|240|241|242|243|244|245|246|247|248|249|25|250|251|252|253|254|255|256|257|258|259|26|260|261|262|263|264|265|266|267|268|269|27|270|271|272|273|28|29|3|30|31|32|33|34|35|36|37|38|39|4|40|41|42|43|44|45|46|47|48|49|5|50|51|52|53|54|55|56|57|58|59|6|60|61|62|63|64|65|66|67|68|69|7|70|71|72|73|74|75|76|77|78|79|8|80|81|82|83|84|85|86|87|88|89|9|90|91|92|93|94|95|96|97|98|99\r\ntype IRefmL_key='ACTIVITY_REWARD_ALREADY_TAKE'|'ACTIVITY_REWARD_NOT_CONDITION'|'AD_IN_CD'|'AD_LIMIT'|'ALREADY_FRIEND'|'APPRENTICE_NOT_OPEN'|'ActivityTab_1'|'BATTLE_REWARD_NOT_TAKE_ALL'|'BATTLE_SWEEP_ERROR'|'BATTLE_SWEEP_TIMES_ENOUGH'|'BATTLE_TOKEN_ALREADY_OPEN'|'BATTLE_TOKEN_ALREADY_REWARDED'|'BATTLE_TOKEN_LEVEL_NOT_CONDITION'|'BATTLE_TOKEN_NOT_MAX_END_COST'|'BATTLE_TOKEN_NOT_MAX_LEVEL'|'BATTLE_TOKEN_NOT_OPEN'|'BATTLE_TOKEN_NOT_REWARD'|'BE_BLACK_FRIEND'|'BLACK_TARGET_FRIEND'|'BOSS_NOT_START'|'BTTLEFIELD'|'BUY_TIMES_LIMIT'|'CANNOT_APPLY_SELF_TO_FRIEND'|'CANT_SEARCH_SELF'|'CARD_ALREADY_BUY'|'CHAPTER_NOT_UNLOCK'|'COMMODITY_NONEXISTENCE'|'CONTIN_SENSITIVE_WORD'|'CTIVITY_NOT_ENBLED'|'Client_ActEnd'|'Client_Discount'|'Client_GemName'|'Client_GetPreview'|'Client_Geted'|'Client_InRankCondition'|'Client_LimitSale'|'Client_NeedMoney'|'Client_NotInRank'|'Client_RankWait'|'Client_RemainTime'|'Client_TaskFight'|'Client_TaskProgress'|'Client_TaskUnFight'|'Client_UnOpen'|'CtrlGameLogin_1'|'ENVIRONMENT_ERROR'|'EQUIP_MAX_LEVEL'|'EQUIP_NOT_EXIST'|'FIRST_RECHARGE_NOT_OPEN'|'FRIEND_NOT_EXIST'|'Func_1'|'Func_2'|'GAME_ERROR'|'GEM_ALREADY_UP'|'GEM_COMPOSE'|'GEM_EQUIP'|'GEM_IS_LOCK'|'GEM_NOT_EXIST'|'GEM_NOT_LOCK'|'GEM_ORDER_LIMIT'|'GEM_QUALITY_NOT_REFINE'|'GEM_UP_IN_SAME_ATTR'|'GEM_UP_IN_SAME_EQUIP'|'GIFT_CODE_ALREADY_USED'|'GIFT_CODE_EXPIRED'|'GIFT_CODE_NOT_EXISTS'|'GObjChapterSelect_1'|'GObjChapterSelect_2'|'GameStateRunning_1'|'GameStateRunning_2'|'GameStateRunning_3'|'GameStateRunning_4'|'GameStateRunning_5'|'GameStateRunning_6'|'ITEM_NOT_ENOUGH'|'LOGIN_UTHENTICTION_FILURE'|'LREDY_HVE_SKIN_CNNOT_BE_PURCHSED'|'LayerAccountUI_1'|'LayerAccountUI_2'|'LayerBattleToken'|'LayerChangeHead_1'|'LayerChangeHead_2'|'LayerChapterStageDetail_1'|'LayerDisconnect_1'|'LayerDisconnect_2'|'LayerDisconnect_3'|'LayerEquipPageNameEdit_1'|'LayerFirstRecharge_1'|'LayerForgingLvUp_1'|'LayerGemInfo_1'|'LayerGemInfo_2'|'LayerGemInfo_3'|'LayerGemInfo_4'|'LayerGemRefine_1'|'LayerGemRefine_2'|'LayerGuanKaGift_1'|'LayerLilian_1'|'LayerLimitActivity_1'|'LayerLimitActivity_2'|'LayerLimitActivity_3'|'LayerLimitActivity_4'|'LayerLimitActivity_5'|'LayerLimitActivity_6'|'LayerLimitActivity_7'|'LayerLogin_1'|'LayerPetSelectBox_1'|'LayerPetSelectBox_2'|'LayerPetSelectBox_3'|'LayerPetsAttr_1'|'LayerPetsAttr_2'|'LayerPetsAttr_3'|'LayerPetsInfo_1'|'LayerPetsInfo_2'|'LayerPetsReset_1'|'LayerPetsSelectOut_1'|'LayerPetsSelectStepUp_1'|'LayerRank_1'|'LayerRank_2'|'LayerRank_3'|'LayerRename_1'|'LayerRename_2'|'LayerRename_3'|'LayerRename_4'|'LayerRename_5'|'LayerSetting_1'|'LayerShopBox_1'|'LayerShopBox_2'|'LayerShopBox_3'|'LayerShowTip_1'|'LayerShowTip_2'|'LayerShowTip_3'|'LayerSkinInfo_1'|'LayerSkinInfo_2'|'LayerUseVoucher_1'|'LayerWaiting_1'|'MAIL_NOT_REWARD_CAN_TAKE'|'MAX_BLACk_FRIEND'|'MAX_LEVEL'|'MAX_NUM'|'MAX_SELECT_TIMES'|'MINING_PROCESS_NOD_CONDITION'|'ModePets_1'|'ModePets_2'|'ModePets_3'|'ModePets_4'|'ModelChanllenge_1'|'ModelEquip_1'|'ModelEquip_2'|'ModelGem_1'|'ModelGem_2'|'ModelGem_3'|'ModelGem_4'|'ModelGem_5'|'ModelGoods_1'|'ModelGuild_1'|'ModelGuild_2'|'ModelGuild_3'|'ModelMonthCard_1'|'ModelSkill_1'|'ModelStore_1'|'ModelStore_2'|'ModelStore_3'|'ModelSystem_1'|'ModelSystem_2'|'ModelSystem_3'|'ModelSystem_4'|'ModelWorkShop_1'|'NAME_TOO_LONG'|'NO_FRIEND_CAN_SEND_END'|'NowLogin'|'PLESE_CLER_THE_FIRST_LEVEL_FIRST'|'REFRESH_TO_FAST'|'REMOVE_FRIEND_FAIL'|'REQUEST_SUCCESS'|'REQUEST_TIMEOUT'|'REWARD_NOT_EXIST'|'ROLE_END_BUY_LIMIT'|'ROLE_END_NOT_ENOUGH'|'ROLE_FUBEN_ENTER_TODAY_MAX'|'ROLE_ITEM_NOT_ENOUGH'|'ROLE_LEVEL_NOT_MATCH'|'ROLE_NAME_CONTAIN_SENSITIVE_WORD'|'ROLE_NAME_CONTAIN_SPECIAL_SYMBOL'|'ROLE_NAME_IS_EMPTY'|'ROLE_NAME_IS_EXIST'|'ROLE_NAME_TOO_LENGTH'|'ROLE_NOT_EXIST'|'ROLE_TICKET_NOT_ENOUGH'|'Refmonster_1'|'Refmonster_2'|'SELF_MAX_FRIEND'|'SERVER_ERROR'|'SEVEN_DAY_CLOSE'|'SHORTGE_OF_GOODS'|'SKILL_CANT_LEVEL'|'SKILL_IN_LOCK'|'SKIN_POSSESSION'|'SPIRIT_BESTS_DONT_EXIST'|'SPIRIT_BEST_HS_RECHED_LEVEL'|'Server'|'Start_1'|'Start_2'|'Start_3'|'SubTabSpecialGiftBase_1'|'TARGET_ALREADY_BLACK'|'TARGET_MAX_FRIEND'|'TARGET_NOT_APPLY'|'TARGET_NOT_BE_BLACK'|'TARGET_TODAY_ALREADY_SEND'|'TARGET_TODAY_ALREADY_TAKE'|'TASK_NOT_EXIST'|'TASK_NOT_FINISH_OR_REWARDED'|'TF_LayerBattleChooseSkill_1'|'TF_LayerBattleChooseSkill_2'|'TF_LayerBattleChooseSkill_3'|'TF_LayerBattleChooseSkill_4'|'TF_LayerBattleFail_1'|'TF_LayerBattleFail_2'|'TF_LayerBattleReward_1'|'TF_LayerBattleReward_2'|'TF_LayerBattle_1'|'THE_CHEST_DOESNT_EXIST'|'THE_CHEST_IS_NOT_COOLED'|'THE_CURRENT_PROPERTIES_MYSTERY_HS_ENDED'|'THE_LEDERBORD_TYPE_DOES_NOT_EXIST'|'THE_MOUNT_OF_BODY_CONSUMED_IS_INCORRECT'|'THE_MOUNT_OF_CONSUMPTION_IS_NOT_CORRECT'|'THE_PRCTITIONER'|'THE_QULITY_OF_SPIRIT_BEST_SELECTED_IS_NOT_CORRECT'|'THE_SELECTED_SPIRIT_BEST_DOES_NOT_EXIST'|'THE_SPIRIT_BEST_HS_ENTERED_THE_FRY'|'THE_SPIRIT_BEST_HS_RECHED_ITS_MXIMUM_CLSS'|'THE_TRESURE_CHEST_HS_BEEN_COLLECTED'|'THIS_BTTLE_TYPE_CNNOT_CONTINUE_TO_THE_NEXT_BTTLE'|'THIS_BTTLE_TYPE_DOES_NOT_SUPPORT_REFRESH_SKILLS'|'TIME_NOT_OPEN'|'TODAY_ALREADY_BUY_MINING'|'TODAY_ALREADY_LIKE_ROLE'|'TODAY_NOT_RECEIVE_TARGET_END'|'TODAY_SEND_END_MAX'|'TODAY_TAKE_END_MAX'|'TTSDKMiniGame_1'|'TabChallenge_1'|'TabForging_1'|'TabForging_2'|'TabGem_1'|'TabGem_2'|'TabGiftMonth_1'|'TabSkill_1'|'UNSELECTED_ITEM'|'VoRole_1'|'WALL_LOCK'|'WALL_NOT_OPEN'|'WE_HVE_RECHED_THE_MXIMUM_LEVEL'|'WORK_SHOP_NOT_OPEN'|'WRONG_SELECTION_QUNTITY'|'YouaiSDK_1'|'YouaiSDK_2'|'battle_level_error'|'battle_level_not_exist'|'battle_level_type_error'|'battle_times_error'|'card_only_select_one'|'card_select_error'|'guanka_gift_can_not_select'|'guanka_gift_not_active'|'guanka_gift_not_select'|'guanka_gift_out_time'|'msg_CmdRoleOfflineBroMsg_type_1'|'msg_CmdRoleOfflineBroMsg_type_2'|'role_data_error'|'time_not_receive'|'wall_hp_higher'\r\ntype IRefmL_name='%s不足'|'({0}/{1})'|'(当前通关{0}-{1})'|'(未通关)'|'BOSS未出现'|'Lv.{0}'|'{0}*{1}'|'{0}[color=#ad51d1](精英)[/color]'|'{0}[color=#d74d46](首领)[/color]'|'{0}·{1}'|'{0}不足'|'{0}分钟'|'{0}层'|'{0}折'|'七日活动未开放'|'下阵成功'|'不满足领取条件'|'不能搜索自己'|'不能添加自己为好友'|'不能选取该宝石'|'今日已给该玩家点赞，明日再来吧'|'今日已赠送过该好友体力'|'今日已领取过该好友的体力'|'今日未收到该好友赠送的体力'|'今日赠送体力已达上限'|'今日领取体力已达上限'|'代金券不足'|'任务不存在'|'任务未完成或已领取'|'体力未达消耗条件'|'体力购买已达今日上限'|'你的好友已经满员了'|'使用成功'|'信件奖励已领取'|'修改成功'|'修行者:'|'免费扫荡次数已用完，是否观看广告增加1次扫荡机会？(每日最多加{0}/{1}次)'|'兑换码不能为空'|'全服'|'关卡不存在'|'关卡未解锁'|'关卡礼包未激活'|'关卡类型错误'|'再抽一次'|'再抽十次'|'出战成功'|'删除好友失败'|'刷新太频繁，稍后再试'|'剩余时间：{0}'|'剩余次数不足'|'功能未开启'|'加载中...'|'加速成功'|'包含敏感字'|'升级'|'升级成功'|'升阶成功'|'卡牌不在随机列表'|'即将进入客服会话'|'卸下宝石成功'|'历练今日已经购买过'|'历练进度不满足条件'|'发现新版本，点击跳转更新'|'取消'|'只能穿戴在同类型装备上'|'只能选一个'|'合成成功'|'同时只能有一个灵宠出战，是否替换{0}成为出战灵宠'|'商品不存在'|'城墙未开启'|'复制成功'|'大侠已经开通该档位了，不能重复开通哦~'|'奖励已领取'|'好友'|'好友不存在'|'字符长度过长'|'宝石孔已满'|'宝石已装备'|'宝石已锁定'|'宝石替换成功'|'宝石未锁定'|'宝石筛选'|'宝石镶嵌成功'|'宝箱不存在'|'宝箱已领取'|'宝箱未冷却'|'对方好友已达上限'|'已学技能'|'已穿戴宝石一旦洗炼就会在所有页签中卸下，是否继续洗炼？'|'已经有皮肤，不能购买'|'已经翻倍'|'已装备同类型宝石'|'已达最大关卡'|'已达最高等级'|'已镶嵌同类型更好品质的宝石'|'已镶嵌相同的宝石'|'已领取'|'广告时间CD'|'广告次数已满'|'广州'|'开启'|'当前关卡不能扫荡'|'当前属性秘境已结束'|'当前暂无装备可升级'|'您已拉黑对方，请先从黑名单移除'|'您已被对方拉黑'|'您的体力不足'|'您的账号已经在其他地方登陆，如非本人操作，请注意账号安全。'|'您的账号已经暂时禁止登陆，请及时联系客服人员。'|'您的账号已被封，请联系客服'|'您的门票不足'|'战令奖励已领取'|'战令奖励未全部领取'|'战令未开启'|'战令未达最大等级'|'战令等级不满足条件'|'战场'|'战场关卡数对不上'|'所有装备升级至最高'|'拥有：'|'排行榜类型不存在'|'提示'|'改名所需晶石不足'|'无'|'时间未到'|'时间没到'|'昵称不能为空哦~'|'暂无奖励可领取'|'暂无好友可赠送体力'|'暂无宝石可合成'|'暂未上榜'|'暂未开放'|'更新倒计时:{0}'|'更新完成'|'最多选择2个技能'|'最多选择{0}个'|'最大上阵数量'|'最近登录'|'服'|'服务器异常'|'服务器正在维护中'|'服务器维护中，请稍等再尝试登陆!'|'未上榜'|'未保存的属性在关闭后会默认放弃，是否继续关闭？'|'未到奖励领取时间'|'未培养灵宠无法重置'|'未选择物品'|'本关怪物'|'本场战斗学习技能已满'|'本服'|'本次改名需要花费{0}晶石，是否确认修改？'|'检查更新{0}%...'|'检测到网络已断开，重启!'|'检测到网络已断开，重新连接!'|'欢度元旦'|'正在检查更新....'|'每天只能扫荡%s次'|'每日任务'|'永久'|'没有奖励可领取'|'没有宝石可合成'|'没有该宝石'|'波数%s不存在'|'洗炼宝石成功'|'活动奖励不满足领取条件'|'活动奖励已领取'|'活动已结束'|'活动期间内，任务每天重置，完成任务可领取奖励'|'活动期间内，礼包不重置，每个礼包指定限购'|'活动期间内，礼包每天重置，每个礼包指定限购'|'活动未开启'|'消耗同系数量不对'|'消耗本体数量不对'|'灵宠不存在'|'灵宠已上阵'|'灵宠已满级'|'灵宠已达最大阶级'|'灵宠攻击+{0}'|'灵宠未开启'|'物品不足'|'玩家不存在'|'玩家名为空'|'玩家名已存在'|'玩家名称包含敏感字'|'玩家名称包含特殊字符'|'玩家名过长'|'玩家已经是好友了'|'玩家物品不足'|'玩家等级不满足条件'|'环境错误!'|'登录验证失败'|'皮肤未拥有'|'目标不存在黑名单中'|'目标已被拉黑'|'目标并未请求添加好友'|'确定'|'确认'|'礼包今日不可购买'|'礼包码不存在'|'礼包码已兑换'|'礼包码已过期'|'立即观看'|'等级不够，不能解锁'|'系统暂未开启'|'维护完成!'|'网络异常，请稍候！'|'网络错误，请重试'|'自定义头像'|'节日特惠'|'血量不能超过100%'|'装备不存在'|'角色攻击+{0}'|'角色等级{0}级开放'|'角色等级不满足'|'解锁成功'|'该副本今日挑战次数已用完'|'该卡已购买'|'该品质宝石不能洗炼'|'该宝石已穿戴'|'该宝石已镶嵌'|'该战令已购买'|'该战斗类型不支持刷新技能'|'该战斗类型不能继续下一场战斗'|'该技能不能升级'|'该技能未解锁'|'该月卡已激活'|'该玩法努力开发中~~'|'该礼包已过期'|'请仔细阅读并同意用户协议和隐私协议'|'请先选取宝石'|'请先通关第一层'|'请填写新的昵称~'|'请大侠次日再来~'|'请求成功'|'请求超时'|'请选择一个奖励'|'请选择上阵的灵宠'|'请选择两张技能卡牌'|'请选择你想要的宝石'|'请选择奖励'|'请选择要替换的宝石'|'请选择要消耗的灵宠'|'请通关更高关卡'|'账号不存在，请重新输入'|'账号数据错误'|'账号注册成功!'|'账号登录'|'购买成功'|'超过购买次数上限'|'还没获得该头像哦~'|'进度条满之后才可购买'|'选取的灵宠不存在'|'选取的灵宠品质不正确'|'选择数量不对'|'通关{0}上榜'|'通关后可解锁加速功能'|'错误'|'阵法护盾+{0}'|'限时礼包'|'限购{0}/{1}'|'需通关第{0}章第{1}关解锁'|'领取成功'|'黑名单数量已达最大'|'￥{0}'\r\n\r\ninterface IRefmodelRef extends IRefBase\r\n{\r\nkey:IRefmodelRef_key\r\npath:string\r\n}\r\ninterface refGen{modelRef:IRefmodelRef}\r\ntype IRefmodelRef_key='场景_战斗地图01'|'场景_战斗地图02'|'场景_战斗地图03'|'场景_战斗地图05'|'场景_战斗地图06'|'场景_战斗地图07'|'场景_战斗地图08'|'场景_战斗地图10'|'场景_战斗地图11'|'场景_战斗地图12'|'场景_战斗地图13'|'场景_战斗地图_泥地_前期'|'场景_战斗地图_绿草地_前期'|'塔防_主角_初始_主角'|'塔防_主角皮肤_灵狐_橙'|'塔防_主角皮肤_灵狐_红'|'塔防_主角皮肤_灵狐_绿'|'塔防_主角皮肤_鲛女'|'塔防_召唤特效_召唤出现'|'塔防_场景_城墙1级'|'塔防_场景_城墙1级_打开'|'塔防_场景_城墙2级'|'塔防_场景_城墙2级_打开'|'塔防_场景_城墙3级'|'塔防_场景_城墙3级_打开'|'塔防_场景_城墙4级'|'塔防_场景_城墙4级_打开'|'塔防_场景_城墙破碎'|'塔防_城墙_回血_大'|'塔防_城墙_血条_红'|'塔防_城墙_血条_绿'|'塔防_城墙_血条_黄'|'塔防_干饭妖分裂'|'塔防_干饭妖分裂BOSS'|'塔防_怪物_亡魂鬼士'|'塔防_怪物_冰晶雪女'|'塔防_怪物_冰鬼'|'塔防_怪物_地灵蛛'|'塔防_怪物_小怪_冰鬼'|'塔防_怪物_小怪_小火龙'|'塔防_怪物_小怪_巨针蜂'|'塔防_怪物_小怪_彩粉蝶'|'塔防_怪物_小怪_护盾小鬼'|'塔防_怪物_小怪_拳击鼠'|'塔防_怪物_小怪_木棉球'|'塔防_怪物_小怪_熊猫滚滚'|'塔防_怪物_小怪_红叶蜻蜓'|'塔防_怪物_小怪_草栗球'|'塔防_怪物_小怪_菊草叶'|'塔防_怪物_小怪_蚊香蛙'|'塔防_怪物_小怪_超级战鬼'|'塔防_怪物_小怪_跳跳猫'|'塔防_怪物_小怪_迷你兔'|'塔防_怪物_小怪_长耳兔'|'塔防_怪物_小怪_青灵王子'|'塔防_怪物_干饭妖'|'塔防_怪物_幽冥鬼火'|'塔防_怪物_搬山巨猿'|'塔防_怪物_暗影白骨'|'塔防_怪物_木甲鬼士'|'塔防_怪物_枯骨鸟'|'塔防_怪物_灯精'|'塔防_怪物_狐狸'|'塔防_怪物_白骨'|'塔防_怪物_白骨女巫'|'塔防_怪物_白骨战士'|'塔防_怪物_白骨邪神'|'塔防_怪物_盾甲白骨'|'塔防_怪物_石剑魔'|'塔防_怪物_米糕精'|'塔防_怪物_红炎灯鬼'|'塔防_怪物_蛟龙'|'塔防_怪物_蝎子'|'塔防_怪物_铁石猪'|'塔防_怪物_镇墓玄龟'|'塔防_怪物_青木精'|'塔防_怪物_青藤侍女'|'塔防_怪物_青藤鬼后'|'塔防_怪物_青蛇'|'塔防_怪物_风蝇君王'|'塔防_怪物_风蝇虫'|'塔防_怪物_食灵'|'塔防_怪物_首领_功夫熊猫'|'塔防_怪物_首领_喷火龙'|'塔防_怪物_首领_木棉妖精'|'塔防_怪物_首领_格斗鼠王'|'塔防_怪物_首领_武士忍蛙'|'塔防_怪物_首领_深渊魔王'|'塔防_怪物_首领_粉蝶领主'|'塔防_怪物_首领_萝卜战兔'|'塔防_怪物_首领_超级冰鬼王'|'塔防_怪物_鲛鱼精'|'塔防_怪物_鸟精'|'塔防_怪物子弹_冰晶雪女_子弹'|'塔防_怪物子弹_枯骨鸟_子弹'|'塔防_怪物子弹_白骨巫女_子弹'|'塔防_怪物子弹_蛟鱼精_子弹'|'塔防_怪物子弹_蛟龙_子弹'|'塔防_技能_万剑诀'|'塔防_技能_公用_单体冰冻'|'塔防_技能_公用_受击_持续燃烧'|'塔防_技能_公用_回血'|'塔防_技能_公用_毒爆'|'塔防_技能_公用_爆炸'|'塔防_技能_公用_眩晕'|'塔防_技能_公用_麻痹'|'塔防_技能_剑林'|'塔防_技能_剑气斩_加强'|'塔防_技能_剑气斩_普通'|'塔防_技能_千羽云鹤_飞剑'|'塔防_技能_天帝之拳_普通_坑'|'塔防_技能_天帝之拳_普通_石头'|'塔防_技能_天帝之拳_毒拳_坑'|'塔防_技能_天帝之拳_毒拳_石头'|'塔防_技能_天残脚_普通_坑'|'塔防_技能_天残脚_普通_石头'|'塔防_技能_天雷网'|'塔防_技能_天雷网_新'|'塔防_技能_寒冰突刺_范围冰冻'|'塔防_技能_寒冰突刺_范围冰冻_出现'|'塔防_技能_寒冰突刺_范围冰冻_循环'|'塔防_技能_寒冰突刺_范围冰冻_消失'|'塔防_技能_寒冰箭_三菱冰箭'|'塔防_技能_寒冰箭_冰冻'|'塔防_技能_寒冰箭_分裂子弹'|'塔防_技能_寒冰箭_受击'|'塔防_技能_寒冰箭_子弹'|'塔防_技能_巨石突刺'|'塔防_技能_御剑术_受击'|'塔防_技能_御剑术_子弹'|'塔防_技能_御剑术_火焰飞剑'|'塔防_技能_怪物护盾'|'塔防_技能_惊雷咒_天雷轰顶'|'塔防_技能_惊雷咒_小电球'|'塔防_技能_惊雷咒_爆炸'|'塔防_技能_惊雷咒_电磁场'|'塔防_技能_惊雷咒_雷电'|'塔防_技能_旋风术'|'塔防_技能_旋风术_闪电旋风'|'塔防_技能_毒云'|'塔防_技能_毒墙'|'塔防_技能_滚石_巨木_常态'|'塔防_技能_滚石_巨木_着火'|'塔防_技能_火墙'|'塔防_技能_火焰弹_受击_低级'|'塔防_技能_火焰弹_受击_高级'|'塔防_技能_火焰弹_子弹_低级'|'塔防_技能_火焰弹_子弹_高级'|'塔防_技能_火焰弹_溅射子弹'|'塔防_技能_荆棘'|'塔防_技能_落石术_坑'|'塔防_技能_落石术_普通_坑'|'塔防_技能_落石术_普通_石头'|'塔防_技能_落石术_燃烧陨石_坑'|'塔防_技能_落石术_燃烧陨石_石头'|'塔防_技能_连环闪电_电链'|'塔防_技能_金手掌_普通_坑'|'塔防_技能_金手掌_普通_石头'|'塔防_技能_长剑突刺'|'塔防_技能_闪电球'|'塔防_技能_闪电球_黑洞'|'塔防_技能_阵法陷阱'|'塔防_技能_风刃陷阱'|'塔防_技能_风暴术_普通'|'塔防_技能_风暴术_闪电风暴'|'塔防_投影_角色投影'|'塔防_死亡特效_死亡特效'|'塔防_灵宠_九尾灵狐'|'塔防_灵宠_千羽云鹤_重伤BUFF'|'塔防_灵宠_千羽灵鹤'|'塔防_灵宠_司雪'|'塔防_灵宠_貔貅'|'塔防_灵宠_青龙'|'塔防_灵宠_鸩'\r\n\r\ninterface IRefmonster extends IRefBase\r\n{\r\natk:number\r\natkNums:number\r\natkSpacing:number\r\nbehavior:number\r\nbuff:{ [key in string]: any }\r\ndesc1:string\r\nexp:number\r\nhp:number\r\nid:IRefmonster_id\r\nmodel:string\r\nname:IRefmonster_name\r\norder:number\r\nprototypeSize:number\r\nshadowSize:any[]\r\nshowSize:number\r\nskill:any[]\r\nspd:number\r\nspdX:number\r\ntype:IRefmonster_type\r\ntype2:number\r\n}\r\ninterface refGen{monster:IRefmonster}\r\ntype IRefmonster_id=60001|60002|60003|60004|60005|60006|60007|60008|60009|60010|60011|60012|60013|60014|60015|60016|60017|60018|61001|61002|61003|61004|61005|61006|61007|61008|61009|61010|61011|61012|61013|61014|61015|61016|61017|61018|62001|62002|62003|62004|62005|62006|62007|62008|62009|62010|63001|63002\r\ntype IRefmonster_name='亡魂鬼士'|'冰晶雪女'|'冰鬼'|'干饭妖'|'幽冥鬼火'|'搬山巨猿'|'暗影白骨'|'木甲鬼士'|'枯骨鸟'|'灯精'|'狐狸'|'白骨'|'白骨女巫'|'白骨战士'|'白骨邪神'|'盾甲白骨'|'石剑魔'|'米糕精'|'红炎灯鬼'|'蛟龙'|'蝎子'|'铁石猪'|'镇墓玄龟'|'青木精'|'青藤侍女'|'青藤鬼后'|'青蛇'|'风蝇君王'|'食灵'|'鲛鱼精'|'鸟精'\r\ntype IRefmonster_type=1|2\r\n\r\ninterface IRefmonsterSkillType extends IRefBase\r\n{\r\nattr:any[]\r\nid:IRefmonsterSkillType_id\r\nkey:IRefmonsterSkillType_key\r\nskillType:number\r\n}\r\ninterface refGen{monsterSkillType:IRefmonsterSkillType}\r\ntype IRefmonsterSkillType_id=1|2|3|4|5\r\ntype IRefmonsterSkillType_key='怪物技能_冰晶雪女_子弹'|'怪物技能_枯骨鸟_子弹'|'怪物技能_白骨巫女_子弹'|'怪物技能_蛟鱼精_子弹'|'怪物技能_蛟龙_子弹'\r\n\r\ninterface IRefmonthCardActPlan extends IRefBase\r\n{\r\ndesc:string\r\nlimitTimeCardResId:number\r\nplan:number\r\n}\r\ninterface refGen{monthCardActPlan:IRefmonthCardActPlan}\r\n\r\ninterface IRefopen extends IRefBase\r\n{\r\nbelond:number\r\ncondition:any[]\r\norder:number\r\nrefId:number\r\ntype:IRefopen_type\r\nurl:string\r\nvisibleCondition:any[]\r\n\r\nRDExpr?:string\r\nShowPreResId?:number\r\nicon?:string\r\nisShowOpen?:number\r\nname?:IRefopen_name\r\npreResId?:number\r\nresShowType?:any[]\r\ntlRDResId?:any[]\r\n}\r\ninterface refGen{open:IRefopen}\r\ntype IRefopen_name='七日狂欢'|'关卡礼包'|'分享'|'历练'|'周常礼包'|'宝石商店'|'怪物图鉴'|'战令'|'排名'|'晶石兑换'|'月度礼包'|'每日礼包'|'活动'|'灵宠'|'灵宠商店'|'灵玉直冲'|'炼器坊'|'礼包'|'签到豪礼'|'终身礼包'|'设置'|'豪华月卡'|'资源商店'|'超值礼包'|'连充礼包'|'邮件'|'铜钱商店'|'阵法'|'首充礼包'\r\ntype IRefopen_type=1|2|3\r\n\r\ninterface IRefpayChannel extends IRefBase\r\n{\r\ncoin:number\r\ncoin2:number\r\nid:IRefpayChannel_id\r\nkey:IRefpayChannel_key\r\nname:IRefpayChannel_name\r\npath:string\r\nrmb:number\r\n}\r\ninterface refGen{payChannel:IRefpayChannel}\r\ntype IRefpayChannel_id=1|2|3|4|5|6\r\ntype IRefpayChannel_key='goods_128'|'goods_30'|'goods_328'|'goods_6'|'goods_648'|'goods_68'\r\ntype IRefpayChannel_name='一堆灵玉'|'一盒灵玉'|'一袋灵玉'|'少量灵玉'|'很多灵玉'|'海量灵玉'\r\n\r\ninterface IRefprivilegeMonthCardInfo extends IRefBase\r\n{\r\nbuyReward:any[]\r\ndayReward:any[]\r\nprivilege:any[]\r\nresId:IRefprivilegeMonthCardInfo_resId\r\n}\r\ninterface refGen{privilegeMonthCardInfo:IRefprivilegeMonthCardInfo}\r\ntype IRefprivilegeMonthCardInfo_resId=2\r\n\r\ninterface IRefrechargeContinuityGift extends IRefBase\r\n{\r\ndays:number\r\nfinishRewards:any[]\r\nrewards:any[]\r\nround:number\r\n}\r\ninterface refGen{rechargeContinuityGift:IRefrechargeContinuityGift}\r\n\r\ninterface IRefrechargeGiftActPlan extends IRefBase\r\n{\r\nplan:number\r\nrechargeProcess:number\r\nrechargeReward:any[]\r\n}\r\ninterface refGen{rechargeGiftActPlan:IRefrechargeGiftActPlan}\r\n\r\ninterface IRefresourceShop extends IRefBase\r\n{\r\ncost:number\r\ndesc:string\r\nid:IRefresourceShop_id\r\nlimit:number\r\npriceType:number\r\nrewards:any[]\r\n}\r\ninterface refGen{resourceShop:IRefresourceShop}\r\ntype IRefresourceShop_id=1|2|3|4\r\n\r\ninterface IRefrewardClientPool extends IRefBase\r\n{\r\ncontentId:number\r\ndesc:string\r\nresId:IRefrewardClientPool_resId\r\ntlAddParam:any[]\r\ntlSetParam:any[]\r\n}\r\ninterface refGen{rewardClientPool:IRefrewardClientPool}\r\ntype IRefrewardClientPool_resId=10123\r\n\r\ninterface IRefrewardPool extends IRefBase\r\n{\r\ncontentId:number\r\nmaxNum:number\r\nminNum:number\r\nresId:IRefrewardPool_resId\r\nweight:number\r\n}\r\ninterface refGen{rewardPool:IRefrewardPool}\r\ntype IRefrewardPool_resId=10121\r\n\r\ninterface IRefroleProperty extends IRefBase\r\n{\r\ndescribe:string\r\nkey:IRefroleProperty_key\r\nname:IRefroleProperty_name\r\nquality:IRefroleProperty_quality\r\nresId:IRefroleProperty_resId\r\nshowType:number\r\ntype:IRefroleProperty_type\r\nunenoughTips:string\r\n\r\nicon?:string\r\nisbig?:number\r\n}\r\ninterface refGen{roleProperty:IRefroleProperty}\r\ntype IRefroleProperty_key='avatar'|'coin'|'end'|'exp'|'gem'|'gold'|'level'|'name'|'sex'|'washStone'\r\ntype IRefroleProperty_name='体力'|'头像'|'性别（1：男，2：女）'|'晶石'|'洗炼石'|'灵玉'|'角色名字'|'角色等级'|'角色经验'|'铜钱'\r\ntype IRefroleProperty_quality=1|2|3|4\r\ntype IRefroleProperty_resId=1|10|2|3|4|5|6|7|8|9\r\ntype IRefroleProperty_type=6\r\n\r\ninterface IRefsecretRealmAttr extends IRefBase\r\n{\r\nattr:any[]\r\nattrDesc:string\r\ntype:IRefsecretRealmAttr_type\r\n}\r\ninterface refGen{secretRealmAttr:IRefsecretRealmAttr}\r\ntype IRefsecretRealmAttr_type=1|2|3|4|5|6\r\n\r\ninterface IRefsecretRealmLevel extends IRefBase\r\n{\r\nfirstPassRewards:any[]\r\nlevel:IRefsecretRealmLevel_level\r\ntype:IRefsecretRealmLevel_type\r\n}\r\ninterface refGen{secretRealmLevel:IRefsecretRealmLevel}\r\ntype IRefsecretRealmLevel_level=1|10|100|11|12|13|14|15|16|17|18|19|2|20|21|22|23|24|25|26|27|28|29|3|30|31|32|33|34|35|36|37|38|39|4|40|41|42|43|44|45|46|47|48|49|5|50|51|52|53|54|55|56|57|58|59|6|60|61|62|63|64|65|66|67|68|69|7|70|71|72|73|74|75|76|77|78|79|8|80|81|82|83|84|85|86|87|88|89|9|90|91|92|93|94|95|96|97|98|99\r\ntype IRefsecretRealmLevel_type=1|2|3|4|5|6\r\n\r\ninterface IRefsecretRealmRefreshCost extends IRefBase\r\n{\r\ncost:number\r\nrefreshNum:number\r\n}\r\ninterface refGen{secretRealmRefreshCost:IRefsecretRealmRefreshCost}\r\n\r\ninterface IRefserverRank extends IRefBase\r\n{\r\nlimit:number\r\nmax:number\r\nmin:number\r\nreward:any[]\r\n}\r\ninterface refGen{serverRank:IRefserverRank}\r\n\r\ninterface IRefsevenDayPoint extends IRefBase\r\n{\r\npoint:number\r\nresId:IRefsevenDayPoint_resId\r\nreward:any[]\r\n}\r\ninterface refGen{sevenDayPoint:IRefsevenDayPoint}\r\ntype IRefsevenDayPoint_resId=1|2|3|4|5\r\n\r\ninterface IRefsevenDayShop extends IRefBase\r\n{\r\ncost:number\r\ndays:number\r\ndiscount:number\r\nid:IRefsevenDayShop_id\r\nlimit:number\r\nname:IRefsevenDayShop_name\r\noriginalPrice:number\r\npriceType:number\r\nrewards:any[]\r\ntype:IRefsevenDayShop_type\r\ntypeName:string\r\n}\r\ninterface refGen{sevenDayShop:IRefsevenDayShop}\r\ntype IRefsevenDayShop_id=11|12|13|21|22|23|31|32|33|41|42|43|51|52|53|61|62|63|71|72|73\r\ntype IRefsevenDayShop_name='体力半价礼包'|'宝石抽卡半价礼包'|'技能书半价礼包'|'洗炼石半价礼包'|'装备图纸半价礼包'|'铜币半价礼包'\r\ntype IRefsevenDayShop_type=3\r\n\r\ninterface IRefsevenDayTask extends IRefBase\r\n{\r\ndays:number\r\nname:IRefsevenDayTask_name\r\nparam:{ [key in string]: any }\r\nresId:IRefsevenDayTask_resId\r\nreward:any[]\r\ntargetType:number\r\ntype:IRefsevenDayTask_type\r\ntypeName:string\r\n}\r\ninterface refGen{sevenDayTask:IRefsevenDayTask}\r\ntype IRefsevenDayTask_name='击杀野怪数量11000只'|'击杀野怪数量15000只'|'击杀野怪数量20000只'|'击杀野怪数量30000只'|'击杀野怪数量3000只'|'击杀野怪数量7000只'|'合成10次宝石'|'合成20次宝石'|'合成2次宝石'|'合成30次宝石'|'合成50次宝石'|'合成5次宝石'|'技能总等级达到10级'|'技能总等级达到20级'|'技能总等级达到30级'|'技能总等级达到40级'|'技能总等级达到60级'|'技能总等级达到80级'|'消耗总体力达到1200点'|'消耗总体力达到1500点'|'消耗总体力达到2000点'|'消耗总体力达到300点'|'消耗总体力达到600点'|'消耗总体力达到900点'|'累积登录1天'|'累积登录2天'|'累积登录3天'|'累积登录4天'|'累积登录5天'|'累积登录6天'|'累积登录7天'|'累计充值1200元'|'累计充值198元'|'累计充值2000元'|'累计充值30元'|'累计充值328元'|'累计充值648元'|'累计充值98元'|'装备总等级达到10级'|'装备总等级达到120级'|'装备总等级达到20级'|'装备总等级达到40级'|'装备总等级达到60级'|'装备总等级达到90级'|'角色达到10级'|'角色达到12级'|'角色达到3级'|'角色达到5级'|'角色达到7级'|'角色达到9级'|'通关1-2关卡'|'通关10-4关卡'|'通关2-2关卡'|'通关3-3关卡'|'通关4-3关卡'|'通关5-3关卡'\r\ntype IRefsevenDayTask_resId=10001|10002|10003|10004|10005|10006|10007|10008|20001|20002|20003|20004|20005|20006|20007|20008|30001|30002|30003|30004|30005|30006|30007|30008|40001|40002|40003|40004|40005|40006|40007|40008|50001|50002|50003|50004|50005|50006|50007|50008|60001|60002|60003|60004|60005|60006|60007|60008|70001|70002|70003|70004|70005|70006|70007|70008\r\ntype IRefsevenDayTask_type=1|2\r\n\r\ninterface IRefsigninTask extends IRefBase\r\n{\r\nday:number\r\nname:IRefsigninTask_name\r\nparam:{ [key in string]: any }\r\nresId:IRefsigninTask_resId\r\nreward:any[]\r\nround:number\r\ntargetType:number\r\ntype:IRefsigninTask_type\r\ntypeName:string\r\n}\r\ninterface refGen{signinTask:IRefsigninTask}\r\ntype IRefsigninTask_name='宝箱14'|'宝箱21'|'宝箱28'|'宝箱7'|'累积登录10天'|'累积登录11天'|'累积登录12天'|'累积登录13天'|'累积登录14天'|'累积登录15天'|'累积登录16天'|'累积登录17天'|'累积登录18天'|'累积登录19天'|'累积登录1天'|'累积登录20天'|'累积登录21天'|'累积登录22天'|'累积登录23天'|'累积登录24天'|'累积登录25天'|'累积登录26天'|'累积登录27天'|'累积登录28天'|'累积登录2天'|'累积登录3天'|'累积登录4天'|'累积登录5天'|'累积登录6天'|'累积登录7天'|'累积登录8天'|'累积登录9天'\r\ntype IRefsigninTask_resId=1040001|1040002|1040003|1040004|1040101|1040102|1040103|1040104|1040105|1040106|1040107|1040108|1040109|1040110|1040111|1040112|1040113|1040114|1040115|1040116|1040117|1040118|1040119|1040120|1040121|1040122|1040123|1040124|1040125|1040126|1040127|1040128|1041001|1041002|1041003|1041004|1041101|1041102|1041103|1041104|1041105|1041106|1041107|1041108|1041109|1041110|1041111|1041112|1041113|1041114|1041115|1041116|1041117|1041118|1041119|1041120|1041121|1041122|1041123|1041124|1041125|1041126|1041127|1041128\r\ntype IRefsigninTask_type=0|1|2|3|4\r\n\r\ninterface IRefskillAdditionBuff extends IRefBase\r\n{\r\nbuffParam:{ [key in string]: any }\r\nbuffType:string\r\nid:IRefskillAdditionBuff_id\r\n}\r\ninterface refGen{skillAdditionBuff:IRefskillAdditionBuff}\r\ntype IRefskillAdditionBuff_id=1|10|11|12|2|3|4|5|6|7|8|9\r\n\r\ninterface IRefskillAttr extends IRefBase\r\n{\r\nid:IRefskillAttr_id\r\nkey:IRefskillAttr_key\r\nskillId:number\r\ntype:IRefskillAttr_type\r\nvalue:string\r\n\r\nparams?:{ [key in string]: any }\r\n}\r\ninterface refGen{skillAttr:IRefskillAttr}\r\ntype IRefskillAttr_id=100000|100001|100004|100005|100006|100007|100008|100009|100010|100011|100012|100013|100014|100015|100016|100017|100018|100019|100020|100021|100022|100023|100024|100025|100026|100027|100028|100029|100031|100032|100033|100034|100036|100038|100039|100040|100041|100042|100043|100044|100045|101000|101001|101002|101003|101004|101006|101007|101008|101009|101010|101011|101012|101013|101014|101015|101016|101017|101018|101019|101020|101021|101022|101024|101025|101028|101029|101030|101031|101032|101034|101035|101036|101037|101038|101039|101040|101041|101042|102000|102001|102002|102003|102004|102005|102006|102007|102008|102009|102010|102011|102012|102013|102015|102016|102017|102018|102019|102020|102021|102022|102023|102024|102025|102026|102027|102028|102029|102030|102031|102032|102033|102034|102035|102036|102037|102038|102039|102040|102041|102042|103000|103001|103002|103003|103004|103006|103008|103009|103010|103011|103012|103013|103014|103015|103016|103017|103018|103019|103020|103021|103022|103023|103024|103025|103026|103027|103028|103029|103030|103031|103032|103033|103034|103035|103036|103037|103038|103039|103040|103041|103042|103043|104000|104001|104002|104003|104004|104005|104006|104007|104008|104009|104010|104011|104012|104013|104014|104015|104016|104017|104018|104019|104020|104021|104022|104023|104024|104025|104026|104027|104028|104029|104030|104031|104032|104033|104034|104035|104036|104037|105000|105001|105002|105003|105004|105005|105006|105007|105008|105009|105010|105011|105012|105013|105014|105015|105016|105017|105018|105019|105020|105021|105023|105024|105025|105026|105027|105028|105029|105030|105031|105032|105033|105034|105035|105036|105037|105038|105039|105040|105041|105042|106000|106001|106002|106003|106004|106005|106006|106007|106008|106009|106010|106011|106014|106015|106016|106017|106018|106019|106020|106021|106022|106023|106024|106025|106026|106027|106028|106029|106030|106031|106032|106033|106034|106035|107001|107002|107003|107004|107005|107006|107007|107008|107009|107010|107011|107012|107013|107017|107018|107019|107020|107021|107022|107023|107024|107025|107026|107027|107028|107029|107030|107031|107032|108000|108001|108002|108003|108004|108006|108007|108008|108009|108010|108011|108012|108013|108015|108016|108017|108018|108019|108020|108021|108022|108023|108024|109000|109001|109002|109004|109005|109006|109007|109008|109009|109010|109011|109012|109013|109014|109015|109016|109017|109018|109019|109020|109021|109022|109023|109024|109026|109027|109028|109029|109030|109031|109032|109033|109034|109036|109037|109038|109039|109040|109041|109042|110000|110001|110002|110003|110004|110005|110006|110007|110008|110009|110010|110011|110012|110013|110014|110015|110016|110017|110018|110019|110020|110021|110022|110023|110024|110025|110026|110027|110028|110029|110030|110031|110032|110033|110034|110035|110036|110037|111000|111001|111002|111003|111004|111005|111006|111007|111008|111009|111010|111011|111012|111013|111014|111015|111016|111017|111018|111019|111020|111021|111022|111023|111024|111025|111026|111027|111029|111030|111031|111032|111033|111034|111035|111036|111037|111038|112000|112001|112002|112003|112004|112005|112006|112007|112008|112009|112010|112011|112012|112013|112014|112015|112016|112017|112018|112019|112020|112021|112022|112023|112024|112025|112026|112027|112028|112029|112030|112031|112032|113000|113001|113002|113003|113004|113005|113006|113007|113008|113009|113010|113011|113012|113013|113014|113015|113016|113017|113018|113019|113020|113021|113022|113023|113024|113025|113026|113027|113029|113030|113031|113032|113033|113034|113035|113036|113037|113038|113039|113040|113041|113042|113043|114000|114001|114002|114003|114004|114005|114006|114007|114009|114011|114012|114013|114014|114015|114016|114017|114018|114019|114020|114021|114022|114023|114024|114025|114027|114028|114029|114030|114031|114032|114034|114035|114036|114037|114039|114040|114041|114042|114043|114044|114046|114048|114049|114050|114051|114052|114053|114054|114055|114056|114057|114058|114059|114060|120000|200000|200001|200002|200003|200004|200005|200006|200007|200008|200009|200010|200011|200012|200013|200014|200015|200016|200017|200018|200019|200020|200021|200022|200023|200024|200025|200026|200027|200028|200029|200030|200031|200032|200033|200034|200035|200036|200037|200038|200039|200040|200041|200042|200043|200044|200045|200046|200047|200048|200049|200050|200051|200052|200053|200054|200055|200056|200057|200058|200059|200060|200061|200062|200063|200066|200067|200068|200069|200070|200071|200072|200073|200075|200076|200078|200079|200080|200081|200082|200083|200084|200085|200086|200087|200088|200089|200090|200091|200092|200093|200095|200096|200097|200100|200103|200104|200105|200106|200107|200108|200109|200113|200114|200115|200116|200117|200118|200119|200120|200121|200122|200123|200124|200125|200126|200127|200128|200129|200130|200131|200133|200135|200136|210001|210002|210003|210004|210005|210006|210007|210008|210009|210010|210011|210012|210013|210014|210015|210101|210102|210103|210201|210203|210204|210205|210206|210208|210209|210210|210211|210212|210213|210214|210301|210302|210303|210304|210305|210306|210401|210402|210403|210404|210405|210406|210407|210408|210409|210501|210502|210503|210504|210505|210506|210507|210508|210509|210601|210602|210603|210604|210605|210606|210607|210616|210617|210618|210619|210701|210702|210703|210704|210705|210706|210707|210708|210709|211401|211402|211403|211501|211502|211503|211504|211601|211602|211603|211604|211605|240000|240001|240002|240003|240004|240005|240006|240007|240008|240009|240010|240011|240012|240013|240014|240015|240016|240100|240101|240102|240103|240104|240105|240106|240107|240108|240109|240200|240201|240202|240203|240204|240205|240206|240207|240208|240209|240210|240211|240212|240213|240214|240215|240216|240218|240219|240220|240221|240222|240223|240300|240301|240302|240303|240304|240306|240307|240308|240309|240310|240312|240313|240314|240315|240400|240401|240402|240403|240404|240406|240407|240408|240410|240411|240412|240413|240414|240415|240416|240419|240500|240501|240502|240503|240504|240505|240506|240507|240509|240510|240512|240514|240515|240516|240517|240519|240520|240521|240522|240523|240530\r\ntype IRefskillAttr_key='万剑诀伤害'|'万剑诀伤害次数'|'万剑诀伤害百分比'|'万剑诀伤害系数'|'万剑诀伤害间隔'|'万剑诀冷却'|'万剑诀冷却百分比'|'万剑诀击退'|'万剑诀命中冰冻的怪物时产生折射'|'万剑诀命中后爆炸'|'万剑诀增伤对主目标每次伤害提升'|'万剑诀增伤对主目标的伤害会逐步提高百分比'|'万剑诀子弹模型'|'万剑诀对主目标额外伤害'|'万剑诀折射'|'万剑诀折射伤害百分比'|'万剑诀持续伤害'|'万剑诀持续伤害百分比'|'万剑诀持续伤害系数'|'万剑诀持续时间百分比'|'万剑诀攻击距离'|'万剑诀杀死怪物后增加伤害次数'|'万剑诀次级速度'|'万剑诀爆炸伤害初始百分比'|'万剑诀爆炸伤害百分比'|'万剑诀爆炸范围'|'万剑诀类型'|'万剑诀范围伤害百分比'|'万剑诀范围伤害系数'|'万剑诀速度'|'万剑诀附加引燃伤害间隔'|'万剑诀附加引燃初始伤害百分比'|'万剑诀附加引燃时间'|'万剑诀齐射'|'万剑诀齐射分叉角度'|'万剑诀齐射发射间隔'|'万剑诀齐射速度'|'三棱寒冰箭分裂子弹发射间隔'|'三棱寒冰箭分裂子弹数量'|'三棱寒冰箭子弹模型'|'不受伤害同时对怪物造成攻击伤害百分比'|'余烬伤害初始百分比'|'余烬伤害间隔'|'余烬初始范围'|'余烬子弹模型'|'余烬持续时间'|'光剑扫射伤害百分比'|'光剑扫射折返次数'|'光剑扫射结束角度'|'光剑扫射起始角度'|'光剑系伤害百分比'|'冰箭风暴伤害百分比'|'冰箭风暴冰箭子弹数量'|'冰系伤害百分比'|'冷冻场范围百分比'|'冻伤叠加上限'|'击杀精英怪和首领后,立即回复城墙生命值上限百分比'|'前5波,造成伤害增加百分比'|'剑气冲击波击退效果'|'剑气冲击波附带击退效果'|'剑气斩伤害'|'剑气斩伤害次数'|'剑气斩伤害次数翻倍'|'剑气斩伤害百分比'|'剑气斩伤害系数'|'剑气斩伤害范围'|'剑气斩伤害范围百分比'|'剑气斩伤害间隔'|'剑气斩使怪物在受到伤害加深时间'|'剑气斩冷却'|'剑气斩冷却百分比'|'剑气斩分叉角度'|'剑气斩命中后1秒内怪物速度百分比'|'剑气斩命中后怪物减速时间'|'剑气斩增加伤害次数'|'剑气斩子弹数量'|'剑气斩子弹模型'|'剑气斩对相同目标造成伤害逐步提升'|'剑气斩扫射发射间隔'|'剑气斩持续伤害'|'剑气斩持续伤害百分比'|'剑气斩持续伤害系数'|'剑气斩持续时间'|'剑气斩攻击距离'|'剑气斩散射模型'|'剑气斩杀死怪物时射出一束剑气'|'剑气斩杀死的怪物无法分裂'|'剑气斩次数百分比'|'剑气斩类型'|'剑气斩结束时晕眩范围内怪物时间'|'剑气斩范围伤害'|'剑气斩范围伤害百分比'|'剑气斩范围伤害系数'|'剑气斩附加1秒眩晕的概率'|'剑诀折射伤害百分比'|'反弹子弹概率'|'受到伤害加深时间'|'受到伤害加深百分比'|'受到伤害降低'|'向后传送的概率'|'向后传送的距离'|'命中怪物时,概率释放无强化单体惊雷咒次数'|'命中首个怪物释放小风暴术数量'|'土系伤害百分比'|'地图全长'|'城墙下是否出现毒墙'|'城墙下是否生成荆棘'|'城墙不受伤害次数'|'城墙受到伤害时是否有概率将伤害变为0点'|'城墙受到伤害降低点数'|'城墙基础血量增加'|'城墙技光剑扫射结束角度'|'城墙技光剑扫射起始角度'|'城墙每损失血量就落下一个滚石百分比'|'城墙每损失血量就落下一个落石百分比'|'城墙每损失血量就释放滚石数量'|'城墙血量不足时,造成伤害百分比'|'城墙近战反伤伤害百分比'|'城墙近战反伤概率'|'城墙远程反伤伤害百分比'|'城墙首次血量低于时,受到的所有伤害归0时间'|'大型风暴术伤害百分比'|'大型风暴术伤害范围'|'大型风暴术伤害间隔'|'大型风暴术子弹模型'|'大型风暴术持续时间'|'大型风暴术牵引力百分比'|'天雷场伤害百分比'|'天雷场伤害间隔'|'天雷场初始伤害百分比'|'天雷场初始范围'|'天雷场子弹模型'|'天雷场持续时间'|'天雷网伤害'|'天雷网伤害次数'|'天雷网伤害百分比'|'天雷网伤害系数'|'天雷网伤害范围'|'天雷网伤害范围百分比'|'天雷网伤害间隔'|'天雷网冷却'|'天雷网冷却百分比'|'天雷网击退'|'天雷网击退百分比'|'天雷网发射次数'|'天雷网发射间隔'|'天雷网右发射角度'|'天雷网命中怪物后是否触发闪电链'|'天雷网命中每个怪物时对周围怪物造成伤害范围'|'天雷网命中每个怪物时对周围怪物造成范围伤害百分比'|'天雷网命中被点燃的怪物时产生爆炸'|'天雷网子弹模型'|'天雷网左发射角度'|'天雷网持续伤害'|'天雷网持续伤害百分比'|'天雷网持续伤害系数'|'天雷网攻击距离'|'天雷网杀死怪物后,在500范围内触发惊雷咒次数'|'天雷网爆炸伤害初始百分比'|'天雷网爆炸模型'|'天雷网爆炸范围'|'天雷网牵引'|'天雷网牵引伤害次数'|'天雷网牵引伤害间隔'|'天雷网类型'|'天雷网范围伤害'|'天雷网范围伤害百分比'|'天雷网范围伤害系数'|'天雷网麻痹效果时间'|'学徒伤害百分比'|'宝石火焰弹伤害百分比'|'宝石额外光剑扫射结束角度'|'宝石额外光剑扫射起始角度'|'寒冰突刺中心区域范围大小'|'寒冰突刺伤害'|'寒冰突刺伤害次数'|'寒冰突刺伤害百分比'|'寒冰突刺伤害系数'|'寒冰突刺伤害范围'|'寒冰突刺伤害范围百分比'|'寒冰突刺伤害间隔'|'寒冰突刺冷冻场结束后寒冰突刺次数'|'寒冰突刺冷却'|'寒冰突刺冷却百分比'|'寒冰突刺冻结怪物后使其每秒损失最大生命百分比'|'寒冰突刺冻结时间'|'寒冰突刺分裂小寒冰箭数量'|'寒冰突刺发射次数'|'寒冰突刺发射间隔'|'寒冰突刺命中怪物时概率释放无强化寒冰箭个数'|'寒冰突刺对中心区域敌人冻结伤害百分比'|'寒冰突刺对中心区域敌人冻结时间翻倍'|'寒冰突刺对中心区域敌人额外造成伤害次数'|'寒冰突刺持续伤害'|'寒冰突刺持续伤害百分比'|'寒冰突刺持续伤害系数'|'寒冰突刺攻击距离'|'寒冰突刺生成的冷冻场伤害百分比'|'寒冰突刺类型'|'寒冰突刺范围伤害'|'寒冰突刺范围伤害百分比'|'寒冰突刺范围伤害系数'|'寒冰突刺附加2秒无视冻结的深度冻结状态的概率'|'寒冰突刺首次释放时若只命中一个怪物则冷却减少百分比'|'寒冰箭不超过最大攻击的倍数'|'寒冰箭伤害'|'寒冰箭伤害百分比'|'寒冰箭伤害系数'|'寒冰箭冷却'|'寒冰箭冷却百分比'|'寒冰箭冻伤初始伤害'|'寒冰箭冻伤间隔'|'寒冰箭冻结时间'|'寒冰箭击退'|'寒冰箭击退百分比'|'寒冰箭分叉角度'|'寒冰箭分裂'|'寒冰箭发射次数'|'寒冰箭发射间隔'|'寒冰箭子弹数量'|'寒冰箭子弹模型'|'寒冰箭对被麻痹的怪物造成伤害百分比'|'寒冰箭抵达边缘时会原路折返'|'寒冰箭持续伤害系数'|'寒冰箭攻击距离'|'寒冰箭施加冻伤时间'|'寒冰箭穿透'|'寒冰箭类型'|'寒冰箭累计命中n次释放一次冰箭风暴III'|'寒冰箭累计命中n次释放一次冰箭风暴V'|'寒冰箭累计命中n次释放一次冰箭风暴VII'|'寒冰箭范围伤害系数'|'寒冰箭速度'|'寒冰箭速度百分比'|'寒冰箭首次命中时,释放1个无强化的寒冰突刺'|'对有负面状态的怪物造成伤害百分比'|'对满血敌人的伤害必定暴击'|'对精英和首领单位伤害百分比'|'对血量高于的怪物造成伤害百分比'|'对距离城墙400距离内的怪物造成伤害百分比'|'对路径间的敌人造成伤害百分比'|'将伤害变为0点的概率'|'小寒冰箭伤害百分比'|'小寒冰箭冰冻初始时间'|'小寒冰箭冻伤初始伤害'|'小寒冰箭冻伤时间'|'小寒冰箭初始伤害百分比'|'小寒冰箭受击模型'|'小寒冰箭子弹模型'|'小寒冰箭穿透'|'小寒冰箭附加冻伤'|'小旋风伤害初始百分比'|'小旋风数量'|'小旋风穿透'|'小电球伤害初始百分比'|'小电球伤害百分比'|'小电球子弹模型'|'小电球子弹速度'|'小电球穿透次数'|'小电球麻痹时间'|'小落石伤害百分比'|'小落石伤害范围'|'小闪电初始伤害'|'小闪电球伤害范围'|'小闪电球子弹模型'|'小闪电球持续时间'|'小闪电球牵引'|'小闪电球速度'|'小风暴术伤害间隔'|'小风暴术初始伤害百分比'|'小风暴术持续时间'|'小风暴术牵引'|'小风暴术结束时会再释放小旋风数量'|'小风暴术范围'|'小黑洞伤害间隔'|'小黑洞持续时间'|'小黑洞牵引'|'小黑洞范围'|'局内学习技能数量'|'巨石突刺不超过最大攻击的倍数'|'巨石突刺伤害'|'巨石突刺伤害百分比'|'巨石突刺伤害系数'|'巨石突刺伤害范围'|'巨石突刺伤害范围百分比'|'巨石突刺余烬伤害初始百分比'|'巨石突刺余烬伤害间隔'|'巨石突刺余烬初始范围'|'巨石突刺余烬持续时间'|'巨石突刺冷却'|'巨石突刺冷却百分比'|'巨石突刺命中后眩晕'|'巨石突刺命中后附加灼烧'|'巨石突刺子弹模型'|'巨石突刺对被冻结的怪物造成伤害百分比'|'巨石突刺持续伤害系数'|'巨石突刺攻击距离'|'巨石突刺有概率造成伤害百分比'|'巨石突刺杀死处于燃烧状态时是否留下余烬'|'巨石突刺点燃可叠加层数'|'巨石突刺眩晕时间'|'巨石突刺类型'|'巨石突刺结束后释放次数'|'巨石突刺范围伤害'|'巨石突刺范围伤害百分比'|'巨石突刺范围伤害系数'|'巨石突刺释放次数'|'巨石突刺释放间隔'|'巨石突刺附加冻结'|'巨石突刺附加冻结时间'|'巨石突刺附加灼烧伤害初始百分比'|'巨石突刺附加灼烧时间'|'引燃的怪物死亡时发生爆炸'|'御剑术不超过最大攻击的倍数'|'御剑术伤害'|'御剑术伤害百分比'|'御剑术伤害系数'|'御剑术冷却'|'御剑术冷却百分比'|'御剑术分叉角度'|'御剑术分裂'|'御剑术分裂击杀分叉角度'|'御剑术发射次数'|'御剑术发射间隔'|'御剑术命中后爆炸'|'御剑术命中时概率附加冻结时间'|'御剑术命中时附加0.5秒麻痹的概率'|'御剑术命中附加点燃'|'御剑术子弹数量'|'御剑术子弹模型'|'御剑术对眩晕怪物伤害百分比'|'御剑术扩散'|'御剑术扩散分叉角度'|'御剑术持续伤害系数'|'御剑术攻击距离'|'御剑术次级伤害百分比'|'御剑术次级初始伤害百分比'|'御剑术次级命中后爆炸'|'御剑术次级模型'|'御剑术次级爆炸伤害初始百分比'|'御剑术次级爆炸模型'|'御剑术爆炸伤害初始百分比'|'御剑术爆炸伤害百分比'|'御剑术爆炸伤害范围'|'御剑术爆炸模型'|'御剑术穿透'|'御剑术类型'|'御剑术累计命中n次释放一次御剑风暴III'|'御剑术累计命中n次释放一次御剑风暴V'|'御剑术累计命中n次释放一次御剑风暴VII'|'御剑术范围伤害'|'御剑术范围伤害百分比'|'御剑术范围伤害系数'|'御剑术速度'|'御剑风暴伤害百分比'|'御剑风暴分叉角度'|'御剑风暴飞弹子弹数量'|'怪物中毒持续时间'|'怪物接触城墙时是否榴弹'|'怪物接触城墙时是否释扫射激光'|'怪物死亡时发生爆炸范围'|'怪物进入城墙200范围内释放滚石次数'|'怪物靠近城墙时是否释放石狮子'|'惊雷咒伤害'|'惊雷咒伤害百分比'|'惊雷咒伤害系数'|'惊雷咒伤害范围'|'惊雷咒伤害范围百分比'|'惊雷咒冷却'|'惊雷咒冷却百分比'|'惊雷咒发射次数'|'惊雷咒发射间隔'|'惊雷咒命中后,释放一个无强化的连环闪电,弹射次数'|'惊雷咒命中后是否释放一个无强化的连环闪电'|'惊雷咒命中后爆炸'|'惊雷咒命中后释放小电球数量'|'惊雷咒命中时是否追加范围伤害'|'惊雷咒子弹模型'|'惊雷咒持续伤害系数'|'惊雷咒攻击距离'|'惊雷咒杀死怪物后,所有处于冷却中的技能冷却减少时间百分比'|'惊雷咒溅射伤害额外触发'|'惊雷咒爆炸伤害初始百分比'|'惊雷咒爆炸伤害百分比'|'惊雷咒爆炸初始范围'|'惊雷咒直接伤害百分比'|'惊雷咒第n次命中时追加范围伤害'|'惊雷咒类型'|'惊雷咒范围伤害'|'惊雷咒范围伤害百分比'|'惊雷咒范围伤害系数'|'惊雷咒麻痹时间'|'所有爆炸伤害百分比'|'所有穿透属性的技能穿透'|'扫射激光伤害'|'扫射激光冷却'|'扫射激光造成伤害时是否附加眩晕'|'扫射激光造成伤害时眩晕时间'|'技能伤害百分比'|'技能伤害随机浮动'|'技能伤害随机浮动上限'|'技能伤害随机浮动下限'|'技能冷却时间减少百分比'|'技能弹道与行进速度百分比'|'折射伤害初始百分比'|'持续落下无强化落石数量'|'持续落下无强化落石间隔'|'攻击力'|'攻击力百分比'|'攻击城墙的怪物是否有概率向后传送'|'旋风术伤害'|'旋风术伤害百分比'|'旋风术伤害系数'|'旋风术伤害范围'|'旋风术冷却'|'旋风术冷却百分比'|'旋风术减速时间'|'旋风术击退'|'旋风术击退百分比'|'旋风术分叉角度'|'旋风术发射次数'|'旋风术发射间隔'|'旋风术命中n次后释放闪电旋风'|'旋风术命中n次后释放闪电旋风数量'|'旋风术子弹模型'|'旋风术小旋风子弹模型'|'旋风术小风暴术子弹模型'|'旋风术持续伤害系数'|'旋风术攻击距离'|'旋风术旋风数量'|'旋风术牵引'|'旋风术直接伤害百分比'|'旋风术穿透'|'旋风术类型'|'旋风术范围伤害'|'旋风术范围伤害百分比'|'旋风术范围伤害系数'|'旋风术速度'|'旋风术造成伤害时,的概率附加眩晕时间'|'无强加落石术伤害范围'|'无强化剑气斩发射间隔'|'无强化滚石伤害范围'|'是否在战场上布置流沙阵法'|'是否持续落下无强化落石'|'是否是三菱冰箭'|'是否有概率反弹子弹'|'是否释放风刃锯齿'|'是否长剑破空'|'暴击伤害百分比'|'暴击时追加最大生命值不超过自身攻击的倍数'|'暴击时额外追加目标最大生命值百分比'|'暴击率'|'杀死怪物时释放单体惊雷咒次数'|'榴弹伤害'|'榴弹冷却'|'榴弹附加燃烧伤害百分比'|'榴弹附加燃烧伤害间隔'|'榴弹附加燃烧可叠加上限'|'榴弹附加燃烧时间'|'次级御剑术扩散分叉角度'|'每击杀一只怪城墙回血点数'|'每学习一个新技能,造成伤害百分比'|'每完成一个波次回血城墙生命百分比'|'每次命中都会爆炸'|'每释放N次剑气斩就会追加一次激光扫射'|'毒墙伤害'|'毒墙伤害百分比'|'毒墙伤害系数'|'毒墙伤害间隔'|'毒墙增加伤害百分比'|'流沙减速时间'|'流沙阵法伤害'|'流沙阵法伤害百分比'|'流沙阵法伤害系数'|'流沙阵法伤害范围'|'流沙阵法冷却'|'流沙阵法减速'|'流沙阵法增加伤害百分比'|'滚石不超过最大攻击的倍数'|'滚石伤害'|'滚石伤害百分比'|'滚石伤害系数'|'滚石伤害范围'|'滚石伤害范围百分比'|'滚石伤害间隔'|'滚石冷却'|'滚石冷却百分比'|'滚石击退'|'滚石击退效果百分比'|'滚石发射次数'|'滚石发射间隔'|'滚石命中怪物后减速时间'|'滚石命中怪物后概率击晕时间'|'滚石命中首个后爆炸'|'滚石子弹数量'|'滚石子弹模型'|'滚石对被冻结的目标伤害'|'滚石持续伤害系数'|'滚石攻击距离'|'滚石爆炸伤害百分比'|'滚石爆炸模型'|'滚石爆炸范围'|'滚石类型'|'滚石范围伤害'|'滚石范围伤害百分比'|'滚石范围伤害系数'|'滚石速度'|'滚石速度百分比'|'滚石附加燃烧'|'滚石附带的减速效果时间'|'激光折射伤害百分比'|'激光法术命中冰冻的怪物时产生折射'|'火焰弹不超过最大攻击的倍数'|'火焰弹伤害'|'火焰弹伤害百分比'|'火焰弹伤害系数'|'火焰弹伤害范围'|'火焰弹伤害范围百分比'|'火焰弹冷却'|'火焰弹冷却百分比'|'火焰弹击退'|'火焰弹击退效果百分比'|'火焰弹发射次数'|'火焰弹发射间隔'|'火焰弹命中后爆炸'|'火焰弹地面燃烧伤害范围'|'火焰弹地面燃烧伤害间隔'|'火焰弹地面燃烧模型'|'火焰弹子弹模型'|'火焰弹害百分比'|'火焰弹持续伤害系数'|'火焰弹攻击距离'|'火焰弹数量'|'火焰弹每命中n次就并列释放1波火焰弹'|'火焰弹波数'|'火焰弹火花模型'|'火焰弹燃烧伤害百分比'|'火焰弹爆炸伤害百分比'|'火焰弹爆炸初始伤害百分比'|'火焰弹爆炸后产生持续燃烧地面时间'|'火焰弹爆炸后释放火花数量'|'火焰弹爆炸模型'|'火焰弹爆炸范围百分比'|'火焰弹爆炸附加燃烧时间'|'火焰弹直接伤害百分比'|'火焰弹穿透'|'火焰弹类型'|'火焰弹范围伤害'|'火焰弹范围伤害百分比'|'火焰弹范围伤害系数'|'火焰弹速度'|'火焰弹附加1秒燃烧效果时间'|'火焰弹附加燃烧伤害间隔'|'火焰弹附加燃烧初始伤害'|'火焰弹附加燃烧效果时间'|'火焰弹附加眩晕'|'火焰风暴分叉角度'|'火系伤害百分比'|'火花初始伤害百分比'|'灵兽剑气伤害范围百分比'|'灵兽剑气命中后5秒内受到的伤害加深百分比'|'灵兽寒冰突刺伤害'|'灵兽寒冰突刺伤害次数'|'灵兽寒冰突刺伤害百分比'|'灵兽寒冰突刺伤害系数'|'灵兽寒冰突刺伤害范围'|'灵兽寒冰突刺伤害范围百分比'|'灵兽寒冰突刺伤害间隔'|'灵兽寒冰突刺冷却'|'灵兽寒冰突刺冷却百分比'|'灵兽寒冰突刺冻结时间'|'灵兽寒冰突刺发射次数'|'灵兽寒冰突刺发射间隔'|'灵兽寒冰突刺子弹模型'|'灵兽寒冰突刺循环子弹模型'|'灵兽寒冰突刺持续伤害'|'灵兽寒冰突刺持续伤害系数'|'灵兽寒冰突刺攻击距离'|'灵兽寒冰突刺消失子弹模型'|'灵兽寒冰突刺类型'|'灵兽寒冰突刺范围伤害'|'灵兽寒冰突刺范围伤害百分比'|'灵兽寒冰突刺范围伤害系数'|'灵兽御剑术伤害'|'灵兽御剑术伤害百分比'|'灵兽御剑术伤害系数'|'灵兽御剑术冷却'|'灵兽御剑术冷却百分比'|'灵兽御剑术发射次数'|'灵兽御剑术发射间隔'|'灵兽御剑术子弹模型'|'灵兽御剑术攻击距离'|'灵兽御剑术穿透'|'灵兽御剑术类型'|'灵兽御剑术速度'|'灵兽惊雷咒伤害'|'灵兽惊雷咒伤害百分比'|'灵兽惊雷咒伤害系数'|'灵兽惊雷咒伤害范围'|'灵兽惊雷咒冷却'|'灵兽惊雷咒发射次数'|'灵兽惊雷咒发射间隔'|'灵兽惊雷咒子弹模型'|'灵兽惊雷咒攻击距离'|'灵兽惊雷咒类型'|'灵兽惊雷咒范围伤害'|'灵兽惊雷咒范围伤害系数'|'灵兽惊雷咒附加麻痹时间'|'灵兽攻击力'|'灵兽每隔30秒朝N个方向发射剑气'|'灵兽毒云伤害百分比'|'灵兽毒云伤害范围'|'灵兽毒云伤害间隔'|'灵兽毒云冷却'|'灵兽毒云叠加上限'|'灵兽毒云子弹模型'|'灵兽毒云存在时间'|'灵兽毒云攻击距离'|'灵兽毒云每次伤害不能超过自身攻击的倍数'|'灵兽泰山造成伤害时除了精英和首领秒杀怪物概率百分比'|'灵兽落石术伤害'|'灵兽落石术伤害百分比'|'灵兽落石术伤害系数'|'灵兽落石术伤害范围'|'灵兽落石术冷却'|'灵兽落石术初始眩晕时间'|'灵兽落石术坑子弹模型'|'灵兽落石术子弹模型'|'灵兽落石术攻击距离'|'灵兽落石术眩晕时间'|'灵兽落石术类型'|'灵兽落石术范围伤害'|'灵兽落石术范围伤害系数'|'灵兽落石术落石数量'|'灵兽落石术落石间隔'|'灵兽落雷增加伤害百分比'|'灵兽释放冻结时附加无视冻结的深度冻结状态概率'|'灵兽风暴术伤害'|'灵兽风暴术伤害次数'|'灵兽风暴术伤害百分比'|'灵兽风暴术伤害系数'|'灵兽风暴术伤害范围'|'灵兽风暴术伤害间隔'|'灵兽风暴术冷却'|'灵兽风暴术子弹模型'|'灵兽风暴术持续伤害'|'灵兽风暴术持续伤害百分比'|'灵兽风暴术持续伤害系数'|'灵兽风暴术持续时间'|'灵兽风暴术持续时间百分比'|'灵兽风暴术攻击距离'|'灵兽风暴术数量'|'灵兽风暴术杀死怪物后持续时间'|'灵兽风暴术牵引'|'灵兽风暴术范围伤害'|'灵兽风暴术范围伤害百分比'|'灵兽风暴术范围伤害系数'|'灵兽风暴术速度'|'灵兽飞剑伤害范围'|'点燃伤害间隔'|'点燃初始伤害百分比'|'点燃可叠加层数'|'燃烧效果伤害间隔'|'燃烧额外追加目标最大生命值伤害百分比'|'爆炸后是否产生天雷场'|'爆炸后是否产生天雷场减速百分比'|'爆炸后是否产生天雷场是否减速'|'电击500范围内随机怪物伤害百分比'|'电击伤害百分比'|'电击间隔时间'|'电系伤害百分比'|'真空伤害'|'真空伤害百分比'|'真空是否造成范围伤害'|'真空是否附加眩晕'|'真空眩晕时间'|'立即恢复城墙生命值百分比'|'荆棘伤害'|'荆棘伤害百分比'|'荆棘伤害系数'|'荆棘伤害间隔'|'荆棘增加伤害百分比'|'落石不超过最大攻击的倍数'|'落石术中心区域小范围伤害百分比'|'落石术中心区域范围大小'|'落石术伤害'|'落石术伤害百分比'|'落石术伤害系数'|'落石术伤害范围'|'落石术伤害范围百分比'|'落石术余烬附带减速效果百分比'|'落石术冷却'|'落石术冷却百分比'|'落石术击退距离'|'落石术初始眩晕时间'|'落石术坑子弹模型'|'落石术子弹模型'|'落石术对被冻结的怪物造成伤害'|'落石术小坑模型'|'落石术小落石数量'|'落石术小落石模型'|'落石术持续伤害系数'|'落石术攻击距离'|'落石术有概率造成伤害百分比'|'落石术每命中n次追加小落石数量'|'落石术爆炸时有概率附带燃烧效果时间'|'落石术爆炸时附带击退'|'落石术爆炸是否产生余烬'|'落石术眩晕时间'|'落石术类型'|'落石术范围伤害'|'落石术范围伤害百分比'|'落石术范围伤害系数'|'落石术落石数量'|'落石术落石间隔'|'落石术速度百分比'|'被寒冰突刺冻结的怪物受到伤害加深百分比'|'被滚石命中的怪物在6秒内受到伤害加深百分比'|'被连环闪电麻痹的怪物受到攻击时是否会电击周围的怪物'|'触发闪电链伤害百分比'|'触发闪电链弹射次数'|'赋予怪物的负面状态持续时间增加百分比'|'近距离攻击城墙的怪物是否有概率反伤'|'进入关卡时立即进行选择奖励次数'|'连环闪电会将命中的怪物引燃时间'|'连环闪电伤害'|'连环闪电伤害百分比'|'连环闪电伤害系数'|'连环闪电冷却'|'连环闪电冷却百分比'|'连环闪电击晕敌人时,使其在5秒内额外受到伤害百分比'|'连环闪电初始目标'|'连环闪电命中后爆炸'|'连环闪电天雷场伤害间隔'|'连环闪电天雷场初始伤害百分比'|'连环闪电天雷场初始范围'|'连环闪电子弹模型'|'连环闪电引燃伤害百分比'|'连环闪电引燃伤害间隔'|'连环闪电弹射'|'连环闪电弹射间隔'|'连环闪电持续伤害系数'|'连环闪电攻击距离'|'连环闪电杀死怪物后会生成持续电磁场时间'|'连环闪电杀死怪物后是否产生天雷场'|'连环闪电每次伤害不超过自身攻击的倍数'|'连环闪电燃烧可叠加上限'|'连环闪电爆炸伤害初始百分比'|'连环闪电爆炸伤害范围'|'连环闪电牵引'|'连环闪电牵引范围'|'连环闪电类型'|'连环闪电范围伤害系数'|'连环闪电麻痹效果时间'|'追加范围伤害百分比'|'造成伤害时追加最大生命值不超过自身攻击的倍数'|'造成伤害时除了精英和首领秒杀怪物概率百分比'|'造成伤害时额外追加目标最大生命值百分比'|'释放剑气斩时是否会持续发射无强化的剑气斩'|'释放扫射激光次数'|'释放技能,额外释放技能的概率百分比'|'释放技能时忽视怪物的伤害减免效果百分比'|'释放方式修改为扫射'|'释放榴弹排数'|'释放榴弹是否附加燃烧'|'释放风刃锯齿次数'|'释放魔力枪是否附加眩晕'|'长剑破空伤害'|'长剑破空伤害百分比'|'长剑破空伤害系数'|'长剑破空冷却'|'长剑破空击退'|'长剑破空增加伤害百分比'|'长剑破空攻击距离'|'长剑破空附加眩晕时间'|'闪电旋风伤害百分比'|'闪电旋风伤害范围'|'闪电旋风分叉角度'|'闪电旋风穿透'|'闪电球伤害'|'闪电球伤害到的怪物在5秒内受到伤害提高百分比'|'闪电球伤害百分比'|'闪电球伤害系数'|'闪电球伤害范围'|'闪电球伤害范围百分比'|'闪电球伤害间隔'|'闪电球冷却'|'闪电球分裂后的闪电球体积不会减小'|'闪电球分裂数量'|'闪电球子弹模型'|'闪电球小型黑洞'|'闪电球小型黑洞模型'|'闪电球持续伤害'|'闪电球持续伤害百分比'|'闪电球持续伤害系数'|'闪电球持续时间'|'闪电球持续时间百分比'|'闪电球攻击距离'|'闪电球爆炸伤害初始百分比'|'闪电球爆炸子弹模型'|'闪电球爆炸范围'|'闪电球牵引'|'闪电球类型'|'闪电球累计伤害后爆炸'|'闪电球范围伤害'|'闪电球范围伤害百分比'|'闪电球范围伤害系数'|'闪电球速度'|'闪电球速度百分比'|'闪电球造成伤害时概率触发无强化的惊雷咒'|'闪电球飞行一段时间分裂穿透闪电球的数量'|'闪电球麻痹持续时间'|'闪电球黑洞'|'闪电球黑洞伤害初始百分比'|'闪电球黑洞持续时间'|'闪电球黑洞模型'|'闪电风暴持续电击附近范围内的随机怪物'|'闪电风暴连环闪电弹射次数'|'阵法石狮子中毒伤害叠加上限'|'阵法石狮子中毒伤害百分比'|'阵法石狮子中毒伤害间隔'|'阵法石狮子中毒时长'|'阵法石狮子伤害'|'阵法石狮子伤害百分比'|'阵法石狮子伤害系数'|'阵法石狮子冷却'|'阵法石狮子增加伤害百分比'|'阵法石狮子是否变为渗毒石狮子'|'阵法石狮子次数'|'阵法石狮子每次伤害不超过自身攻击的倍数'|'阵法石狮子速度'|'阵法石狮子释放间隔'|'附加点燃伤害间隔'|'附加点燃初始伤害百分比'|'附加点燃初始持续时间'|'附加点燃时间'|'附带燃烧效果初始百分比'|'雷暴子弹模型'|'风刃锯齿伤害'|'风刃锯齿伤害百分比'|'风刃锯齿伤害系数'|'风刃锯齿伤害间隔'|'风刃锯齿冷却'|'风刃锯齿增加伤害百分比'|'风刃锯齿持续时间'|'风刃锯齿移动速度'|'风刃锯齿释放间隔'|'风暴术伤害'|'风暴术伤害次数'|'风暴术伤害百分比'|'风暴术伤害系数'|'风暴术伤害范围'|'风暴术伤害范围百分比'|'风暴术伤害间隔'|'风暴术冷却'|'风暴术冷却百分比'|'风暴术子弹模型'|'风暴术对中心的怪物额外造成伤害百分比'|'风暴术对燃烧的怪物在3秒内燃烧额外造成最大生命值伤害百分比'|'风暴术对燃烧的怪物在3秒内额燃烧额外造成最大生命值百分比伤害'|'风暴术持续伤害'|'风暴术持续伤害百分比'|'风暴术持续伤害系数'|'风暴术持续时间'|'风暴术持续时间百分比'|'风暴术攻击距离'|'风暴术数量'|'风暴术杀死怪物后,持续时间'|'风暴术牵引'|'风暴术牵引百分比'|'风暴术类型'|'风暴术结束后释放大型风暴术数量'|'风暴术范围伤害'|'风暴术范围伤害百分比'|'风暴术范围伤害系数'|'风暴术速度'|'风暴术速度百分比'|'风暴术造成伤害时概率将怪物冻结时间'|'风系伤害百分比'|'飞行后小闪电球初始伤害百分比'|'飞行后小闪电球子弹模型'|'飞行后小闪电球子弹速度'|'飞行后小闪电球穿透次数'|'黑洞伤害间隔'|'黑洞牵引'|'黑洞范围'\r\ntype IRefskillAttr_type='number'|'string'\r\n\r\ninterface IRefskillAttrModifier extends IRefBase\r\n{\r\nid:IRefskillAttrModifier_id\r\nmodify:any[]\r\n\r\ndesc?:string\r\ndescribe?:{ [key in string]: any }\r\nicon?:string\r\nname?:IRefskillAttrModifier_name\r\n}\r\ninterface refGen{skillAttrModifier:IRefskillAttrModifier}\r\ntype IRefskillAttrModifier_id=1200000|20000001|20000002|20000003|20000004|20000005|20000006|20000007|20000008|20000009|20000010|20000011|20000012|20000013|20000014|20000015|20000016|20000017|20000018|20000019|20000020|20000021|20000022|20000023|20000024|20000025|20000026|20000027|20000028|20000029|20000030|20000031|20000032|20000033|20000034|20000035|20000036|20000037|20000038|20000039|20000040|20000041|20000042|20000043|20000044|20000045|20000046|20000047|20000048|20000049|20000050|20000051|20000052|20000053|20000054|20000055|20000056|20000057|20000058|20000059|20000060|20000061|20000062|20000063|20000064|20000065|20000066|20000067|20000068|20000069|20000070|20000073|20000074|20000075|20000076|20000077|20000078|20000079|20000080|20000081|20000082|20000083|20000084|20000085|20000086|20000087|20000088|20000089|20000090|20000091|20000094|20000095|20000096|20000099|20000100|20000101|20000102|20000103|20000104|20000105|20000108|20000109|20000110|20000111|20000112|20000113|20000114|20000115|20000116|20000117|20000118|20000119|20000120|20000121|20000122|20000123|20000124|20000125|20000126|20000127|20000128|20000129|20000130|20000131|20000132|20000133|20000136|20000137|20000138|20000139|20000143|20000144|20000145|20000146|20000151|20000152|20000153|20000155|20000156|20000157|20000158|20000159|20000160|20000161|20000164|20000165|20000166|20000167|20000168|20000172|20000173|20000174|20000175|20000177|20000178|20000179|20000180|20000181|20000182|20000186|20000187|20000188|20000189|20000194|20000195|20000196|20000202|20000203|20000210|20000211|20000212|20000213|20000214|20000215|20000216|20000217|20000224|20000229|20000230|20000231|20000236|20000237|20000238|20000243|20000244|20000245|20000249|20000250|20000251|20000252|20000253|20000254|20000255|20000256|20000257|20000258|20000259|20000260|20000261|20000262|20000263|20000264|20000265|20000266|20000267|20000268|20000269|20000270|20000271|20000272|20000273|20000274|20000275|20000276|20000277|20000278|20000279|20000280|20000309|20000310|20000311|20000312|20000313|20000314|20000315|20000316|20000317|20000318|20000319|20000320|20000321|20000322|20000323|20000324|20000325|20000326|20000327|20000328|20000329|20000330|20000331|20000332|20000333|20000334|20000335|20000336|20000337|20000338|20000339|20000340|20000341|20000342|20000343|20000344|20000345|20000346|20000347|20000348|20000349|20000350|20000351|20000352|20000353|20000354|20000355|20000356|20000357|20000358|20000359|20000360|20000361|20000362|20000363|20000364|20000365|20000366|20000367|20000368|20000369|20000370|20000371|20000372|20000373|20000374|20000375|20000376|20000377|20000378|20000379|20000380|20000381|20000382|20000383|20000384|20000385|20000387|20000388|20000389|20000390|20000391|20000392|20000397|20000398|20000404|20000405|20000409|20000410|20000411|20000412|20000414|20000415|20000416|20000417|20000421|20000422|20000423|20000424|20000425|20000426|20000427|20000430|20000431|20000432|20000433|20000434|20000438|20000439|20000440|20000441|20000445|20000446|20000451|20000452|20000453|20000454|20000455|20000459|20000460|20000461|20000466|20000467|20000468|20000469|20000472|20000473|20000474|20000481|20000487|20000488|20000489|20000490|20000494|20000495|20000496|20000500|20000501|20000502|20000503|20000504|20000509|20000510|20000511|20000514|20000515|20000516|20000521|20000522|20000523|20000528|20000529|20000530|20000531|20000536|20000537|20000538|20000544|20000545|20000550|20000551|20000552|20000557|20000558|20000559|20000564|20000565|20000566|20000567|20000572|20000579|20000581|20000588|20000591|20000592|20000593|20000594|20000595|20000599|20000600|20000606|20000615|20000620|20000621|20000622|20000623|20000629|20000637|20000643|20000648|20000649|20000650|20000654|20000655|20000656|20000657|20000658|20000665|20000669|20000670|20000671|20000676|20000677|20000678|20000684|20000690|20000691|20000692|20000693|20000699|20000700|20000705|20000706|20000707|20000710|20000711|20000712|20000713|20000714|20000718|20000719|20000726|20000727|20000728|20000733|20000740|20000747|20000754|20000762|20000763|21000000|21000001|21000002|21000003|21000004|21000005|21000006|21000007|21000100|21000101|21000102|21000103|21000104|21000105|21000106|21000107|21000200|21000201|21000202|21000203|21000204|21000205|21000206|21000207|21000300|21000301|21000302|21000303|21000304|21000305|21000306|21000307|21000400|21000401|21000402|21000403|21000404|21000405|21000406|21000407|21000500|21000501|21000502|21000503|21000504|21000505|21000506|21000507|21000600|21000601|21000602|21000603|21000604|21000605|21000606|21000607|21000700|21000701|21000702|21000703|21000804|21000805|21000806|21000807|21000904|21000905|21000906|21000907|21001000|21001001|21001002|21001003|21001004|21001005|21001006|21001007|22000000|22000001|22000002|22000003|22000004|22000005|23000000|23000001|23000002|23000003|23000004|23000005|23000006|24000000|24000001|24000002|24000003|24000004|24000005|24000006|24000007|24000008|24000009|24000010|24000011|24000012|24000013|24000014|24000015|24000016|24000017|24000018|24010000|24010001|24010002|24010003|24010004|24010005|24010006|24010007|24010008|24010009|24010010|24010011|24010012|24010013|24010014|24010015|24010016|24010017|24010018|24020000|24020001|24020002|24020003|24020004|24020005|24020006|24020007|24020008|24020009|24020010|24020011|24020012|24020013|24020014|24020015|24020016|24020017|24020018|24030000|24030001|24030002|24030003|24030004|24030005|24030006|24030007|24030008|24030009|24030010|24030011|24030012|24030013|24030014|24030015|24030016|24030017|24030018|24040000|24040001|24040002|24040003|24040004|24040005|24040006|24040007|24040008|24040009|24040010|24040011|24040012|24040013|24040014|24040015|24040016|24040017|24040018|24050000|24050001|24050002|24050003|24050004|24050005|24050006|24050007|24050008|24050009|24050010|24050011|24050012|24050013|24050014|24050015|24050016|24050017|24050018|25000000|25000001|25000002|25000003|25000004|25000005|25000006|25000007|25000008|25000009|25000010|25000011|25000012|25000013|25000014|25000015|25000016|25000017|25000018|25000019|3000100|3000101|3000102|3000103|3000104|3000105|3000106|3000107|3000108|3000109|3000110|3000111|3000112|3000113|3000114|3000115|3000116|3000117|3000118|3000119|3000120|3000121|3000122|3000123|3001100|3001101|3001102|3001103|3001105|3001106|3001107|3001108|3001109|3001110|3001111|3001112|3001113|3001114|3001115|3001116|3001117|3001118|3001119|3001120|3001121|3002100|3002101|3002102|3002103|3002104|3002105|3002106|3002107|3002108|3002109|3002110|3002111|3002112|3002113|3002114|3002115|3002116|3002117|3002118|3002120|3002121|3002122|3003100|3003101|3003102|3003103|3003104|3003105|3003106|3003107|3003108|3003109|3003110|3003111|3003112|3003113|3003114|3003115|3003116|3003117|3003118|3003119|3003120|3003121|3003122|3004100|3004101|3004102|3004103|3004104|3004105|3004106|3004107|3004108|3004109|3004110|3004111|3004112|3004113|3004114|3004115|3004116|3004117|3004118|3004119|3004120|3004121|3004122|3005100|3005101|3005102|3005103|3005104|3005105|3005106|3005107|3005108|3005109|3005110|3005111|3005112|3005113|3005114|3005115|3005116|3005117|3005118|3006100|3006101|3006102|3006103|3006104|3006105|3006106|3006107|3006108|3006109|3006110|3006111|3006112|3006113|3006114|3006115|3007100|3007101|3007102|3007103|3007104|3007105|3007106|3007107|3007108|3007109|3007110|3007111|3007112|3007113|3007114|3007115|3007116|3007117|3008100|3008101|3008102|3008103|3008104|3008105|3008106|3008107|3008108|3008109|3008110|3008111|3008112|3008113|3008114|3008115|3008116|3009100|3009101|3009102|3009103|3009104|3009105|3009106|3009107|3009108|3009109|3009110|3009111|3009112|3009113|3009114|3009115|3009116|3010100|3010101|3010102|3010103|3010104|3010105|3010106|3010107|3010108|3010109|3010110|3010111|3010112|3010113|3010114|3010115|3010116|3011100|3011101|3011102|3011103|3011104|3011105|3011106|3011107|3011108|3011109|3011110|3011111|3011112|3011113|3011114|3011115|3011116|3011117|3011118|3012100|3012101|3012102|3012103|3012104|3012105|3012106|3012107|3012108|3012109|3012110|3012111|3012112|3012113|3012114|3012115|3013100|3013101|3013102|3013103|3013104|3013105|3013106|3013107|3013108|3013109|3013110|3013111|3013112|3013113|3013114|3013115|3013116|3013117|3013118|3013119|3013120|3014100|3014101|3014102|3014103|3014104|3014105|3014106|3014107|3014108|3014109|3014110|3014111|3014112|3014113|3014114|3014115|3014116|3014117|3014118\r\ntype IRefskillAttrModifier_name='九天惊雷'|'修炼万剑诀'|'修炼剑气斩'|'修炼天雷网'|'修炼寒冰突刺'|'修炼寒冰箭'|'修炼巨石突刺'|'修炼御剑术'|'修炼惊雷咒'|'修炼旋风术'|'修炼毒气弹'|'修炼泰山压顶'|'修炼石狮子'|'修炼连环闪电'|'修炼闪电球'|'修炼风暴术'|'光剑增幅'|'光剑增幅I'|'光剑超载'|'冰封千里'|'冰片伤害增幅'|'冰片分裂'|'击杀触发电击'|'分裂'|'制导飞剑'|'刺骨冰寒'|'剑定天下'|'剑气乱射'|'剑气冲击波'|'剑气威力增强III'|'剑气扩散'|'剑气斩折射'|'剑荡四方'|'剑诀折射'|'剑诀爆破'|'剑诀重击'|'双发寒冰箭'|'双发惊雷咒'|'双发毒气弹'|'双发石狮子'|'双发飞剑'|'双重陨石'|'双重风暴'|'受伤加深'|'吸附力提升'|'吸附提升'|'命中冰冻怪物时折射'|'命中概率眩晕'|'噬骨之寒'|'噬骨冰棱'|'备用石狮'|'多棱寒冰'|'大型风暴聚集'|'天外陨石'|'天雷分裂'|'天雷感应'|'天雷滚滚'|'天雷滚滚II'|'天雷网增幅'|'天雷轰顶'|'天雷降世'|'夺命冰息'|'奔跑吧，狮子！'|'寒冰伤害增强'|'寒冰扩散'|'寒冰法术'|'寒冰法术精通'|'寒冰碎裂'|'寒冰碎裂增强'|'寒冰箭伤害增幅'|'寒冰箭冲击'|'寒冰箭分裂'|'寒冰箭贯穿'|'寒冰箭连发'|'对中心的怪物额外伤害增加'|'对冰冻状态伤害增加'|'对眩晕的敌人伤害提升'|'对被冰冻的怪物造成伤害提升'|'小型虚空结界'|'小型风暴聚集'|'小型飞剑增强'|'小寒冰穿透'|'小寒冰箭附冰冻'|'山体增长'|'山体重击'|'巨型石狮子'|'巨大寒冰突刺'|'御剑术增幅'|'御剑术精通'|'急冻领域'|'急速坠落'|'怪物在5秒内受到伤害提高30%'|'惊雷咒伤害增幅'|'惊雷咒溅射'|'扫射剑气'|'扫射剑气折返'|'折返'|'旋风乱流'|'旋风乱流II'|'旋风凝滞'|'旋风术增强'|'旋风术连发'|'旋风术连发II'|'旋风真空'|'旋风突袭'|'旋风齐射'|'无双剑气'|'无限风暴'|'星火燎原'|'极寒冰心'|'极寒寒冰箭'|'极度深寒'|'概率冰冻'|'概率增伤'|'概率眩晕'|'概率造成伤害提升'|'概率释放寒冰箭'|'次级爆破'|'毒气侵袭'|'毒气冲击'|'毒气减速'|'毒气弹精通'|'毒气沼泽'|'毒气渗透'|'毒气溅射'|'毒气爆发'|'毒气爆炸增强'|'毒气蔓延'|'毒气轰炸'|'毒雾缭绕'|'泰山压顶伤害增强'|'泰山坠落'|'泰山连击'|'混沌陨石'|'渗毒岩石'|'渗毒石狮'|'渗毒飞剑'|'激光延续'|'激光灭杀'|'激光超载'|'爆炸击退'|'爆炸附中毒'|'爆破增幅'|'爆破御剑'|'爆裂冰晶'|'球体扩张'|'瓦解射线'|'电击传感'|'电击回路'|'电击流转'|'电球吸附'|'电网击退'|'电网击退II'|'电网双发'|'电网双发II'|'电网吸附'|'电网爆炸'|'疯狂毒气弹'|'真空崩塌'|'石狮子伤害增幅'|'石狮子冲击'|'石狮子急行'|'石狮子拓展'|'碎冰'|'稳定光线'|'稳定雷池'|'突刺伤害增强'|'突刺冷却'|'突刺眩晕'|'突刺碎裂'|'突刺范围增长'|'突刺连击'|'精准打击'|'群山压顶2'|'聚焦伤害增幅'|'脉冲伤害增幅'|'致命之雾'|'致命剑诀'|'致残石狮子'|'范围增幅'|'范围爆炸'|'虚空结界'|'衰弱光线'|'贯穿旋风'|'贯穿飞剑'|'转移攻击对象速度提升'|'连环冰刺'|'连环电网'|'连环突刺'|'连续石狮子'|'迷雾扩散'|'追加伤害'|'追加惊雷咒'|'重创'|'重型突刺'|'重寒冰箭'|'闪电伤害增强'|'闪电延续'|'闪电扩张'|'闪电球伤害增强'|'闪电球分裂'|'闪电球分裂II'|'闪电球延续'|'闪电球爆炸'|'闪电粒子爆破'|'闪电风暴'|'阵法修复'|'附加中毒'|'附加中毒,概率附加冰冻'|'附加燃烧'|'附加眩晕'|'附寒冰突刺'|'附带击退'|'陨石风暴'|'雷暴'|'雷暴伤害增幅'|'雷池'|'雷法精通'|'雷电天罚'|'雷神之力'|'雷系法术增强'|'雷霆万钧'|'风之祝愿'|'风卷残云'|'风暴吸附'|'风暴增长'|'风暴术伤害增强'|'风暴术延续'|'风暴突袭'|'风暴聚集'|'风起云涌'|'飞剑分裂'|'飞剑四射'|'飞剑威力增强'|'飞剑威力增强II'|'飞剑连发II'|'飞剑齐射'|'高压小电球'|'高压惊雷咒'|'高压电击'|'高压电网'|'高压雷池'|'高速闪电球'|'魔雾缥缈'|'麻痹增伤'|'麻痹闪电球'\r\n\r\ninterface IRefskillBook extends IRefBase\r\n{\r\nattr:any[]\r\ndisplay:number\r\nid:IRefskillBook_id\r\nlevel:IRefskillBook_level\r\nskillId:number\r\ntlShowAttr:any[]\r\n\r\nmodify?:any[]\r\nmodifyDesc?:string\r\n}\r\ninterface refGen{skillBook:IRefskillBook}\r\ntype IRefskillBook_id=1|10|100|101|102|103|104|105|106|107|108|109|11|110|111|112|113|114|115|116|117|118|119|12|120|121|122|123|124|125|126|127|128|129|13|130|131|132|133|134|135|136|137|138|139|14|140|141|142|143|144|145|146|147|148|149|15|150|151|152|153|154|155|156|157|158|159|16|160|161|162|163|164|165|166|167|168|169|17|170|171|172|173|174|175|176|177|178|179|18|180|181|182|183|184|185|186|187|188|189|19|190|191|192|193|194|195|196|197|198|199|2|20|200|201|202|203|204|205|206|207|208|209|21|210|211|212|213|214|215|216|217|218|219|22|220|221|222|223|224|225|226|227|228|229|23|230|231|232|233|234|235|236|237|238|239|24|240|241|242|243|244|245|246|247|248|249|25|250|251|252|253|254|255|256|257|258|259|26|260|261|262|263|264|265|266|267|268|269|27|270|271|272|273|274|275|276|277|278|279|28|280|281|282|283|284|285|286|287|288|289|29|290|291|292|293|294|295|296|297|298|299|3|30|300|301|302|303|304|305|306|307|308|309|31|310|311|312|313|314|315|316|317|318|319|32|320|321|322|323|324|325|326|327|328|329|33|330|331|332|333|334|335|336|337|338|339|34|340|341|342|343|344|345|346|347|348|349|35|350|351|352|353|354|355|356|357|358|359|36|360|361|362|363|364|365|366|367|368|369|37|370|371|372|373|374|375|376|377|378|379|38|380|381|382|383|384|385|386|387|388|389|39|390|4|40|41|42|43|44|45|46|47|48|49|5|50|51|52|53|54|55|56|57|58|59|6|60|61|62|63|64|65|66|67|68|69|7|70|71|72|73|74|75|76|77|78|79|8|80|81|82|83|84|85|86|87|88|89|9|90|91|92|93|94|95|96|97|98|99\r\ntype IRefskillBook_level=0|1|10|11|12|13|14|15|16|17|18|19|2|20|21|22|23|24|25|3|4|5|6|7|8|9\r\n\r\ninterface IRefskillBookLevel extends IRefBase\r\n{\r\ndenseRoll:number\r\ngold:number\r\nitemNumber:number\r\nlevel:IRefskillBookLevel_level\r\n}\r\ninterface refGen{skillBookLevel:IRefskillBookLevel}\r\ntype IRefskillBookLevel_level=1|10|11|12|13|14|15|16|17|18|19|2|20|21|22|23|24|25|3|4|5|6|7|8|9\r\n\r\ninterface IRefskillBuff extends IRefBase\r\n{\r\nkey:IRefskillBuff_key\r\noverlying:any[]\r\nparam:{ [key in string]: any }\r\nposition:number\r\n\r\nmodel?:string\r\n}\r\ninterface refGen{skillBuff:IRefskillBuff}\r\ntype IRefskillBuff_key='亡者守护'|'免疫冰系伤害'|'免疫火系伤害'|'免疫物理系伤害'|'免疫电系伤害'|'免疫风系伤害'|'再生'|'再生2'|'冻伤'|'冻结'|'冻结抵抗'|'减速'|'击退'|'分裂'|'助燃I'|'助燃II'|'反应护盾'|'受到冰系伤害额外增加'|'受到弹道类技能伤害降低'|'受到火系伤害额外增加'|'受到物理系伤害额外增加'|'受到电系伤害额外增加'|'受到风系伤害额外增加'|'召唤'|'召唤2'|'回春'|'回春2'|'坚毅'|'坚毅I'|'坚毅II'|'坚盾'|'坚盾2'|'坚盾II'|'坚盾III'|'应激治疗'|'引燃'|'急速'|'急速2'|'急速冷却'|'护盾'|'拦截弹道类技能'|'晕眩抗性'|'暴走'|'暴走2'|'死亡回响'|'溅射'|'燃烧'|'燃烧增加伤害'|'燃烧抵抗'|'牵引'|'牵引抵抗'|'牵引抵抗II'|'牵引抵抗III'|'狂飙'|'眩晕'|'眩晕2'|'禁止分裂'|'群体治愈'|'自爆'|'规避'|'适应装甲'|'重伤'|'重生'|'顽强'|'麻痹'|'麻痹抵抗'\r\n\r\ninterface IRefskillEffect extends IRefBase\r\n{\r\neffects:any[]\r\nkey:IRefskillEffect_key\r\n}\r\ninterface refGen{skillEffect:IRefskillEffect}\r\ntype IRefskillEffect_key='万剑诀_折射'|'万剑诀_次级'|'万剑诀_爆炸'|'万剑诀_默认'|'万剑诀齐射_默认'|'剑气斩_扫射'|'剑气斩_折射'|'剑气斩_无强化'|'剑气斩_默认'|'天雷网_技能效果'|'天雷网_爆炸'|'天雷网_默认'|'寒冰突刺_冷冻场'|'寒冰突刺_默认'|'寒冰箭_冰箭风暴'|'寒冰箭_小寒冰箭'|'寒冰箭_默认'|'巨石突刺_余烬'|'巨石突刺_眩晕'|'巨石突刺_默认'|'御剑术_御剑风暴'|'御剑术_无强化'|'御剑术_次级'|'御剑术_次级爆炸'|'御剑术_火焰御剑'|'御剑术_爆炸'|'御剑术_默认'|'惊雷咒_天雷场'|'惊雷咒_小电球'|'惊雷咒_无强化'|'惊雷咒_无强化溅射伤害'|'惊雷咒_溅射伤害'|'惊雷咒_追加AOE'|'惊雷咒_雷暴'|'惊雷咒_默认'|'旋风术_小旋风'|'旋风术_小风暴术'|'旋风术_真空'|'旋风术_眩晕'|'旋风术_闪电旋风'|'旋风术_默认'|'滚石_无强化'|'滚石_燃烧滚石'|'滚石_爆炸'|'滚石_默认'|'火焰弹_地面燃烧'|'火焰弹_弹雨'|'火焰弹_怪物死亡爆炸'|'火焰弹_火花'|'火焰弹_爆炸'|'火焰弹_眩晕'|'火焰弹_默认'|'灵兽_寒冰突刺'|'灵兽_寒冰突刺冷冻场'|'灵兽_御剑术'|'灵兽_惊雷咒'|'灵兽_惊雷咒溅射伤害'|'灵兽_毒云'|'灵兽_落石术'|'灵兽_风暴术'|'落石术_余烬'|'落石术_无强化'|'落石术_眩晕'|'落石术_落石雨'|'落石术_默认'|'连环闪电_天雷场'|'连环闪电_无强化'|'连环闪电_爆炸'|'连环闪电_默认'|'闪电球 _无强化惊雷咒'|'闪电球_小型黑洞'|'闪电球_小闪电球'|'闪电球_爆炸'|'闪电球_飞行后小闪电球'|'闪电球_黑洞'|'闪电球_默认'|'阵法_毒墙'|'阵法_流沙阵法'|'阵法_渗毒石狮子'|'阵法_石狮子'|'阵法_荆棘'|'阵法_长剑破空'|'阵法_风刃锯齿'|'风暴术_大型风暴术'|'风暴术_默认'\r\n\r\ninterface IRefskillEffectDamageType extends IRefBase\r\n{\r\nkey:IRefskillEffectDamageType_key\r\nname:IRefskillEffectDamageType_name\r\nvalue:number\r\n}\r\ninterface refGen{skillEffectDamageType:IRefskillEffectDamageType}\r\ntype IRefskillEffectDamageType_key='Aoshu'|'Bingxi'|'Dandao'|'Dianxi'|'Fengxi'|'Huoxi'|'Wuli'\r\ntype IRefskillEffectDamageType_name='冰系'|'奥术'|'弹道'|'火系'|'物理地面'|'电系'|'风系'\r\n\r\ninterface IRefskillEffectShow extends IRefBase\r\n{\r\neffects:any[]\r\nkey:IRefskillEffectShow_key\r\n}\r\ninterface refGen{skillEffectShow:IRefskillEffectShow}\r\ntype IRefskillEffectShow_key='万剑诀_伤害次数'|'万剑诀_伤害间隔'|'万剑诀_冷却'|'剑气斩_伤害次数'|'剑气斩_伤害间隔'|'剑气斩_冷却'|'剑气斩_范围伤害'|'天雷网_伤害次数'|'天雷网_伤害间隔'|'天雷网_冷却'|'寒冰突刺_伤害范围'|'寒冰突刺_伤害间隔'|'寒冰突刺_冷却'|'寒冰突刺_持续伤害'|'寒冰箭_冷却'|'寒冰箭_穿透'|'巨石突刺_伤害范围'|'巨石突刺_冷却'|'御剑术_冷却'|'御剑术_穿透'|'惊雷咒_伤害范围'|'惊雷咒_冷却'|'惊雷咒_范围伤害'|'旋风术_冷却'|'旋风术_穿透'|'滚石_伤害范围'|'滚石_冷却'|'火焰弹_伤害范围'|'火焰弹_冷却'|'火焰弹_范围伤害'|'落石术_伤害范围'|'落石术_冷却'|'连环闪电_冷却'|'连环闪电_弹射'|'闪电球_伤害间隔'|'闪电球_冷却'|'风暴术_伤害范围'|'风暴术_伤害间隔'|'风暴术_冷却'\r\n\r\ninterface IRefskillLevelUP extends IRefBase\r\n{\r\nauto:number\r\ncardType:number\r\ndesc:string\r\nicon:string\r\nid:IRefskillLevelUP_id\r\nmodify:any[]\r\nname:IRefskillLevelUP_name\r\nneedKaPai:any[]\r\nskillId:number\r\ntimes:number\r\n}\r\ninterface refGen{skillLevelUP:IRefskillLevelUP}\r\ntype IRefskillLevelUP_id=100000|100001|100002|100003|100004|100005|100006|100007|100008|100009|100010|100011|100012|100013|100014|101000|101001|101002|101003|101004|101005|101006|101007|101008|101009|101010|101011|102000|102001|102002|102003|102004|102005|102006|102007|102008|102009|102010|102011|102012|102013|103000|103001|103002|103003|103004|103005|103006|103007|103008|103009|103010|103011|103012|103013|104000|104001|104002|104003|104004|104005|104006|104007|104008|104009|104010|104011|104012|105000|105001|105002|105003|105004|105005|105006|105007|105008|105009|105010|106000|106001|106002|106003|106004|106005|106006|106007|106008|107000|107001|107002|107003|107004|107005|107006|107007|107008|107009|107010|108000|108001|108002|108003|108004|108005|108006|108007|108008|109000|109001|109002|109003|109004|109005|109006|109007|109008|109009|110000|110001|110002|110003|110004|110005|110006|110007|110008|111000|111001|111002|111003|111004|111005|111006|111007|111008|111009|112000|112001|112002|112003|112004|112005|112006|112007|112008|113000|113001|113002|113003|113004|113005|113006|113007|113008|113009|113010|113011|114000|114001|114002|114003|114004|114005|114006|114007|114008|114009|114010\r\ntype IRefskillLevelUP_name='万剑归宗'|'修炼万剑诀'|'修炼剑气斩'|'修炼天雷网'|'修炼寒冰突刺'|'修炼寒冰箭'|'修炼巨石突刺'|'修炼御剑术'|'修炼惊雷咒'|'修炼旋风术'|'修炼毒气弹'|'修炼泰山压顶'|'修炼石狮子'|'修炼连环闪电'|'修炼闪电球'|'修炼风暴术'|'凝滞剑气'|'分裂箭增强'|'分裂箭穿透'|'刺骨冰寒'|'剑气乱射'|'剑气修行'|'剑气冲击波'|'剑气增强I'|'剑气威力增强'|'剑气威力增强II'|'剑气威力增强III'|'剑气范围增强'|'剑气重创'|'剑诀修行'|'剑诀增强'|'剑诀折射'|'剑诀爆破'|'剑诀重击'|'双发寒冰箭'|'双发惊雷咒'|'双发毒气弹'|'双发石狮子'|'双发飞剑'|'双重风暴'|'备用石狮'|'多棱寒冰'|'大型石狮'|'大型风暴聚集'|'天雷分裂'|'天雷感应'|'天雷滚滚'|'天雷滚滚II'|'天雷轰顶'|'奔跑吧，狮子！'|'寒冰伤害增强'|'寒冰冲击'|'寒冰分裂箭'|'寒冰威力增强'|'寒冰法术精通'|'寒冰碎裂'|'寒冰碎裂增强'|'寒冰箭连发'|'寒冰蔓延'|'小型爆炸飞剑'|'小型风暴聚集'|'小型飞剑增强'|'小电球增强'|'山体增长'|'山体重击'|'巨型石狮子'|'御剑术精通'|'急冻领域'|'急速坠落'|'惊雷咒威力增强'|'扫射剑气'|'旋风乱流'|'旋风乱流II'|'旋风凝滞'|'旋风术增强'|'旋风术连发'|'旋风术连发II'|'旋风真空'|'旋风突袭'|'极寒冰心'|'极寒冰箭'|'极度深寒'|'毒气侵袭'|'毒气冲击'|'毒气弹精通'|'毒气渗透'|'毒气溅射'|'毒气爆发'|'毒气爆炸增强'|'毒气蔓延'|'毒气轰炸'|'泰山压顶伤害增强'|'泰山坠落'|'泰山连击'|'渗毒岩石'|'渗毒石狮'|'渗毒飞剑'|'爆炸威力增加'|'爆炸飞剑'|'狂飙石狮'|'球体扩张'|'电击传感'|'电击回路'|'电击流转'|'电球吸附'|'电网击退'|'电网击退II'|'电网双发'|'电网增强'|'电网增长'|'疯狂毒气弹'|'真空崩塌'|'石狮冲击'|'石狮冲撞'|'石狮击退'|'石狮威力增强'|'石狮碾压'|'稳定雷池'|'突刺伤害增强'|'突刺冷却'|'突刺眩晕'|'突刺范围增长'|'突刺连击'|'精准打击'|'群山压顶'|'致命剑诀'|'虚空结界'|'蚀骨冰寒'|'蚀骨冰箭'|'裂地突刺'|'贯穿寒冰'|'贯穿旋风'|'贯穿飞剑'|'超强剑气'|'连环冰刺'|'连环电网'|'连环突刺'|'重型突刺'|'重装寒冰箭'|'闪电伤害增强'|'闪电延续'|'闪电球伤害增强'|'闪电球分裂'|'闪电球延续'|'闪电粒子爆破'|'闪电风暴'|'阵法修复'|'雷暴'|'雷暴威力增强'|'雷池'|'雷法精通'|'雷电天罚'|'雷系法术增强'|'风暴吸附'|'风暴增长'|'风暴术伤害增强'|'风暴术延续'|'风暴突袭'|'飞剑分裂'|'飞剑四射'|'飞剑威力增强'|'飞剑威力增强II'|'飞剑齐射'|'高压电击'|'高压电网'|'高压雷池'|'高速闪电球'|'麻痹天雷'|'麻痹闪电球'\r\n\r\ninterface IRefskillType extends IRefBase\r\n{\r\naoeRate:number\r\natkRate:number\r\nattr:any[]\r\nattrSkillName:IRefskillType_attrSkillName\r\ncdType:number\r\nconsumeId:number\r\nicon:string\r\nid:IRefskillType_id\r\nkeepDamageRate:number\r\nkey:IRefskillType_key\r\nname:IRefskillType_name\r\nrangeType:number\r\nroleLevel:number\r\n\r\ndesc?:string\r\n}\r\ninterface refGen{skillType:IRefskillType}\r\ntype IRefskillType_attrSkillName='万剑诀'|'全局'|'剑气斩'|'天雷网'|'寒冰突刺'|'寒冰箭'|'巨石突刺'|'御剑术'|'惊雷咒'|'旋风术'|'滚石'|'火焰弹'|'落石术'|'连环闪电'|'闪电球'|'风暴术'\r\ntype IRefskillType_id=0|30001|30011|30021|30031|30041|30051|30061|30071|30081|30091|30101|30111|30121|30131|30141\r\ntype IRefskillType_key='FengBaoShu'|'Global'|'GunShi'|'HanBingJian'|'HanBingTuCi'|'HuoYanDan'|'JianQiZhan'|'JingLeiZhou'|'JuShiTuCi'|'LianHuanShanDian'|'LuoShiShu'|'ShanDianQiu'|'TianLeiWang'|'WanJianJue'|'XuanFengShu'|'YuJianShu'\r\ntype IRefskillType_name='万剑诀'|'全局'|'剑气斩'|'天雷网'|'寒冰突刺'|'寒冰箭'|'巨石突刺'|'御剑术'|'惊雷咒'|'旋风术'|'毒气弹'|'泰山压顶'|'石狮子'|'连环闪电'|'闪电球'|'风暴术'\r\n\r\ninterface IRefskin extends IRefBase\r\n{\r\nattrAddition:any[]\r\nbindHead:number\r\ndesc:string\r\ndesc1:string\r\ndesc2:string\r\nicon:string\r\niconHalf:string\r\niconKaPai:string\r\nmodel:string\r\nname:IRefskin_name\r\nquality:IRefskin_quality\r\nresId:IRefskin_resId\r\nsplitReward:any[]\r\ntype:IRefskin_type\r\nuseAttrAddition:any[]\r\n}\r\ninterface refGen{skin:IRefskin}\r\ntype IRefskin_name='剑仙'|'灵狐•玄珠'|'灵狐•红莲'|'灵狐•青叶'|'鲛女'\r\ntype IRefskin_quality=3|4|5\r\ntype IRefskin_resId=80001|80002|80003|80004|80005\r\ntype IRefskin_type=9\r\n\r\ninterface IRefspiritAnimal extends IRefBase\r\n{\r\nanimalId:number\r\nattrList:any[]\r\nresId:IRefspiritAnimal_resId\r\nstep:number\r\nstepUpCostCamp:any[]\r\nstepUpCostSame:any[]\r\n}\r\ninterface refGen{spiritAnimal:IRefspiritAnimal}\r\ntype IRefspiritAnimal_resId=90101|90102|90103|90104|90105|90106|90107|90108|90109|90110|90111|90112|90113|90114|90115|90201|90202|90203|90204|90205|90206|90207|90208|90209|90210|90211|90212|90213|90214|90215|90301|90302|90303|90304|90305|90306|90307|90308|90309|90310|90311|90312|90313|90314|90315|90401|90402|90403|90404|90405|90406|90407|90408|90409|90410|90411|90412|90413|90414|90415|90501|90502|90503|90504|90505|90506|90507|90508|90509|90510|90511|90512|90513|90514|90515|90601|90602|90603|90604|90605|90606|90607|90608|90609|90610|90611|90612|90613|90614|90615\r\n\r\ninterface IRefspiritAnimalBox extends IRefBase\r\n{\r\nbigBaodi:number\r\ndesc:string\r\nfirstDrawResId:number\r\nfreeTimes:number\r\nid:IRefspiritAnimalBox_id\r\nlimit:number\r\nnormalDrawResId:number\r\nnormalOneDrawCostNum:number\r\nnormalTenDrawCostNum:number\r\nprocessBox:{ [key in string]: any }\r\nrewards:any[]\r\nsmallBaodi:number\r\nticketDrawCostNum:number\r\nticketDrawResId:number\r\n}\r\ninterface refGen{spiritAnimalBox:IRefspiritAnimalBox}\r\ntype IRefspiritAnimalBox_id=1\r\n\r\ninterface IRefspiritAnimalLevel extends IRefBase\r\n{\r\nlevel:IRefspiritAnimalLevel_level\r\nlevelUpCost:any[]\r\nspiritAnimalAtk:number\r\n}\r\ninterface refGen{spiritAnimalLevel:IRefspiritAnimalLevel}\r\ntype IRefspiritAnimalLevel_level=1|10|100|11|12|13|14|15|16|17|18|19|2|20|21|22|23|24|25|26|27|28|29|3|30|31|32|33|34|35|36|37|38|39|4|40|41|42|43|44|45|46|47|48|49|5|50|51|52|53|54|55|56|57|58|59|6|60|61|62|63|64|65|66|67|68|69|7|70|71|72|73|74|75|76|77|78|79|8|80|81|82|83|84|85|86|87|88|89|9|90|91|92|93|94|95|96|97|98|99\r\n\r\ninterface IRefspiritAnimalSkillType extends IRefBase\r\n{\r\ndesc:string\r\nid:IRefspiritAnimalSkillType_id\r\nkey:IRefspiritAnimalSkillType_key\r\nname:IRefspiritAnimalSkillType_name\r\n}\r\ninterface refGen{spiritAnimalSkillType:IRefspiritAnimalSkillType}\r\ntype IRefspiritAnimalSkillType_id=1|2|3|4|5|6\r\ntype IRefspiritAnimalSkillType_key='LS_Skill_Bing'|'LS_Skill_Du'|'LS_Skill_Feng'|'LS_Skill_Jian'|'LS_Skill_Lei'|'LS_Skill_Tu'\r\ntype IRefspiritAnimalSkillType_name='灵兽1'|'灵兽2'|'灵兽3'|'灵兽4'|'灵兽5'|'灵兽6'\r\n\r\ninterface IRefspiritAnimalStep extends IRefBase\r\n{\r\ndesc:string\r\nquality:IRefspiritAnimalStep_quality\r\nroleAtk:number\r\nspiritAnimalAtk:number\r\nstar:IRefspiritAnimalStep_star\r\nstep:number\r\nwallAddHp:number\r\n}\r\ninterface refGen{spiritAnimalStep:IRefspiritAnimalStep}\r\ntype IRefspiritAnimalStep_quality=2|3|4|5|6\r\ntype IRefspiritAnimalStep_star=0|1|2\r\n\r\ninterface IRefspiritAnimalType extends IRefBase\r\n{\r\nanimalId:number\r\natkRange:number\r\natkSpacing:number\r\nattrSkillName:IRefspiritAnimalType_attrSkillName\r\ncamp:number\r\ndesc:string\r\nicon:string\r\niconKaPai:string\r\niconSkill:string\r\nkey:IRefspiritAnimalType_key\r\nmodel:string\r\nname:IRefspiritAnimalType_name\r\nshowSize:number\r\nskill:{ [key in string]: any }\r\nskillId:number\r\nspd:number\r\n}\r\ninterface refGen{spiritAnimalType:IRefspiritAnimalType}\r\ntype IRefspiritAnimalType_attrSkillName='寒冰突刺'|'御剑术'|'惊雷咒'|'毒云'|'落石术'|'风暴术'\r\ntype IRefspiritAnimalType_key='LingShouBiongDong'|'LingShouDuYun'|'LingShouFeiJian'|'LingShouFengBao'|'LingShouLeiJi'|'LingShouYunShi'\r\ntype IRefspiritAnimalType_name='九尾狐'|'千羽灵鹤'|'司雪'|'貔貅'|'青龙'|'鸩'\r\n\r\ninterface IRefsystemDesc extends IRefBase\r\n{\r\ndesc:string\r\nkey:IRefsystemDesc_key\r\nname:IRefsystemDesc_name\r\n}\r\ninterface refGen{systemDesc:IRefsystemDesc}\r\ntype IRefsystemDesc_key='gongfang'|'gongfang2'|'gongfang3'|'lingshoushuoming'|'mijing'|'suoyaota'|'zhenfa'|'zhuliyaoqing'\r\ntype IRefsystemDesc_name='助力邀请'|'图纸绘制'|'宝石锻造'|'灵宠说明'|'秘境'|'铜钱铸造'|'锁妖塔'|'阵法'\r\n\r\ninterface IReftaskType extends IRefBase\r\n{\r\nkey:IReftaskType_key\r\nname:IReftaskType_name\r\ntype:IReftaskType_type\r\n}\r\ninterface refGen{taskType:IReftaskType}\r\ntype IReftaskType_key='task1'|'task11'|'task12'|'task13'|'task14'|'task15'|'task16'|'task17'|'task18'|'task19'|'task2'|'task3'|'task4'|'task5'|'task6'|'task7'|'task8'|'task9'\r\ntype IReftaskType_name='11=装备总等级达到{count}级'|'12=技能中等级达到{count}级'|'13=累积充值达到{count}灵玉'|'14累积消耗钻石数'|'15累积消耗灵玉数'|'1=观看广告{count}次'|'2=消耗体力{count}点'|'3=合成宝石{count}次'|'4=击杀野怪{count}只'|'5=挑战深渊挑战{count}次'|'6=今日登录'|'7=累积充值达到{count}元'|'8=通关指定关卡{count}'|'9=人物达到{count}级'|'挑战N次秘境'|'挑战N次锁妖塔'|'章节关卡内选择技能N次'|'累积登录N天'\r\ntype IReftaskType_type=1|11|12|13|14|15|16|17|18|19|2|3|4|5|6|7|8|9\r\n\r\ninterface IRefvaluableMonthCardInfo extends IRefBase\r\n{\r\nbuyReward:any[]\r\ndayReward:any[]\r\nprivilege:any[]\r\nresId:IRefvaluableMonthCardInfo_resId\r\n}\r\ninterface refGen{valuableMonthCardInfo:IRefvaluableMonthCardInfo}\r\ntype IRefvaluableMonthCardInfo_resId=1\r\n\r\ninterface IRefwall extends IRefBase\r\n{\r\nhp:number\r\nitemId:number\r\nlevel:IRefwall_level\r\nnumber:number\r\n}\r\ninterface refGen{wall:IRefwall}\r\ntype IRefwall_level=0|1|10|11|12|13|14|15|16|17|18|19|2|20|3|4|5|6|7|8|9\r\n\r\ninterface IRefwallSkillBook extends IRefBase\r\n{\r\ndisplay:number\r\nid:IRefwallSkillBook_id\r\nlevel:IRefwallSkillBook_level\r\nlevelModify:any[]\r\nmodifyDesc:string\r\nskillId:number\r\ntlShowAttr:any[]\r\n\r\nskillCd?:string\r\n}\r\ninterface refGen{wallSkillBook:IRefwallSkillBook}\r\ntype IRefwallSkillBook_id=1|101|102|103|104|105|106|107|108|2|201|202|203|204|205|206|207|208|3|301|302|303|304|305|306|307|308|4|401|402|403|404|405|406|407|408|5|501|502|503|504|505|506|507|508|6|601|602|603|604|605|606|607|608|7|8\r\ntype IRefwallSkillBook_level=1|2|3|4|5|6|7|8\r\n\r\ninterface IRefwallSkillLevel extends IRefBase\r\n{\r\nitemId:number\r\nlevel:IRefwallSkillLevel_level\r\nnumber:number\r\n}\r\ninterface refGen{wallSkillLevel:IRefwallSkillLevel}\r\ntype IRefwallSkillLevel_level=1|2|3|4|5|6|7|8\r\n\r\ninterface IRefwallSkillType extends IRefBase\r\n{\r\naoeRate:number\r\natkRate:number\r\nattr:any[]\r\nattrSkillName:IRefwallSkillType_attrSkillName\r\ncdType:number\r\ndesc:string\r\nicon:string\r\nid:IRefwallSkillType_id\r\nisAttrEffect:number\r\nkeepDamageRate:number\r\nkey:IRefwallSkillType_key\r\nname:IRefwallSkillType_name\r\nrangeType:number\r\nwallLevel:number\r\n\r\nuseRes?:any[]\r\n}\r\ninterface refGen{wallSkillType:IRefwallSkillType}\r\ntype IRefwallSkillType_attrSkillName='伤害降低'|'反伤'|'毒墙'|'流沙阵法'|'长剑破空'|'阵法石狮子'|'风刃锯齿'\r\ntype IRefwallSkillType_id=210001|210101|210201|210301|210401|210501|210601\r\ntype IRefwallSkillType_key='ChangJianPoKong'|'DuQiZaoZe'|'FengRenJuChi'|'LiuShaZhenFa'|'QinDuShiShiZi'|'YuanChengFanShang'|'ZhenFaMianShang'\r\ntype IRefwallSkillType_name='伤害减免'|'伤害反弹'|'召唤石狮'|'毒障荆棘'|'流沙阵法'|'长剑破空'|'风刃锯齿'\r\n\r\ninterface IRefwallType extends IRefBase\r\n{\r\nbrokenModel:string\r\ngold:number\r\nmodel:string\r\nname:IRefwallType_name\r\nopenModel:string\r\nskillIds:any[]\r\ntype:IRefwallType_type\r\nwallLevel:number\r\n}\r\ninterface refGen{wallType:IRefwallType}\r\ntype IRefwallType_name='阵法'\r\ntype IRefwallType_type=1\r\n\r\ninterface IRefworkShop extends IRefBase\r\n{\r\nhelp:string\r\nid:IRefworkShop_id\r\nitemId:number\r\nname:IRefworkShop_name\r\nnumber:number\r\nopenBarrier:number\r\n}\r\ninterface refGen{workShop:IRefworkShop}\r\ntype IRefworkShop_id=1|2|3\r\ntype IRefworkShop_name='宝石工坊'|'装备工坊'|'铜钱工坊'\r\n\r\ninterface IRefworkShopLevel extends IRefBase\r\n{\r\nconsumeGem:number\r\nlevel:IRefworkShopLevel_level\r\nnumber:number\r\nopenBarriers:any[]\r\nproduceTime:number\r\nupLevelTime:number\r\n}\r\ninterface refGen{workShopLevel:IRefworkShopLevel}\r\ntype IRefworkShopLevel_level=1|10|11|12|13|14|15|2|3|4|5|6|7|8|9\r\n\r\ntype TRefName=\"activity.json\"|\"activityOpen.json\"|\"activityTab.json\"|\"ad.json\"|\"apprentice.json\"|\"apprenticeLevel.json\"|\"audioConfig.json\"|\"battleModel.json\"|\"battleTokenConfig.json\"|\"battleTokenPlan.json\"|\"battleTokenReward.json\"|\"bigNum.json\"|\"buySta.json\"|\"chapterAttrAddition.json\"|\"dayChallengeActTask.json\"|\"equip.json\"|\"equipConsume.json\"|\"firstRechargeShop.json\"|\"fundPlan.json\"|\"fundReward.json\"|\"gameCircleConfig.json\"|\"gameConfig.json\"|\"gem.json\"|\"gemAttribute.json\"|\"gemAttrModifier.json\"|\"gemBox.json\"|\"gemQuality.json\"|\"gemShop.json\"|\"giftShop.json\"|\"goldShop.json\"|\"guanka.json\"|\"guanka1.json\"|\"guankaChapter.json\"|\"guankaGift.json\"|\"guankaReward.json\"|\"guankaReward1.json\"|\"guankaReward2.json\"|\"guankaSkillLevelUp.json\"|\"guankaStar.json\"|\"guideAll.json\"|\"head.json\"|\"icon.json\"|\"idConfig.json\"|\"item.json\"|\"itemBox.json\"|\"itemBoxPro.json\"|\"itemType.json\"|\"level.json\"|\"limitTimeCard.json\"|\"lockMonsterLevel.json\"|\"lockMonsterRefreshCost.json\"|\"mapTerrian.json\"|\"mL.json\"|\"modelRef.json\"|\"monster.json\"|\"monsterSkillType.json\"|\"monthCardActPlan.json\"|\"open.json\"|\"payChannel.json\"|\"privilegeMonthCardInfo.json\"|\"rechargeContinuityGift.json\"|\"rechargeGiftActPlan.json\"|\"resourceShop.json\"|\"rewardClientPool.json\"|\"rewardPool.json\"|\"roleProperty.json\"|\"secretRealmAttr.json\"|\"secretRealmLevel.json\"|\"secretRealmRefreshCost.json\"|\"serverRank.json\"|\"sevenDayPoint.json\"|\"sevenDayShop.json\"|\"sevenDayTask.json\"|\"signinTask.json\"|\"skillAdditionBuff.json\"|\"skillAttr.json\"|\"skillAttrModifier.json\"|\"skillBook.json\"|\"skillBookLevel.json\"|\"skillBuff.json\"|\"skillEffect.json\"|\"skillEffectDamageType.json\"|\"skillEffectShow.json\"|\"skillLevelUP.json\"|\"skillType.json\"|\"skin.json\"|\"spiritAnimal.json\"|\"spiritAnimalBox.json\"|\"spiritAnimalLevel.json\"|\"spiritAnimalSkillType.json\"|\"spiritAnimalStep.json\"|\"spiritAnimalType.json\"|\"systemDesc.json\"|\"taskType.json\"|\"valuableMonthCardInfo.json\"|\"wall.json\"|\"wallSkillBook.json\"|\"wallSkillLevel.json\"|\"wallSkillType.json\"|\"wallType.json\"|\"workShop.json\"|\"workShopLevel.json\"\r\n\r\ntype TRefName2=\"activity\"|\"activityOpen\"|\"activityTab\"|\"ad\"|\"apprentice\"|\"apprenticeLevel\"|\"audioConfig\"|\"battleModel\"|\"battleTokenConfig\"|\"battleTokenPlan\"|\"battleTokenReward\"|\"bigNum\"|\"buySta\"|\"chapterAttrAddition\"|\"dayChallengeActTask\"|\"equip\"|\"equipConsume\"|\"firstRechargeShop\"|\"fundPlan\"|\"fundReward\"|\"gameCircleConfig\"|\"gameConfig\"|\"gem\"|\"gemAttribute\"|\"gemAttrModifier\"|\"gemBox\"|\"gemQuality\"|\"gemShop\"|\"giftShop\"|\"goldShop\"|\"guanka\"|\"guanka1\"|\"guankaChapter\"|\"guankaGift\"|\"guankaReward\"|\"guankaReward1\"|\"guankaReward2\"|\"guankaSkillLevelUp\"|\"guankaStar\"|\"guideAll\"|\"head\"|\"icon\"|\"idConfig\"|\"item\"|\"itemBox\"|\"itemBoxPro\"|\"itemType\"|\"level\"|\"limitTimeCard\"|\"lockMonsterLevel\"|\"lockMonsterRefreshCost\"|\"mapTerrian\"|\"mL\"|\"modelRef\"|\"monster\"|\"monsterSkillType\"|\"monthCardActPlan\"|\"open\"|\"payChannel\"|\"privilegeMonthCardInfo\"|\"rechargeContinuityGift\"|\"rechargeGiftActPlan\"|\"resourceShop\"|\"rewardClientPool\"|\"rewardPool\"|\"roleProperty\"|\"secretRealmAttr\"|\"secretRealmLevel\"|\"secretRealmRefreshCost\"|\"serverRank\"|\"sevenDayPoint\"|\"sevenDayShop\"|\"sevenDayTask\"|\"signinTask\"|\"skillAdditionBuff\"|\"skillAttr\"|\"skillAttrModifier\"|\"skillBook\"|\"skillBookLevel\"|\"skillBuff\"|\"skillEffect\"|\"skillEffectDamageType\"|\"skillEffectShow\"|\"skillLevelUP\"|\"skillType\"|\"skin\"|\"spiritAnimal\"|\"spiritAnimalBox\"|\"spiritAnimalLevel\"|\"spiritAnimalSkillType\"|\"spiritAnimalStep\"|\"spiritAnimalType\"|\"systemDesc\"|\"taskType\"|\"valuableMonthCardInfo\"|\"wall\"|\"wallSkillBook\"|\"wallSkillLevel\"|\"wallSkillType\"|\"wallType\"|\"workShop\"|\"workShopLevel\""]], 0, 0, [], [], []]