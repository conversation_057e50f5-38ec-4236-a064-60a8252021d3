[1, 0, 0, [["cc.TextAsset", ["_name", "text"], 1]], [[0, 0, 1, 3]], [[0, "Cmd.d", "\r\ndeclare interface CmdGuide {\r\n\r\n}\r\n\r\ndeclare interface CmdGuide_Key {\r\nid: number\r\n}\r\n\r\ndeclare interface CmdGuideDetail {\r\nresId: number\r\ntype: number\r\nextend?: string\r\n}\r\n\r\ndeclare interface CmdGem {\r\nid: number\r\nresId: number\r\nquality: number\r\nnum: number\r\nattribute: number\r\nlock: number\r\nnewAttribute?: number\r\n}\r\n\r\ndeclare interface CmdGem_Key {\r\nid: number\r\n}\r\n\r\ndeclare interface CmdGemPreview {\r\nid: number\r\nnum: number\r\n}\r\n\r\ndeclare interface CmdSkin {\r\nresId: number\r\nused?: boolean\r\n}\r\n\r\ndeclare interface CmdSkin_Key {\r\nresId: number\r\n}\r\n\r\ndeclare interface CmdFriendCache {\r\nroleId: number\r\nfriendDetail: CmdFriendsShowDetail\r\n}\r\n\r\ndeclare interface CmdFriendCache_Key {\r\nroleId: number\r\n}\r\n\r\ndeclare interface CmdApplyFriendCache {\r\nroleId: number\r\nfriendDetail: CmdFriendsShowDetail\r\n}\r\n\r\ndeclare interface CmdApplyFriendCache_Key {\r\nroleId: number\r\n}\r\n\r\ndeclare interface CmdBlackFriendCache {\r\nroleId: number\r\nfriendDetail: CmdFriendsShowDetail\r\n}\r\n\r\ndeclare interface CmdBlackFriendCache_Key {\r\nroleId: number\r\n}\r\n\r\ndeclare interface CmdFriendsShowDetail {\r\nroleId: number\r\nroleImg: number\r\nroleName: string\r\npower: number\r\nheadFrame: number\r\nonline?: boolean\r\npopularity?: number\r\nlastOfflineTime?: number\r\ntodaySend?: boolean\r\ntodayReceive?: boolean\r\ntodayTake?: boolean\r\nlevel?: number\r\nheroId?: number\r\ntodayLike?: boolean\r\napply?: boolean\r\n}\r\n\r\ndeclare interface CmdFriendsWearEquip {\r\nresId: number\r\nquality: number\r\nphase: number\r\nlevel: number\r\n}\r\n\r\ndeclare interface CmdFund {\r\n\r\n}\r\n\r\ndeclare interface CmdFund_Key {\r\nid: number\r\n}\r\n\r\ndeclare interface CmdFundDetail {\r\nresId: number\r\nbuyState: number\r\ncmdFundRewardState?: CmdFundRewardState[]\r\n}\r\n\r\ndeclare interface CmdFundRewardState {\r\nresId: number\r\nstate: number\r\n}\r\n\r\ndeclare interface CmdFuBen {\r\nresId: number\r\ndetail: CmdFuBenDetail\r\n}\r\n\r\ndeclare interface CmdFuBen_Key {\r\nresId: number\r\n}\r\n\r\ndeclare interface CmdFuBenDetail {\r\ncmdFuBenGoldDetail?: CmdFuBenGoldDetail\r\ncmdFuBenDailyDetail?: CmdFuBenDailyDetail\r\n}\r\n\r\ndeclare interface CmdFuBenGoldDetail {\r\nresId: number\r\ntype: number\r\nlevel: number\r\nbuyTime: number\r\nfreeTime: number\r\nenterCount: number\r\n}\r\n\r\ndeclare interface CmdFuBenDailyDetail {\r\nresId: number\r\ntype: number\r\nlevel: number\r\nbuyTime: number\r\nfreeTime: number\r\nenterCount: number\r\ncmdFuBenRewardState?: CmdFuBenRewardState[]\r\n}\r\n\r\ndeclare interface CmdFuBenRewardState {\r\nresId: number\r\nstate: number\r\n}\r\n\r\ndeclare enum CmdFuBenType {\r\nCHAPTER=1,\r\nMONEY=2,\r\nDAILY=3\r\n}\r\n\r\ndeclare interface CmdClientInfo {\r\nimei: string\r\nmac: string\r\nidfa: string\r\nmobileBrand: string\r\nmobileModel: string\r\nversionName: string\r\nversionCode: string\r\nsystemName: string\r\nsystemVersion: string\r\nnetworkType: string\r\nbigChannel: string\r\nsmallChannel: string\r\nsmallChannelPack: string\r\nappType?: string\r\nappId?: string\r\n}\r\n\r\ndeclare interface CmdTips {\r\ncontent: string\r\n}\r\n\r\ndeclare interface CmdCommonTips {\r\ncontent: CmdI18N\r\n}\r\n\r\ndeclare interface CmdI18N {\r\ntemplate?: string\r\ncmdI18NRef?: string\r\ntlParam?: CmdI18N[]\r\n}\r\n\r\ndeclare interface CmdClientAppendData {\r\ntlCmdClientAppendDataModel?: CmdClientAppendDataModel[]\r\n}\r\n\r\ndeclare interface CmdClientAppendDataModel {\r\ncmdClientAppendDataModelEnum: CmdClientAppendDataModelEnum\r\nappendData: Uint8Array\r\n}\r\n\r\ndeclare enum CmdClientAppendDataModelEnum {\r\nCmdGuideList=1\r\n}\r\n\r\ndeclare interface CmdGoods {\r\nresId: number\r\nnumber: string\r\nquality?: number\r\n}\r\n\r\ndeclare interface CmdGameExtend {\r\nclazzName: string\r\nmessage: Uint8Array\r\n}\r\n\r\ndeclare interface CmdGoodsList {\r\ntlCmdGoods?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdRecharge {\r\nid: number\r\nrewards?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdGmInfo {\r\ncmd: string\r\ndesc?: string\r\n}\r\n\r\ndeclare interface CmdOpenCache {\r\nrefId: number\r\nshow: boolean\r\n}\r\n\r\ndeclare interface CmdOpenCache_Key {\r\nrefId: number\r\n}\r\n\r\ndeclare enum CmdCommonState {\r\nUNDONE=0,\r\nFINISH=1,\r\nREWARDED=2\r\n}\r\n\r\ndeclare interface CmdGameCircle {\r\nlikeRewardState: CmdCommonState\r\n}\r\n\r\ndeclare interface CmdGameCircle_Key {\r\n\r\n}\r\n\r\ndeclare interface CmdGuideRecordReqMsg {\r\nresId: number\r\ntype: number\r\nextend?: string\r\n}\r\n\r\ndeclare interface CmdGuideRecordRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdGuideReadReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdGuideReadRspMsg {\r\ncmdGuideDetail?: CmdGuideDetail[]\r\n}\r\n\r\ndeclare interface CmdChapter {\r\nresId: number\r\nfinishState: number\r\nliveTime: number\r\ntlChapterBoxState?: CmdChapterBoxState[]\r\ngiftState: number\r\n}\r\n\r\ndeclare interface CmdChapter_Key {\r\nresId: number\r\n}\r\n\r\ndeclare interface CmdChapterBoxState {\r\nboxResId: number\r\nrewardState: CmdCommonState\r\n}\r\n\r\ndeclare interface CmdFuBenEnterReqMsg {\r\ntype: CmdFuBenType\r\nresId: number\r\n}\r\n\r\ndeclare interface CmdFuBenEnterRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdFuBenDailyGetRewardReqMsg {\r\nresId: number\r\n}\r\n\r\ndeclare interface CmdFuBenDailyGetRewardRspMsg {\r\ntlCmdGoods?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdFuBenBuyTimesReqMsg {\r\ntype: number\r\nresId: number\r\n}\r\n\r\ndeclare interface CmdFuBenBuyTimesRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdBattleLevel {\r\nlevel: number\r\nstar: number\r\nbattleTime: number\r\n}\r\n\r\ndeclare interface CmdBattleLevel_Key {\r\nlevel: number\r\n}\r\n\r\ndeclare interface CmdBattleChapter {\r\nchapterId: number\r\ntakeStar: number\r\nstar: number\r\ntlCmdBattleLevel?: CmdBattleLevel[]\r\n}\r\n\r\ndeclare interface CmdBattleChapter_Key {\r\nchapterId: number\r\n}\r\n\r\ndeclare interface CmdBattleData {\r\ntlSkillId?: number[]\r\ntlCmdBattleCard?: CmdBattleCard[]\r\ntlCmdBattleBuff?: CmdBattleBuff[]\r\ntlCmdBattleAttr?: CmdBattleAttr[]\r\nadvertNum: number\r\nrandomTimes: number\r\nbattleId: string\r\ntlId?: number[]\r\nclientData?: Uint8Array\r\nbattleType?: CmdBattleType\r\ngemRefreshSkillNum?: number\r\ntimes: number\r\ntlWallSkillId?: number[]\r\nspiritAnimalData?: CmdSpiritAnimalData\r\ncmdBattleSkill?: CmdBattleSkill\r\n}\r\n\r\ndeclare interface CmdSpiritAnimalData {\r\ntlSkillId?: number[]\r\ntlCmdBattleBuff?: CmdBattleBuff[]\r\ntlCmdBattleAttr?: CmdBattleAttr[]\r\n}\r\n\r\ndeclare interface CmdBattleSkill {\r\ntlSkillId?: number[]\r\ntlBaseSkillId?: number[]\r\ntlBossSkillId?: number[]\r\ncurRandomSkill?: number[]\r\ncurRandomSkillType?: number\r\n}\r\n\r\ndeclare interface CmdBattleCard {\r\nid: number\r\nuseNum: number\r\n}\r\n\r\ndeclare interface CmdBattleBuff {\r\nid: number\r\nrefId: number\r\n}\r\n\r\ndeclare interface CmdBattleAttr {\r\nrefId: number\r\nvalue: string\r\n}\r\n\r\ndeclare enum CmdBattleType {\r\nCHAPTER=1,\r\nLOCK_MONSTER=2,\r\nSECRET_REALM=3\r\n}\r\n\r\ndeclare interface CmdEnterBattleReqMsg {\r\nbattleType: CmdBattleType\r\nlevel?: number\r\n}\r\n\r\ndeclare interface CmdEnterBattleRspMsg {\r\ncmdBattleData: CmdBattleData\r\n}\r\n\r\ndeclare interface CmdContinueNextBattleReqMsg {\r\nbattleType: CmdBattleType\r\ncontinue?: boolean\r\n}\r\n\r\ndeclare interface CmdContinueNextBattleRspMsg {\r\ncmdBattleData?: CmdBattleData\r\n}\r\n\r\ndeclare interface CmdGemRefreshSkillReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdGemRefreshSkillRspMsg {\r\ntlId?: number[]\r\n}\r\n\r\ndeclare interface CmdBattleDataUploadReqMsg {\r\nbattleId: string\r\nclientData: Uint8Array\r\n}\r\n\r\ndeclare interface CmdBattleDataUploadRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdBattleResultReqMsg {\r\nlevel?: number\r\nstar?: number\r\nbattleTime?: number\r\nkillNumber?: number\r\nreport?: Uint8Array\r\nversion?: number\r\ncmdBattleType?: CmdBattleType\r\n}\r\n\r\ndeclare interface CmdBattleResultRspMsg {\r\ntlCmdGoods?: CmdGoods[]\r\ntipType?: number\r\n}\r\n\r\ndeclare interface CmdBattleStatisticsReqMsg {\r\nbattleId: string\r\nlevel?: number\r\ncmdBattleType?: CmdBattleType\r\nbattleTime?: number\r\nreport?: Uint8Array\r\nversion?: number\r\nroundIndex?: number\r\n}\r\n\r\ndeclare interface CmdBattleStatisticsRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdTakeStarRewardReqMsg {\r\nlevel: number\r\nstar: number\r\n}\r\n\r\ndeclare interface CmdTakeStarRewardRspMsg {\r\ntlCmdGoods?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdTakeChapterRewardReqMsg {\r\nchapter: number\r\n}\r\n\r\ndeclare interface CmdTakeChapterRewardRspMsg {\r\ntlCmdGoods?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdBattleSweepReqMsg {\r\ntype: number\r\nlevel: number\r\n}\r\n\r\ndeclare interface CmdBattleSweepRspMsg {\r\ntlCmdGoods?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdBattleRandomSkillReqMsg {\r\nlevel?: number\r\ntype?: number\r\nhp?: number\r\ncardId?: number\r\nclientData?: Uint8Array\r\nbattleTime?: number\r\n}\r\n\r\ndeclare interface CmdBattleRandomSkillRspMsg {\r\ntlId?: number[]\r\nselectId?: number[]\r\ncmdBattleData?: CmdBattleData\r\n}\r\n\r\ndeclare interface CmdBattleSelectSkillReqMsg {\r\nlevel?: number\r\nid?: number[]\r\ntype?: number\r\n}\r\n\r\ndeclare interface CmdBattleSelectSkillRspMsg {\r\ncmdBattleData: CmdBattleData\r\n}\r\n\r\ndeclare interface CmdBattlePreviewReqMsg {\r\nlevel: number\r\n}\r\n\r\ndeclare interface CmdBattlePreviewRspMsg {\r\ntlCmdGoods?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdBattleCompleteInfoReqMsg {\r\nlevel: number\r\n}\r\n\r\ndeclare interface CmdBattleCompleteInfoRspMsg {\r\nstar3: number\r\n}\r\n\r\ndeclare interface CmdShareGetRewardReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdShareGetRewardRspMsg {\r\ntlCmdGoods?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdShareStateReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdShareStateRspMsg {\r\nshareState: number\r\nrewardState: number\r\n}\r\n\r\ndeclare interface CmdSpiritAnimalLevelUpReqMsg {\r\nuniqueId: number\r\n}\r\n\r\ndeclare interface CmdSpiritAnimalLevelUpRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdSpiritAnimalStepUpReqMsg {\r\nuniqueId: number\r\ntlSameUniqueId?: number[]\r\ntlCampUniqueId?: number[]\r\n}\r\n\r\ndeclare interface CmdSpiritAnimalStepUpRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdSpiritAnimalBattleReqMsg {\r\nuniqueId: number\r\n}\r\n\r\ndeclare interface CmdSpiritAnimalBattleRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdSpiritAnimalResetReqMsg {\r\nuniqueId: number\r\n}\r\n\r\ndeclare interface CmdSpiritAnimalResetRspMsg {\r\ntlCmdGoods?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdSpiritAnimalResetPreviewReqMsg {\r\nuniqueId: number\r\n}\r\n\r\ndeclare interface CmdSpiritAnimalResetPreviewRspMsg {\r\ntlCmdGoods?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdBuyStaState {\r\nresId: number\r\nstate: number\r\n}\r\n\r\ndeclare interface CmdTask {\r\nresId: number\r\ncount: number\r\nrewardState: CmdCommonState\r\n}\r\n\r\ndeclare interface CmdTask_Key {\r\nresId: number\r\n}\r\n\r\ndeclare enum CmdTaskType {\r\nSEVEN_DAY_TASK=2,\r\nsignin=4\r\n}\r\n\r\ndeclare interface CmdSevenDayInfo {\r\ntlCmdTask?: CmdTask[]\r\ncmdSevenDayPoint: CmdSevenDayPoint\r\ntlCmdSevenDayShop?: CmdSevenDayShop[]\r\n}\r\n\r\ndeclare interface CmdSevenDayPoint {\r\npoint: number\r\ntlIds?: number[]\r\n}\r\n\r\ndeclare interface CmdSevenDayShop {\r\nid: number\r\nbuyTimes: number\r\n}\r\n\r\ndeclare interface CmdSigninInfo {\r\ntlCmdTask?: CmdTask[]\r\nboxTlCmdTask?: CmdTask[]\r\ntype: number\r\nround: number\r\nday: number\r\n}\r\n\r\ndeclare interface CmdSevenDayInfoReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdSevenDayInfoRspMsg {\r\ncmdSevenDayInfo: CmdSevenDayInfo\r\n}\r\n\r\ndeclare interface CmdSevenDayShopBuyReqMsg {\r\nid: number\r\n}\r\n\r\ndeclare interface CmdSevenDayShopBuyRspMsg {\r\ncmdSevenDayInfo: CmdSevenDayInfo\r\ntlCmdGoods?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdSevenDayTakeTaskRewardReqMsg {\r\nresId: number\r\n}\r\n\r\ndeclare interface CmdSevenDayTakeTaskRewardRspMsg {\r\ncmdSevenDayInfo: CmdSevenDayInfo\r\ntlCmdGoods?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdSevenDayTakePointRewardReqMsg {\r\nresId: number\r\n}\r\n\r\ndeclare interface CmdSevenDayTakePointRewardRspMsg {\r\ncmdSevenDayInfo: CmdSevenDayInfo\r\ntlCmdGoods?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdSigninInfoReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdSigninInfoRspMsg {\r\ncmdSigninInfo: CmdSigninInfo\r\n}\r\n\r\ndeclare interface CmdSigninTakeTaskRewardReqMsg {\r\nresId: number\r\n}\r\n\r\ndeclare interface CmdSigninTakeTaskRewardRspMsg {\r\ncmdSigninInfo: CmdSigninInfo\r\ntlCmdGoods?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdEveryDaySign {\r\n\r\n}\r\n\r\ndeclare interface CmdEveryDaySign_Key {\r\nid: number\r\n}\r\n\r\ndeclare interface EveryDaySignInReward {\r\nday: number\r\ntlCmdGoods?: CmdGoods[]\r\nstate: number\r\n}\r\n\r\ndeclare interface CmdEveryDaySignInInfoReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdEveryDaySignInInfoRspMsg {\r\ntlEveryDaySignInReward?: EveryDaySignInReward[]\r\ndays: number\r\n}\r\n\r\ndeclare interface CmdEveryDaySignInReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdEveryDaySignInRspMsg {\r\ntlCmdGoods?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdEveryDayGetRewardsReqMsg {\r\nday: number\r\n}\r\n\r\ndeclare interface CmdEveryDayGetRewardsRspMsg {\r\ntlCmdGoods?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdCastleSkill {\r\nskillId: number\r\nlevel: number\r\n}\r\n\r\ndeclare interface CmdCastleSkill_Key {\r\nskillId: number\r\n}\r\n\r\ndeclare interface CmdCastleWorkShop {\r\nid: number\r\nlevel: number\r\nrewardNum: number\r\nlevelEndTime: number\r\nrewardEndTime: number\r\nhistory: string\r\n}\r\n\r\ndeclare interface CmdCastleWorkShop_Key {\r\nid: number\r\n}\r\n\r\ndeclare interface CmdCastleApprentice {\r\nid: number\r\nlevel: number\r\nstate: boolean\r\n}\r\n\r\ndeclare interface CmdCastleApprentice_Key {\r\nid: number\r\n}\r\n\r\ndeclare interface CmdCastleWall {\r\nselectType: number\r\ntlWallType?: number[]\r\nlevel: number\r\n}\r\n\r\ndeclare interface CmdCastleWall_Key {\r\n\r\n}\r\n\r\ndeclare interface CmdCastleWallSkill {\r\nid: number\r\nlevel: number\r\n}\r\n\r\ndeclare interface CmdCastleWallSkill_Key {\r\nid: number\r\n}\r\n\r\ndeclare interface CmdRole {\r\nid: number\r\nname: string\r\nimg: number\r\nheadFrame: number\r\nlv: number\r\nexp: number\r\ngold: string\r\ngem: string\r\ncoin: string\r\nwashStone: number\r\nend: number\r\ncreateTime: number\r\nnextEndRecoverTime: number\r\nselectEquipPage: number\r\nminingProcess: number\r\nminingShow: boolean\r\nrecharge: number\r\ncmdFirstRechargeInfo: CmdFirstRechargeInfo\r\ntlCmdAdvert?: CmdAdvert[]\r\nplatformImg: string\r\nfreeRenameCount: number\r\ntlExtraId?: number[]\r\nendMax: number\r\ninternal: number\r\ntlCmdAppletInfo?: CmdAppletInfo[]\r\nsweepTimes: number\r\nregion?: string\r\nselectServerId?: number\r\nbattleSpiritAnimalUniqueId?: number\r\nspiritAnimalAtkAdd?: number\r\nmaxBattleLevelPassTime?: number\r\n}\r\n\r\ndeclare interface CmdRole_Key {\r\n\r\n}\r\n\r\ndeclare interface CmdRoleEndBuyInfo {\r\nbuyId: number\r\nnumber: number\r\n}\r\n\r\ndeclare interface CmdFirstRechargeInfo {\r\nid: number\r\nrewardIds?: number[]\r\nlastTakeTime?: number\r\n}\r\n\r\ndeclare interface CmdAdvert {\r\ntype: number\r\nnum: number\r\nnextTime?: number\r\n}\r\n\r\ndeclare enum LoginStatus {\r\nSUCCESS=1,\r\nWEI_HU=2,\r\nBAN=3\r\n}\r\n\r\ndeclare interface CmdAppletInfo {\r\ntype: number\r\nnum: number\r\nextra?: string\r\n}\r\n\r\ndeclare interface CmdGuanKaGift {\r\nresId: number\r\nendTime: number\r\nselectId?: number\r\nreceive?: number\r\n}\r\n\r\ndeclare interface CmdGuanKaGift_Key {\r\nresId: number\r\n}\r\n\r\ndeclare interface CmdGuanKaGiftBroMsg {\r\ncmdGuanKaGift: CmdGuanKaGift\r\n}\r\n\r\ndeclare interface CmdChapterRewardReqMsg {\r\nresId: number\r\n}\r\n\r\ndeclare interface CmdChapterRewardRspMsg {\r\ntlCmdGoods?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdBuyStaBuyReqMsg {\r\nresId: number\r\n}\r\n\r\ndeclare interface CmdBuyStaBuyRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdBuyStaInfoReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdBuyStaInfoRspMsg {\r\ncmdBuyStaState?: CmdBuyStaState[]\r\n}\r\n\r\ndeclare interface CmdLockMonster {\r\nlevel: number\r\nnextBoxRefreshTime: number\r\n}\r\n\r\ndeclare interface CmdLockMonster_Key {\r\n\r\n}\r\n\r\ndeclare interface CmdHallRole {\r\nroleId: number\r\nroleName?: string\r\npoint?: CmdPoint\r\n}\r\n\r\ndeclare interface CmdPoint {\r\nx: number\r\ny: number\r\n}\r\n\r\ndeclare interface CmdRune {\r\nid: number\r\nresId: number\r\nquality: number\r\nphase: number\r\nisPutOn: boolean\r\nisNew: boolean\r\n}\r\n\r\ndeclare interface CmdRune_Key {\r\nid: number\r\n}\r\n\r\ndeclare interface CmdDayWeekMonthGift {\r\ntlDayGift?: EveryDayGift[]\r\ntlWeekGift?: EveryDayGift[]\r\ntlMonthGift?: EveryDayGift[]\r\ntlRechargeCount?: RechargeCount[]\r\nrechargeDay: number\r\n}\r\n\r\ndeclare interface CmdDayWeekMonthGift_Key {\r\nid: number\r\n}\r\n\r\ndeclare interface EveryDayGift {\r\ngiftId: number\r\nfromId: number\r\nbuyCount: number\r\nrewards?: CmdGoods[]\r\n}\r\n\r\ndeclare interface RechargeCount {\r\nresId: number\r\nbuyState: number\r\nrewards?: CmdGoods[]\r\n}\r\n\r\ndeclare interface ClientCmdData {\r\nmessageId: number\r\nclientIndex: number\r\ndata?: Uint8Array\r\nappendData?: Uint8Array\r\n}\r\n\r\ndeclare interface ServerCmdData {\r\nclientIndex: number\r\nmessageIds?: number[]\r\nsplits?: number[]\r\ndata: Uint8Array\r\ncmdCaches?: CmdCache[]\r\ncompress: boolean\r\nresult: boolean\r\n}\r\n\r\ndeclare interface CmdCache {\r\ncacheName: string\r\ntype: number\r\ndata?: Uint8Array[]\r\n}\r\n\r\ndeclare interface CmdCacheUpdateByField {\r\nkey: Uint8Array\r\nfields?: CmdCacheField[]\r\n}\r\n\r\ndeclare interface CmdCacheField {\r\nfieldNumber: number\r\ndata: Uint8Array\r\n}\r\n\r\ndeclare interface CmdInt32 {\r\nvalue: number\r\n}\r\n\r\ndeclare interface CmdInt64 {\r\nvalue: number\r\n}\r\n\r\ndeclare interface CmdDouble {\r\nvalue: number\r\n}\r\n\r\ndeclare interface CmdFloat {\r\nvalue: number\r\n}\r\n\r\ndeclare interface CmdDayWeekMonthGiftGetRewardReqMsg {\r\nid: number\r\n}\r\n\r\ndeclare interface CmdDayWeekMonthGiftGetRewardRspMsg {\r\ntlCmdGoods?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdDayWeekMonthGiftInfoReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdDayWeekMonthGiftInfoRspMsg {\r\ncmdDayWeekMonthGift: CmdDayWeekMonthGift\r\n}\r\n\r\ndeclare interface CmdDayWeekMonthGiftFreeBuyReqMsg {\r\nid: number\r\n}\r\n\r\ndeclare interface CmdDayWeekMonthGiftFreeBuyRspMsg {\r\ntlCmdGoods?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdMail {\r\nid: number\r\ntitle: string\r\ncontent: string\r\ncmdGoodsList?: CmdGoodsList\r\nread: boolean\r\ntake: boolean\r\nsendTime: number\r\nreadTime: number\r\n}\r\n\r\ndeclare interface CmdMailSimple {\r\nid: number\r\ntitle: string\r\nread: boolean\r\ntake: boolean\r\nsendTime: number\r\n}\r\n\r\ndeclare interface CmdRoll {\r\ncontent: string\r\ntime: number\r\n}\r\n\r\ndeclare interface CmdRollBroMsg {\r\ncmdRoll: CmdRoll\r\n}\r\n\r\ndeclare interface CmdBattleToken {\r\nroleId: number\r\nlevel: number\r\nexp: number\r\ntlBattleTokenMessage?: CmdBattleTokenMessage[]\r\nsurplusDay: number\r\n}\r\n\r\ndeclare interface CmdBattleToken_Key {\r\nroleId: number\r\n}\r\n\r\ndeclare interface CmdBattleTokenMessage {\r\nresId: number\r\nopen: boolean\r\ntlTakeLevel?: number[]\r\n}\r\n\r\ndeclare interface CmdBattleTokenOpenReqMsg {\r\nresId: number\r\n}\r\n\r\ndeclare interface CmdBattleTokenOpenRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdBattleTokenTakeRewardReqMsg {\r\ntakeAll?: boolean\r\nresId?: number\r\nlevel?: number\r\n}\r\n\r\ndeclare interface CmdBattleTokenTakeRewardRspMsg {\r\ntlReward?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdBattleTokenResetReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdBattleTokenResetRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdActivity {\r\nopenResId?: number\r\nactivityRefId?: number\r\nstartTime?: number\r\nendTime?: number\r\ndetail?: CmdActivityDetail\r\n}\r\n\r\ndeclare interface CmdActivity_Key {\r\nopenResId: number\r\n}\r\n\r\ndeclare interface CmdActivityDetail {\r\ndayChallengeActDetail?: CmdDayChallengeActDetail\r\nmonthCardActDetail?: CmdMonthCardActDetail\r\nrechargeGiftActDetail?: CmdRechargeGiftActDetail\r\ncmdRechargeGiftAct2Detail?: CmdRechargeGiftAct2Detail\r\ncmdRechargeGiftAct3Detail?: CmdRechargeGiftAct3Detail\r\nmonthCardActDetail2?: CmdMonthCardActDetail\r\nskinActDetail?: CmdSkinActDetail\r\n}\r\n\r\ndeclare interface CmdSkinActDetail {\r\nskinResId: number\r\nprice: number\r\nalreadyBuy: boolean\r\n}\r\n\r\ndeclare interface CmdDayChallengeActDetail {\r\ntlDayChallengeTask?: CmdDayChallengeTask[]\r\n}\r\n\r\ndeclare interface CmdMonthCardActDetail {\r\ncardEndTime: number\r\ntodayTake?: boolean\r\n}\r\n\r\ndeclare interface CmdRechargeGiftActDetail {\r\ncurRechargeProcess: number\r\ntlTakeRechargeProcess?: number[]\r\n}\r\n\r\ndeclare interface CmdDayChallengeTask {\r\nresId: number\r\nprocess: number\r\nstate: CmdCommonState\r\n}\r\n\r\ndeclare interface CmdRechargeGiftAct2Detail {\r\ncurProcess: number\r\ntlTakeProcess?: number[]\r\n}\r\n\r\ndeclare interface CmdRechargeGiftAct3Detail {\r\ncurProcess: number\r\ntlTakeProcess?: number[]\r\n}\r\n\r\ndeclare interface CmdActivitySimple {\r\nid: string\r\ntype: number\r\nname: string\r\ncmdActivityTime: CmdActivityTime\r\ntlCmdActivityTime?: CmdActivityTime[]\r\nchangeTime?: number\r\njsonNames?: string[]\r\nexist: boolean\r\n}\r\n\r\ndeclare interface CmdActivitySimple_Key {\r\nid: string\r\n}\r\n\r\ndeclare enum CmdActivityTypeEnum {\r\nlimit_time=101,\r\nlimit_skin=102\r\n}\r\n\r\ndeclare interface CmdActivityTime {\r\nstartTime?: number\r\nendTime?: number\r\n}\r\n\r\ndeclare interface CmdActivityGoods {\r\nresId: number\r\ntimes: number\r\n}\r\n\r\ndeclare interface CmdSkinPlayer {\r\ntlCmdSkinShop?: CmdSkinShop[]\r\n}\r\n\r\ndeclare interface CmdSkinShop {\r\nid: number\r\nbuy: boolean\r\n}\r\n\r\ndeclare interface CmdTakeGameCircleRewardReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdTakeGameCircleRewardRspMsg {\r\ntlCmdGoods?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdCastleSkillUpLevelReqMsg {\r\nskillId: number\r\n}\r\n\r\ndeclare interface CmdCastleSkillUpLevelRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdCastleSkillResearchReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdCastleSkillResearchRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdCastleWorkShopOpenReqMsg {\r\nid: number\r\n}\r\n\r\ndeclare interface CmdCastleWorkShopOpenRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdCastleWorkShopUpLevelReqMsg {\r\nid: number\r\n}\r\n\r\ndeclare interface CmdCastleWorkShopUpLevelRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdCastleWorkShopSpeedReqMsg {\r\nid: number\r\n}\r\n\r\ndeclare interface CmdCastleWorkShopSpeedRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdCastleWorkShopTakeReqMsg {\r\nid: number\r\n}\r\n\r\ndeclare interface CmdCastleWorkShopTakeRspMsg {\r\ntlCmdGoods?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdCastleApprenticeUpReqMsg {\r\nid: number\r\ntype: number\r\n}\r\n\r\ndeclare interface CmdCastleApprenticeUpRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdCastleApprenticeUpLevelReqMsg {\r\nid: number\r\n}\r\n\r\ndeclare interface CmdCastleApprenticeUpLevelRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdCastleWallUpLevelReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdCastleWallUpLevelRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdCastleWallQualityUpReqMsg {\r\nwallType: number\r\n}\r\n\r\ndeclare interface CmdCastleWallQualityUpRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdCastleWallSelectReqMsg {\r\ntype: number\r\n}\r\n\r\ndeclare interface CmdCastleWallSelectRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdCastleWallUpSkillLevelReqMsg {\r\nid: number\r\n}\r\n\r\ndeclare interface CmdCastleWallUpSkillLevelRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdItem {\r\nresId: number\r\nnumber: number\r\nisNew: boolean\r\n}\r\n\r\ndeclare interface CmdItem_Key {\r\nresId: number\r\n}\r\n\r\ndeclare interface CmdItemUseReqMsg {\r\nresId: number\r\nnumber: number\r\n}\r\n\r\ndeclare interface CmdItemUseRspMsg {\r\ntlCmdGoods?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdItemBoxUseReqMsg {\r\nresId: number\r\nnumber: number\r\ncmdGoodsList?: CmdGoodsList\r\n}\r\n\r\ndeclare interface CmdItemBoxUseRspMsg {\r\ntlCmdGoods?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdGemOnReqMsg {\r\nequipId: number\r\npage: number\r\nid: number\r\n}\r\n\r\ndeclare interface CmdGemOnRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdGemReplaceReqMsg {\r\nequipId: number\r\npage: number\r\nid: number\r\norder: number\r\n}\r\n\r\ndeclare interface CmdGemReplaceRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdGemDownReqMsg {\r\nequipId: number\r\npage: number\r\norder: number\r\n}\r\n\r\ndeclare interface CmdGemDownRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdGemRefineReqMsg {\r\nid: number\r\ntype?: number\r\n}\r\n\r\ndeclare interface CmdGemRefineRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdGemRefineSaveReqMsg {\r\nid: number\r\ntype: number\r\n}\r\n\r\ndeclare interface CmdGemRefineSaveRspMsg {\r\nid?: number\r\n}\r\n\r\ndeclare interface CmdGemComposeReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdGemComposeRspMsg {\r\ntlCmdGoods?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdGemLockReqMsg {\r\nid: number\r\nlock?: number\r\n}\r\n\r\ndeclare interface CmdGemLockRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdGemComposePreviewReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdGemComposePreviewRspMsg {\r\ntlCmdGemPreview?: CmdGemPreview[]\r\n}\r\n\r\ndeclare interface CmdMailListReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdMailListRspMsg {\r\ntlCmdMailSimple?: CmdMailSimple[]\r\n}\r\n\r\ndeclare interface CmdMailReadReqMsg {\r\nid: number\r\n}\r\n\r\ndeclare interface CmdMailReadRspMsg {\r\ncmdMail: CmdMail\r\n}\r\n\r\ndeclare interface CmdMailDeleteReqMsg {\r\nid?: number[]\r\n}\r\n\r\ndeclare interface CmdMailDeleteRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdMailTakeRewardReqMsg {\r\nid?: number[]\r\n}\r\n\r\ndeclare interface CmdMailTakeRewardRspMsg {\r\ntlReward?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdCardCache {\r\nresId: number\r\nendTime: number\r\n}\r\n\r\ndeclare interface CmdCardCache_Key {\r\nresId: number\r\n}\r\n\r\ndeclare interface CmdCardBuyReqMsg {\r\nresId: number\r\n}\r\n\r\ndeclare interface CmdCardBuyRspMsg {\r\ntlRewards?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdHero {\r\nresId: number\r\nlevel: number\r\nstate: number\r\nequipId?: number\r\n}\r\n\r\ndeclare interface CmdHero_Key {\r\nresId: number\r\n}\r\n\r\ndeclare interface CmdHeroUnlockInfo {\r\nresId: number\r\ncount: number\r\nrewardState: number\r\n}\r\n\r\ndeclare interface CmdHeroChangeReqMsg {\r\nresId: number\r\n}\r\n\r\ndeclare interface CmdHeroChangeRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdHeroLevelUpReqMsg {\r\nresId: number\r\n}\r\n\r\ndeclare interface CmdHeroLevelUpRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdHeroLevelUpOneKeyReqMsg {\r\nresId: number\r\n}\r\n\r\ndeclare interface CmdHeroLevelUpOneKeyRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdHeroOpenReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdHeroOpenRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdHeroUnlockGetInfoReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdHeroUnlockGetInfoRspMsg {\r\ncmdHeroUnlockInfo?: CmdHeroUnlockInfo[]\r\n}\r\n\r\ndeclare interface CmdHeroUnlockRewardReqMsg {\r\nresId: number\r\n}\r\n\r\ndeclare interface CmdHeroUnlockRewardRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdSpecialGiftCache {\r\nresId: number\r\nstate: CmdCommonState\r\nendTime: number\r\n}\r\n\r\ndeclare interface CmdSpecialGiftCache_Key {\r\nresId: number\r\n}\r\n\r\ndeclare interface CmdTakeLockMonsterBoxReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdTakeLockMonsterBoxRspMsg {\r\ntlCmdGoods?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdGemInfo {\r\norder: number\r\ngemId: number\r\n}\r\n\r\ndeclare interface CmdEquipPage {\r\npage: number\r\nname: string\r\ntlCmdEquip?: CmdEquip[]\r\n}\r\n\r\ndeclare interface CmdEquipPage_Key {\r\npage: number\r\n}\r\n\r\ndeclare interface CmdEquip {\r\nequipId: number\r\nlevel: number\r\ntlCmdGemInfo?: CmdGemInfo[]\r\n}\r\n\r\ndeclare interface CmdRoleLoginReqMsg {\r\ncmdClientInfo: CmdClientInfo\r\nplatform: number\r\ncode: string\r\nroleId: number\r\ntime: number\r\ntoken: string\r\nreconnect?: boolean\r\nimg?: string\r\nopenid?: string\r\nselectServerId?: number\r\n}\r\n\r\ndeclare interface CmdRoleLoginRspMsg {\r\nnewRole?: boolean\r\ncmdBattleData?: CmdBattleData\r\nloginStatus: LoginStatus\r\n}\r\n\r\ndeclare interface CmdRoleChangeNameReqMsg {\r\nname: string\r\n}\r\n\r\ndeclare interface CmdRoleChangeNameRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdRoleChangeImgReqMsg {\r\nresId: number\r\n}\r\n\r\ndeclare interface CmdRoleChangeImgRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdRoleUploadPlatformImgReqMsg {\r\nplatformImg: string\r\n}\r\n\r\ndeclare interface CmdRoleUploadPlatformImgRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdRoleChangeHeadFrameReqMsg {\r\nresId: number\r\n}\r\n\r\ndeclare interface CmdRoleChangeHeadFrameRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdEndBuyInfoReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdEndBuyInfoRspMsg {\r\ntlEndBuyInfo?: CmdRoleEndBuyInfo[]\r\n}\r\n\r\ndeclare interface CmdEndBuyReqMsg {\r\nbuyId: number\r\n}\r\n\r\ndeclare interface CmdEndBuyRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdRoleEndUpdateReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdRoleEndUpdateRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdMiningBuyReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdMiningBuyRspMsg {\r\ntlReward?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdCodeExchangeReqMsg {\r\ncode: string\r\n}\r\n\r\ndeclare interface CmdCodeExchangeRspMsg {\r\ntlReward?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdRoleTakeFirstChargeReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdRoleTakeFirstChargeRspMsg {\r\ntlCmdGoods?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdRoleWatchAdvertReqMsg {\r\ntype: number\r\n}\r\n\r\ndeclare interface CmdRoleWatchAdvertRspMsg {\r\ntlCmdGoods?: CmdGoods[]\r\ntlId?: number[]\r\n}\r\n\r\ndeclare interface CmdRoleInfoViewReqMsg {\r\nroleId: number\r\nserverId: number\r\n}\r\n\r\ndeclare interface CmdRoleInfoViewRspMsg {\r\nroleId: number\r\nroleName: string\r\nimg: number\r\nplatformImg: string\r\nheadFrame: number\r\nlevel: number\r\ntlCmdEquip?: CmdEquip[]\r\n}\r\n\r\ndeclare interface CmdRoleDouYinRewardReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdRoleDouYinRewardRspMsg {\r\ntlCmdGoods?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdRoleAppletShareReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdRoleAppletShareRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdRoleAppletShareRewardReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdRoleAppletShareRewardRspMsg {\r\ntlCmdGoods?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdRoleGuanKaGiftSelectReqMsg {\r\nresId: number\r\ngemId: number\r\n}\r\n\r\ndeclare interface CmdRoleGuanKaGiftSelectRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdRoleGuanKaGiftBuyReqMsg {\r\nresId: number\r\n}\r\n\r\ndeclare interface CmdRoleGuanKaGiftBuyRspMsg {\r\ntlCmdGoods?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdShop {\r\ncmdShopType: CmdShopType\r\ntlCmdShopGoods?: CmdShopGoods[]\r\n}\r\n\r\ndeclare interface CmdShop_Key {\r\ncmdShopType: CmdShopType\r\n}\r\n\r\ndeclare interface CmdShopGoods {\r\nid: number\r\nbuyTimes: number\r\nnextBuyTime?: number\r\nnextTime?: number\r\nnextTime50?: number\r\nboxProcess?: number\r\nalreadyTakeBox?: number[]\r\n}\r\n\r\ndeclare enum CmdShopType {\r\nbox=1,\r\nresource=2,\r\ndiamond=3,\r\ngold=4,\r\ngift=5,\r\nspiritAnimal=6\r\n}\r\n\r\ndeclare interface CmdContinuityGiftInfo {\r\ndays: number\r\nstatus: number\r\n}\r\n\r\ndeclare interface CmdGoodReplace {\r\noldResId: number\r\nnewResId: number\r\nnumber: number\r\n}\r\n\r\ndeclare interface CmdShopBuyReqMsg {\r\nid: number\r\ncmdShopType: CmdShopType\r\ntenTimes?: boolean\r\ntlGoodReplace?: CmdGoodReplace[]\r\n}\r\n\r\ndeclare interface CmdShopBuyRspMsg {\r\ntlCmdGoods?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdContinuityGiftInfoReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdContinuityGiftInfoRspMsg {\r\ntlCmdContinuityGiftInfo?: CmdContinuityGiftInfo[]\r\nendTime: number\r\naccumulativeAward?: number[]\r\n}\r\n\r\ndeclare interface CmdContinuityGiftRewardReqMsg {\r\ndays: number\r\n}\r\n\r\ndeclare interface CmdContinuityGiftRewardRspMsg {\r\ntlCmdContinuityGiftInfo?: CmdContinuityGiftInfo[]\r\ntlCmdGoods?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdContinuityGiftFinishRewardReqMsg {\r\ndays: number\r\n}\r\n\r\ndeclare interface CmdContinuityGiftFinishRewardRspMsg {\r\ntlCmdGoods?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdTakeSpiritAnimalBoxReqMsg {\r\nid: number\r\ntakeProcess: number\r\nresId: number\r\n}\r\n\r\ndeclare interface CmdTakeSpiritAnimalBoxRspMsg {\r\ntlCmdGoods?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdHang {\r\n\r\n}\r\n\r\ndeclare interface CmdHang_Key {\r\nresId: number\r\n}\r\n\r\ndeclare interface CmdRankData {\r\nrankList?: CmdRankRole[]\r\nmyRank: CmdRankRole\r\nregion?: string\r\n}\r\n\r\ndeclare interface CmdRankRole {\r\nrank: number\r\nroleId: number\r\nroleName?: string\r\nroleImg?: number\r\nplatFormImg?: string\r\nheadFrame?: number\r\nvalue?: number\r\nserverId?: number\r\nselectServerId?: number\r\n}\r\n\r\ndeclare enum CmdRankType {\r\nLOCK_MONSTER_FULL_RANK=1,\r\nLOCK_MONSTER_REGION_RANK=2,\r\nSECRET_REALM_FULL_RANK=3,\r\nSECRET_REALM_REGION_RANK=4,\r\nCHAPTER_FULL_RANK=5,\r\nCHAPTER_REGION_RANK=6,\r\nCHAPTER_SERVER_RANK=7,\r\nLOCK_MONSTER_SERVER_RANK=8,\r\nSECRET_REALM_SERVER_RANK=9,\r\nCHAPTER_SERVER_REWARD_RANK=10\r\n}\r\n\r\ndeclare interface CmdRankItem {\r\ntype: CmdRankType\r\nstartTime?: number\r\nendTime?: number\r\nshowEndTime?: number\r\n}\r\n\r\ndeclare interface CmdRankItem_Key {\r\ntype: CmdRankType\r\n}\r\n\r\ndeclare interface CmdGetRankReqMsg {\r\nrankType: CmdRankType\r\ntype?: number\r\n}\r\n\r\ndeclare interface CmdGetRankRspMsg {\r\nrankInfo?: CmdRankData\r\n}\r\n\r\ndeclare interface CmdGetHallRoleListReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdGetHallRoleListRspMsg {\r\ntlHallRole?: CmdHallRole[]\r\n}\r\n\r\ndeclare interface CmdMall {\r\nid?: number\r\ndetail?: CmdMallDetail\r\n}\r\n\r\ndeclare interface CmdMall_Key {\r\nid: number\r\n}\r\n\r\ndeclare interface CmdMallDetail {\r\ncmdMallSEquipDetail?: CmdMallSEquipDetail[]\r\ncmdMallLuckDrawDetail?: CmdMallLuckDrawDetail[]\r\ncmdMallDailyShopDetail?: CmdMallDailyShopDetail[]\r\ncmdMallChapterGiftDetail?: CmdMallChapterGiftDetail[]\r\ncmdMallChargeDetail?: CmdMallChargeDetail[]\r\ncmdMallCoinDetail?: CmdMallCoinDetail[]\r\n}\r\n\r\ndeclare interface CmdMallSEquipDetail {\r\nresId?: number\r\nbuyCount?: number\r\nbigPrizeCount?: number\r\nultimatePrizeCount?: number\r\n}\r\n\r\ndeclare interface CmdMallLuckDrawDetail {\r\nresId?: number\r\nbuyCount?: number\r\nbigPrizeCount?: number\r\nfreeTime?: number\r\n}\r\n\r\ndeclare interface CmdMallDailyShopDetail {\r\nresId?: number\r\nrewardId?: number\r\nbuyCount?: number\r\nadTime?: number\r\noriginal?: string\r\ndiscount?: string\r\nfinalNumber?: string\r\n}\r\n\r\ndeclare interface CmdMallChapterGiftDetail {\r\nresId?: number\r\nstate?: number\r\n}\r\n\r\ndeclare interface CmdMallChargeDetail {\r\nresId?: number\r\nstate?: number\r\n}\r\n\r\ndeclare interface CmdMallCoinDetail {\r\nresId?: number\r\nbuyCount?: number\r\nfreeTime?: number\r\n}\r\n\r\ndeclare interface CmdCacheBroMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdRoleOfflineBroMsg {\r\ntype: number\r\n}\r\n\r\ndeclare interface CmdRechargeBroMsg {\r\ncmdRecharge: CmdRecharge\r\n}\r\n\r\ndeclare interface CmdModifyItemBroMsg {\r\ntlCmdGoods?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdBattleBroMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdMallBroMsg {\r\nmallId: number\r\ncmdMall: CmdMall\r\n}\r\n\r\ndeclare interface CmdEndBroMsg {\r\nend: number\r\nnextTime: number\r\n}\r\n\r\ndeclare interface CmdTakeDayChallengeActRewardReqMsg {\r\nresId: number\r\ntaskResId: number\r\n}\r\n\r\ndeclare interface CmdTakeDayChallengeActRewardRspMsg {\r\ntlReward?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdTakeMonthCardActRewardReqMsg {\r\nresId: number\r\n}\r\n\r\ndeclare interface CmdTakeMonthCardActRewardRspMsg {\r\ntlReward?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdTakeRechargeGiftActRewardReqMsg {\r\nresId: number\r\nrechargeProcess: number\r\n}\r\n\r\ndeclare interface CmdTakeRechargeGiftActRewardRspMsg {\r\ntlReward?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdActivityJsonReqMsg {\r\nactivityId: string\r\ntlJsonName?: string[]\r\n}\r\n\r\ndeclare interface CmdActivityJsonRspMsg {\r\ncontent: string\r\n}\r\n\r\ndeclare interface CmdActivityLimitTimeInfoReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdActivityLimitTimeInfoRspMsg {\r\ntlCmdTask?: CmdTask[]\r\ntlLimitTimeDay?: CmdActivityGoods[]\r\ntlLimitTimeActivity?: CmdActivityGoods[]\r\n}\r\n\r\ndeclare interface CmdActivityLimitTimeTakeReqMsg {\r\nresId: number\r\ntype: number\r\n}\r\n\r\ndeclare interface CmdActivityLimitTimeTakeRspMsg {\r\ntlCmdGoods?: CmdGoods[]\r\ntlCmdTask?: CmdTask[]\r\ntlLimitTimeDay?: CmdActivityGoods[]\r\ntlLimitTimeActivity?: CmdActivityGoods[]\r\n}\r\n\r\ndeclare interface CmdBuySkinReqMsg {\r\nresId: number\r\n}\r\n\r\ndeclare interface CmdBuySkinRspMsg {\r\ntlReward?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdActivityLimitSkinInfoReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdActivityLimitSkinInfoRspMsg {\r\ncmdSkinPlayer: CmdSkinPlayer\r\n}\r\n\r\ndeclare interface CmdActivityLimitSkinBuyReqMsg {\r\nid: number\r\n}\r\n\r\ndeclare interface CmdActivityLimitSkinBuyRspMsg {\r\ntlReward?: CmdGoods[]\r\ncmdSkinPlayer: CmdSkinPlayer\r\n}\r\n\r\ndeclare interface CmdFriendsApplyReqMsg {\r\ntargetId: number\r\n}\r\n\r\ndeclare interface CmdFriendsApplyRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdFriendsAddReqMsg {\r\ntargetId: number\r\n}\r\n\r\ndeclare interface CmdFriendsAddRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdFriendsRefuseReqMsg {\r\ntargetId: number\r\n}\r\n\r\ndeclare interface CmdFriendsRefuseRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdFriendsRemoveReqMsg {\r\ntargetId: number\r\n}\r\n\r\ndeclare interface CmdFriendsRemoveRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdFriendsBlackReqMsg {\r\ntargetId: number\r\n}\r\n\r\ndeclare interface CmdFriendsBlackRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdFriendsSearchReqMsg {\r\ntargetId: number\r\n}\r\n\r\ndeclare interface CmdFriendsSearchRspMsg {\r\ndetail: CmdFriendsShowDetail\r\n}\r\n\r\ndeclare interface CmdFriendGetEndReqMsg {\r\ntargetId: number\r\n}\r\n\r\ndeclare interface CmdFriendGetEndRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdFriendGetEndOneKeyReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdFriendGetEndOneKeyRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdFriendSendEndReqMsg {\r\ntargetId: number\r\n}\r\n\r\ndeclare interface CmdFriendSendEndRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdFriendSendEndOneKeyReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdFriendSendEndOneKeyRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdFriendAgreeOneKeyReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdFriendAgreeOneKeyRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdFriendRefuseOneKeyReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdFriendRefuseOneKeyRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdFriendDetailReqMsg {\r\ntargetId: number\r\n}\r\n\r\ndeclare interface CmdFriendDetailRspMsg {\r\ncmdFriendsShowDetail: CmdFriendsShowDetail\r\nequipList?: CmdFriendsWearEquip[]\r\n}\r\n\r\ndeclare interface CmdFriendLikeReqMsg {\r\ntargetId: number\r\n}\r\n\r\ndeclare interface CmdFriendLikeRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdFriendBlackRemoveReqMsg {\r\ntargetId: number\r\n}\r\n\r\ndeclare interface CmdFriendBlackRemoveRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdFriendRefreshSuggestReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdFriendRefreshSuggestRspMsg {\r\ntlSuggestFriend?: CmdFriendsShowDetail[]\r\n}\r\n\r\ndeclare interface CmdHangGetRewardReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdHangGetRewardRspMsg {\r\ntlCmdGoods?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdHangQuickReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdHangQuickRspMsg {\r\ntlCmdGoods?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdHangTimeReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdHangTimeRspMsg {\r\ntime: number\r\n}\r\n\r\ndeclare interface CmdSystemSyncTimeReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdSystemSyncTimeRspMsg {\r\nserverTime: number\r\n}\r\n\r\ndeclare interface CmdSystemGmReqMsg {\r\ncmd: string\r\n}\r\n\r\ndeclare interface CmdSystemGmRspMsg {\r\ntlCmdGoods?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdGmListReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdGmListRspMsg {\r\ncmd?: CmdGmInfo[]\r\n}\r\n\r\ndeclare interface CmdRecordClientAniReqMsg {\r\nclientAniRefId: number\r\n}\r\n\r\ndeclare interface CmdRecordClientAniRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdGetAllPlayClientAniReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdGetAllPlayClientAniRspMsg {\r\nclientAniRefId?: number[]\r\n}\r\n\r\ndeclare interface CmdCode {\r\n\r\n}\r\n\r\ndeclare interface CmdCode_Key {\r\nid: number\r\n}\r\n\r\ndeclare interface CmdBreach {\r\n\r\n}\r\n\r\ndeclare interface CmdBreach_Key {\r\nresId: number\r\n}\r\n\r\ndeclare interface CmdChangeUsedSkinReqMsg {\r\nresId: number\r\n}\r\n\r\ndeclare interface CmdChangeUsedSkinRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdRunePutOnReqMsg {\r\nid: number\r\narea: number\r\n}\r\n\r\ndeclare interface CmdRunePutOnRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdRuneTakeOffReqMsg {\r\nid: number\r\narea: number\r\n}\r\n\r\ndeclare interface CmdRuneTakeOffRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdRuneReplaceReqMsg {\r\nid: number\r\nreplaceId: number\r\n}\r\n\r\ndeclare interface CmdRuneReplaceRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdRuneSynthesisReqMsg {\r\nresId: number\r\nquality: number\r\nphase: number\r\nnid?: number[]\r\n}\r\n\r\ndeclare interface CmdRuneSynthesisRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdRuneSynthesisOneKeyReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdRuneSynthesisOneKeyRspMsg {\r\nid?: number[]\r\n}\r\n\r\ndeclare interface CmdRedDotCache {\r\nredDotType: CmdRedDotType\r\nnumber: number\r\nnextRefreshTime?: number\r\n}\r\n\r\ndeclare interface CmdRedDotCache_Key {\r\nredDotType: CmdRedDotType\r\n}\r\n\r\ndeclare interface CmdGoodsRedDot {\r\nresId: number\r\nid?: number\r\n}\r\n\r\ndeclare enum CmdRedDotType {\r\nTEST=1,\r\nMAil=2,\r\ncontinuity_days=3,\r\nGEM_COMPOSE=4,\r\nSEVEN_DAY=5,\r\nlimit_time_activity=6,\r\nsignin=7,\r\nSPIRIT_ANIMAL_BOX=8\r\n}\r\n\r\ndeclare interface CmdUpdateRedDotReqMsg {\r\nredDotType: CmdRedDotType\r\n}\r\n\r\ndeclare interface CmdUpdateRedDotRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdGoodsRedDotDispelReqMsg {\r\ndispelParam?: CmdGoodsRedDot[]\r\n}\r\n\r\ndeclare interface CmdGoodsRedDotDispelRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdEquipUpLevelAllReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdEquipUpLevelAllRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdEquipUpLevelReqMsg {\r\nid: number\r\ntype?: number\r\n}\r\n\r\ndeclare interface CmdEquipUpLevelRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdEquipSelectPageReqMsg {\r\npage: number\r\n}\r\n\r\ndeclare interface CmdEquipSelectPageRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdEquipChangePageNameReqMsg {\r\npage: number\r\nname: string\r\n}\r\n\r\ndeclare interface CmdEquipChangePageNameRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdFirstRecharge {\r\n\r\n}\r\n\r\ndeclare interface CmdFirstRecharge_Key {\r\nid: number\r\n}\r\n\r\ndeclare interface CmdFundGetInfoReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdFundGetInfoRspMsg {\r\ncmdFundDetail?: CmdFundDetail[]\r\n}\r\n\r\ndeclare interface CmdFundGetRewardReqMsg {\r\nresId: number\r\n}\r\n\r\ndeclare interface CmdFundGetRewardRspMsg {\r\ntlCmdGoods?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdSpiritAnimal {\r\nuniqueId: number\r\nresId: number\r\nlevel: number\r\n}\r\n\r\ndeclare interface CmdSpiritAnimal_Key {\r\nuniqueId: number\r\n}\r\n\r\ndeclare interface CmdSecretRealm {\r\nroleId: number\r\ntype: number\r\nlevel: number\r\nendTime: number\r\nsecretRealmScore?: CmdSecretRealmScore[]\r\n}\r\n\r\ndeclare interface CmdSecretRealm_Key {\r\nroleId: number\r\n}\r\n\r\ndeclare interface CmdSecretRealmScore {\r\ntype: number\r\nlevel: number\r\n}\r\n\r\ndeclare interface CmdShare {\r\n\r\n}\r\n\r\ndeclare interface CmdShare_Key {\r\nid: number\r\n}\r\n\r\ndeclare interface CmdBreachReqMsg {\r\nresId: number\r\ntype: number\r\n}\r\n\r\ndeclare interface CmdBreachRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdFirstRechargeRewardReqMsg {\r\nresId: number\r\n}\r\n\r\ndeclare interface CmdFirstRechargeRewardRspMsg {\r\ntlCmdGoods?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdFirstRechargeOpenReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdFirstRechargeOpenRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdMallSEquipDrawReqMsg {\r\nmallId: number\r\nresId: number\r\ntype: number\r\ncount: boolean\r\n}\r\n\r\ndeclare interface CmdMallSEquipDrawRspMsg {\r\ntlCmdGoods?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdMallLuckDrawReqMsg {\r\nmallId: number\r\nresId: number\r\ntype: number\r\n}\r\n\r\ndeclare interface CmdMallLuckDrawRspMsg {\r\ntlCmdGoods?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdMallDailyShopBuyReqMsg {\r\nmallId: number\r\nplanId: number\r\nisAd: number\r\n}\r\n\r\ndeclare interface CmdMallDailyShopBuyRspMsg {\r\ntlCmdGoods?: CmdGoods[]\r\n}\r\n\r\ndeclare interface CmdMallCoinBuyReqMsg {\r\nmallId: number\r\nresId: number\r\ntype: number\r\n}\r\n\r\ndeclare interface CmdMallCoinBuyRspMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdMallGetDataReqMsg {\r\n\r\n}\r\n\r\ndeclare interface CmdMallGetDataRspMsg {\r\ncmdMall?: CmdMall[]\r\n}\r\n\r\ndeclare type EnumProtoName =\r\n| \"CmdFuBenType\"\r\n| \"CmdClientAppendDataModelEnum\"\r\n| \"CmdCommonState\"\r\n| \"CmdBattleType\"\r\n| \"CmdTaskType\"\r\n| \"LoginStatus\"\r\n| \"MessageId\"\r\n| \"CmdActivityTypeEnum\"\r\n| \"CmdShopType\"\r\n| \"CmdRankType\"\r\n| \"CmdRedDotType\"\r\n\r\ndeclare type MessageProtoName =\r\n| \"CmdGuide\"\r\n| \"CmdGuide_Key\"\r\n| \"CmdGuideDetail\"\r\n| \"CmdGem\"\r\n| \"CmdGem_Key\"\r\n| \"CmdGemPreview\"\r\n| \"CmdSkin\"\r\n| \"CmdSkin_Key\"\r\n| \"CmdFriendCache\"\r\n| \"CmdFriendCache_Key\"\r\n| \"CmdApplyFriendCache\"\r\n| \"CmdApplyFriendCache_Key\"\r\n| \"CmdBlackFriendCache\"\r\n| \"CmdBlackFriendCache_Key\"\r\n| \"CmdFriendsShowDetail\"\r\n| \"CmdFriendsWearEquip\"\r\n| \"CmdFund\"\r\n| \"CmdFund_Key\"\r\n| \"CmdFundDetail\"\r\n| \"CmdFundRewardState\"\r\n| \"CmdFuBen\"\r\n| \"CmdFuBen_Key\"\r\n| \"CmdFuBenDetail\"\r\n| \"CmdFuBenGoldDetail\"\r\n| \"CmdFuBenDailyDetail\"\r\n| \"CmdFuBenRewardState\"\r\n| \"CmdClientInfo\"\r\n| \"CmdTips\"\r\n| \"CmdCommonTips\"\r\n| \"CmdI18N\"\r\n| \"CmdClientAppendData\"\r\n| \"CmdClientAppendDataModel\"\r\n| \"CmdGoods\"\r\n| \"CmdGameExtend\"\r\n| \"CmdGoodsList\"\r\n| \"CmdRecharge\"\r\n| \"CmdGmInfo\"\r\n| \"CmdOpenCache\"\r\n| \"CmdOpenCache_Key\"\r\n| \"CmdGameCircle\"\r\n| \"CmdGameCircle_Key\"\r\n| \"CmdGuideRecordReqMsg\"\r\n| \"CmdGuideRecordRspMsg\"\r\n| \"CmdGuideReadReqMsg\"\r\n| \"CmdGuideReadRspMsg\"\r\n| \"CmdChapter\"\r\n| \"CmdChapter_Key\"\r\n| \"CmdChapterBoxState\"\r\n| \"CmdFuBenEnterReqMsg\"\r\n| \"CmdFuBenEnterRspMsg\"\r\n| \"CmdFuBenDailyGetRewardReqMsg\"\r\n| \"CmdFuBenDailyGetRewardRspMsg\"\r\n| \"CmdFuBenBuyTimesReqMsg\"\r\n| \"CmdFuBenBuyTimesRspMsg\"\r\n| \"CmdBattleLevel\"\r\n| \"CmdBattleLevel_Key\"\r\n| \"CmdBattleChapter\"\r\n| \"CmdBattleChapter_Key\"\r\n| \"CmdBattleData\"\r\n| \"CmdSpiritAnimalData\"\r\n| \"CmdBattleSkill\"\r\n| \"CmdBattleCard\"\r\n| \"CmdBattleBuff\"\r\n| \"CmdBattleAttr\"\r\n| \"CmdEnterBattleReqMsg\"\r\n| \"CmdEnterBattleRspMsg\"\r\n| \"CmdContinueNextBattleReqMsg\"\r\n| \"CmdContinueNextBattleRspMsg\"\r\n| \"CmdGemRefreshSkillReqMsg\"\r\n| \"CmdGemRefreshSkillRspMsg\"\r\n| \"CmdBattleDataUploadReqMsg\"\r\n| \"CmdBattleDataUploadRspMsg\"\r\n| \"CmdBattleResultReqMsg\"\r\n| \"CmdBattleResultRspMsg\"\r\n| \"CmdBattleStatisticsReqMsg\"\r\n| \"CmdBattleStatisticsRspMsg\"\r\n| \"CmdTakeStarRewardReqMsg\"\r\n| \"CmdTakeStarRewardRspMsg\"\r\n| \"CmdTakeChapterRewardReqMsg\"\r\n| \"CmdTakeChapterRewardRspMsg\"\r\n| \"CmdBattleSweepReqMsg\"\r\n| \"CmdBattleSweepRspMsg\"\r\n| \"CmdBattleRandomSkillReqMsg\"\r\n| \"CmdBattleRandomSkillRspMsg\"\r\n| \"CmdBattleSelectSkillReqMsg\"\r\n| \"CmdBattleSelectSkillRspMsg\"\r\n| \"CmdBattlePreviewReqMsg\"\r\n| \"CmdBattlePreviewRspMsg\"\r\n| \"CmdBattleCompleteInfoReqMsg\"\r\n| \"CmdBattleCompleteInfoRspMsg\"\r\n| \"CmdShareGetRewardReqMsg\"\r\n| \"CmdShareGetRewardRspMsg\"\r\n| \"CmdShareStateReqMsg\"\r\n| \"CmdShareStateRspMsg\"\r\n| \"CmdSpiritAnimalLevelUpReqMsg\"\r\n| \"CmdSpiritAnimalLevelUpRspMsg\"\r\n| \"CmdSpiritAnimalStepUpReqMsg\"\r\n| \"CmdSpiritAnimalStepUpRspMsg\"\r\n| \"CmdSpiritAnimalBattleReqMsg\"\r\n| \"CmdSpiritAnimalBattleRspMsg\"\r\n| \"CmdSpiritAnimalResetReqMsg\"\r\n| \"CmdSpiritAnimalResetRspMsg\"\r\n| \"CmdSpiritAnimalResetPreviewReqMsg\"\r\n| \"CmdSpiritAnimalResetPreviewRspMsg\"\r\n| \"CmdBuyStaState\"\r\n| \"CmdTask\"\r\n| \"CmdTask_Key\"\r\n| \"CmdSevenDayInfo\"\r\n| \"CmdSevenDayPoint\"\r\n| \"CmdSevenDayShop\"\r\n| \"CmdSigninInfo\"\r\n| \"CmdSevenDayInfoReqMsg\"\r\n| \"CmdSevenDayInfoRspMsg\"\r\n| \"CmdSevenDayShopBuyReqMsg\"\r\n| \"CmdSevenDayShopBuyRspMsg\"\r\n| \"CmdSevenDayTakeTaskRewardReqMsg\"\r\n| \"CmdSevenDayTakeTaskRewardRspMsg\"\r\n| \"CmdSevenDayTakePointRewardReqMsg\"\r\n| \"CmdSevenDayTakePointRewardRspMsg\"\r\n| \"CmdSigninInfoReqMsg\"\r\n| \"CmdSigninInfoRspMsg\"\r\n| \"CmdSigninTakeTaskRewardReqMsg\"\r\n| \"CmdSigninTakeTaskRewardRspMsg\"\r\n| \"CmdEveryDaySign\"\r\n| \"CmdEveryDaySign_Key\"\r\n| \"EveryDaySignInReward\"\r\n| \"CmdEveryDaySignInInfoReqMsg\"\r\n| \"CmdEveryDaySignInInfoRspMsg\"\r\n| \"CmdEveryDaySignInReqMsg\"\r\n| \"CmdEveryDaySignInRspMsg\"\r\n| \"CmdEveryDayGetRewardsReqMsg\"\r\n| \"CmdEveryDayGetRewardsRspMsg\"\r\n| \"CmdCastleSkill\"\r\n| \"CmdCastleSkill_Key\"\r\n| \"CmdCastleWorkShop\"\r\n| \"CmdCastleWorkShop_Key\"\r\n| \"CmdCastleApprentice\"\r\n| \"CmdCastleApprentice_Key\"\r\n| \"CmdCastleWall\"\r\n| \"CmdCastleWall_Key\"\r\n| \"CmdCastleWallSkill\"\r\n| \"CmdCastleWallSkill_Key\"\r\n| \"CmdRole\"\r\n| \"CmdRole_Key\"\r\n| \"CmdRoleEndBuyInfo\"\r\n| \"CmdFirstRechargeInfo\"\r\n| \"CmdAdvert\"\r\n| \"CmdAppletInfo\"\r\n| \"CmdGuanKaGift\"\r\n| \"CmdGuanKaGift_Key\"\r\n| \"CmdGuanKaGiftBroMsg\"\r\n| \"CmdChapterRewardReqMsg\"\r\n| \"CmdChapterRewardRspMsg\"\r\n| \"CmdBuyStaBuyReqMsg\"\r\n| \"CmdBuyStaBuyRspMsg\"\r\n| \"CmdBuyStaInfoReqMsg\"\r\n| \"CmdBuyStaInfoRspMsg\"\r\n| \"CmdLockMonster\"\r\n| \"CmdLockMonster_Key\"\r\n| \"CmdHallRole\"\r\n| \"CmdPoint\"\r\n| \"CmdRune\"\r\n| \"CmdRune_Key\"\r\n| \"CmdDayWeekMonthGift\"\r\n| \"CmdDayWeekMonthGift_Key\"\r\n| \"EveryDayGift\"\r\n| \"RechargeCount\"\r\n| \"ClientCmdData\"\r\n| \"ServerCmdData\"\r\n| \"CmdCache\"\r\n| \"CmdCacheUpdateByField\"\r\n| \"CmdCacheField\"\r\n| \"CmdInt32\"\r\n| \"CmdInt64\"\r\n| \"CmdDouble\"\r\n| \"CmdFloat\"\r\n| \"CmdDayWeekMonthGiftGetRewardReqMsg\"\r\n| \"CmdDayWeekMonthGiftGetRewardRspMsg\"\r\n| \"CmdDayWeekMonthGiftInfoReqMsg\"\r\n| \"CmdDayWeekMonthGiftInfoRspMsg\"\r\n| \"CmdDayWeekMonthGiftFreeBuyReqMsg\"\r\n| \"CmdDayWeekMonthGiftFreeBuyRspMsg\"\r\n| \"CmdMail\"\r\n| \"CmdMailSimple\"\r\n| \"CmdRoll\"\r\n| \"CmdRollBroMsg\"\r\n| \"CmdBattleToken\"\r\n| \"CmdBattleToken_Key\"\r\n| \"CmdBattleTokenMessage\"\r\n| \"CmdBattleTokenOpenReqMsg\"\r\n| \"CmdBattleTokenOpenRspMsg\"\r\n| \"CmdBattleTokenTakeRewardReqMsg\"\r\n| \"CmdBattleTokenTakeRewardRspMsg\"\r\n| \"CmdBattleTokenResetReqMsg\"\r\n| \"CmdBattleTokenResetRspMsg\"\r\n| \"CmdActivity\"\r\n| \"CmdActivity_Key\"\r\n| \"CmdActivityDetail\"\r\n| \"CmdSkinActDetail\"\r\n| \"CmdDayChallengeActDetail\"\r\n| \"CmdMonthCardActDetail\"\r\n| \"CmdRechargeGiftActDetail\"\r\n| \"CmdDayChallengeTask\"\r\n| \"CmdRechargeGiftAct2Detail\"\r\n| \"CmdRechargeGiftAct3Detail\"\r\n| \"CmdActivitySimple\"\r\n| \"CmdActivitySimple_Key\"\r\n| \"CmdActivityTime\"\r\n| \"CmdActivityGoods\"\r\n| \"CmdSkinPlayer\"\r\n| \"CmdSkinShop\"\r\n| \"CmdTakeGameCircleRewardReqMsg\"\r\n| \"CmdTakeGameCircleRewardRspMsg\"\r\n| \"CmdCastleSkillUpLevelReqMsg\"\r\n| \"CmdCastleSkillUpLevelRspMsg\"\r\n| \"CmdCastleSkillResearchReqMsg\"\r\n| \"CmdCastleSkillResearchRspMsg\"\r\n| \"CmdCastleWorkShopOpenReqMsg\"\r\n| \"CmdCastleWorkShopOpenRspMsg\"\r\n| \"CmdCastleWorkShopUpLevelReqMsg\"\r\n| \"CmdCastleWorkShopUpLevelRspMsg\"\r\n| \"CmdCastleWorkShopSpeedReqMsg\"\r\n| \"CmdCastleWorkShopSpeedRspMsg\"\r\n| \"CmdCastleWorkShopTakeReqMsg\"\r\n| \"CmdCastleWorkShopTakeRspMsg\"\r\n| \"CmdCastleApprenticeUpReqMsg\"\r\n| \"CmdCastleApprenticeUpRspMsg\"\r\n| \"CmdCastleApprenticeUpLevelReqMsg\"\r\n| \"CmdCastleApprenticeUpLevelRspMsg\"\r\n| \"CmdCastleWallUpLevelReqMsg\"\r\n| \"CmdCastleWallUpLevelRspMsg\"\r\n| \"CmdCastleWallQualityUpReqMsg\"\r\n| \"CmdCastleWallQualityUpRspMsg\"\r\n| \"CmdCastleWallSelectReqMsg\"\r\n| \"CmdCastleWallSelectRspMsg\"\r\n| \"CmdCastleWallUpSkillLevelReqMsg\"\r\n| \"CmdCastleWallUpSkillLevelRspMsg\"\r\n| \"CmdItem\"\r\n| \"CmdItem_Key\"\r\n| \"CmdItemUseReqMsg\"\r\n| \"CmdItemUseRspMsg\"\r\n| \"CmdItemBoxUseReqMsg\"\r\n| \"CmdItemBoxUseRspMsg\"\r\n| \"CmdGemOnReqMsg\"\r\n| \"CmdGemOnRspMsg\"\r\n| \"CmdGemReplaceReqMsg\"\r\n| \"CmdGemReplaceRspMsg\"\r\n| \"CmdGemDownReqMsg\"\r\n| \"CmdGemDownRspMsg\"\r\n| \"CmdGemRefineReqMsg\"\r\n| \"CmdGemRefineRspMsg\"\r\n| \"CmdGemRefineSaveReqMsg\"\r\n| \"CmdGemRefineSaveRspMsg\"\r\n| \"CmdGemComposeReqMsg\"\r\n| \"CmdGemComposeRspMsg\"\r\n| \"CmdGemLockReqMsg\"\r\n| \"CmdGemLockRspMsg\"\r\n| \"CmdGemComposePreviewReqMsg\"\r\n| \"CmdGemComposePreviewRspMsg\"\r\n| \"CmdMailListReqMsg\"\r\n| \"CmdMailListRspMsg\"\r\n| \"CmdMailReadReqMsg\"\r\n| \"CmdMailReadRspMsg\"\r\n| \"CmdMailDeleteReqMsg\"\r\n| \"CmdMailDeleteRspMsg\"\r\n| \"CmdMailTakeRewardReqMsg\"\r\n| \"CmdMailTakeRewardRspMsg\"\r\n| \"CmdCardCache\"\r\n| \"CmdCardCache_Key\"\r\n| \"CmdCardBuyReqMsg\"\r\n| \"CmdCardBuyRspMsg\"\r\n| \"CmdHero\"\r\n| \"CmdHero_Key\"\r\n| \"CmdHeroUnlockInfo\"\r\n| \"CmdHeroChangeReqMsg\"\r\n| \"CmdHeroChangeRspMsg\"\r\n| \"CmdHeroLevelUpReqMsg\"\r\n| \"CmdHeroLevelUpRspMsg\"\r\n| \"CmdHeroLevelUpOneKeyReqMsg\"\r\n| \"CmdHeroLevelUpOneKeyRspMsg\"\r\n| \"CmdHeroOpenReqMsg\"\r\n| \"CmdHeroOpenRspMsg\"\r\n| \"CmdHeroUnlockGetInfoReqMsg\"\r\n| \"CmdHeroUnlockGetInfoRspMsg\"\r\n| \"CmdHeroUnlockRewardReqMsg\"\r\n| \"CmdHeroUnlockRewardRspMsg\"\r\n| \"CmdSpecialGiftCache\"\r\n| \"CmdSpecialGiftCache_Key\"\r\n| \"CmdTakeLockMonsterBoxReqMsg\"\r\n| \"CmdTakeLockMonsterBoxRspMsg\"\r\n| \"CmdGemInfo\"\r\n| \"CmdEquipPage\"\r\n| \"CmdEquipPage_Key\"\r\n| \"CmdEquip\"\r\n| \"CmdRoleLoginReqMsg\"\r\n| \"CmdRoleLoginRspMsg\"\r\n| \"CmdRoleChangeNameReqMsg\"\r\n| \"CmdRoleChangeNameRspMsg\"\r\n| \"CmdRoleChangeImgReqMsg\"\r\n| \"CmdRoleChangeImgRspMsg\"\r\n| \"CmdRoleUploadPlatformImgReqMsg\"\r\n| \"CmdRoleUploadPlatformImgRspMsg\"\r\n| \"CmdRoleChangeHeadFrameReqMsg\"\r\n| \"CmdRoleChangeHeadFrameRspMsg\"\r\n| \"CmdEndBuyInfoReqMsg\"\r\n| \"CmdEndBuyInfoRspMsg\"\r\n| \"CmdEndBuyReqMsg\"\r\n| \"CmdEndBuyRspMsg\"\r\n| \"CmdRoleEndUpdateReqMsg\"\r\n| \"CmdRoleEndUpdateRspMsg\"\r\n| \"CmdMiningBuyReqMsg\"\r\n| \"CmdMiningBuyRspMsg\"\r\n| \"CmdCodeExchangeReqMsg\"\r\n| \"CmdCodeExchangeRspMsg\"\r\n| \"CmdRoleTakeFirstChargeReqMsg\"\r\n| \"CmdRoleTakeFirstChargeRspMsg\"\r\n| \"CmdRoleWatchAdvertReqMsg\"\r\n| \"CmdRoleWatchAdvertRspMsg\"\r\n| \"CmdRoleInfoViewReqMsg\"\r\n| \"CmdRoleInfoViewRspMsg\"\r\n| \"CmdRoleDouYinRewardReqMsg\"\r\n| \"CmdRoleDouYinRewardRspMsg\"\r\n| \"CmdRoleAppletShareReqMsg\"\r\n| \"CmdRoleAppletShareRspMsg\"\r\n| \"CmdRoleAppletShareRewardReqMsg\"\r\n| \"CmdRoleAppletShareRewardRspMsg\"\r\n| \"CmdRoleGuanKaGiftSelectReqMsg\"\r\n| \"CmdRoleGuanKaGiftSelectRspMsg\"\r\n| \"CmdRoleGuanKaGiftBuyReqMsg\"\r\n| \"CmdRoleGuanKaGiftBuyRspMsg\"\r\n| \"CmdShop\"\r\n| \"CmdShop_Key\"\r\n| \"CmdShopGoods\"\r\n| \"CmdContinuityGiftInfo\"\r\n| \"CmdGoodReplace\"\r\n| \"CmdShopBuyReqMsg\"\r\n| \"CmdShopBuyRspMsg\"\r\n| \"CmdContinuityGiftInfoReqMsg\"\r\n| \"CmdContinuityGiftInfoRspMsg\"\r\n| \"CmdContinuityGiftRewardReqMsg\"\r\n| \"CmdContinuityGiftRewardRspMsg\"\r\n| \"CmdContinuityGiftFinishRewardReqMsg\"\r\n| \"CmdContinuityGiftFinishRewardRspMsg\"\r\n| \"CmdTakeSpiritAnimalBoxReqMsg\"\r\n| \"CmdTakeSpiritAnimalBoxRspMsg\"\r\n| \"CmdHang\"\r\n| \"CmdHang_Key\"\r\n| \"CmdRankData\"\r\n| \"CmdRankRole\"\r\n| \"CmdRankItem\"\r\n| \"CmdRankItem_Key\"\r\n| \"CmdGetRankReqMsg\"\r\n| \"CmdGetRankRspMsg\"\r\n| \"CmdGetHallRoleListReqMsg\"\r\n| \"CmdGetHallRoleListRspMsg\"\r\n| \"CmdMall\"\r\n| \"CmdMall_Key\"\r\n| \"CmdMallDetail\"\r\n| \"CmdMallSEquipDetail\"\r\n| \"CmdMallLuckDrawDetail\"\r\n| \"CmdMallDailyShopDetail\"\r\n| \"CmdMallChapterGiftDetail\"\r\n| \"CmdMallChargeDetail\"\r\n| \"CmdMallCoinDetail\"\r\n| \"CmdCacheBroMsg\"\r\n| \"CmdRoleOfflineBroMsg\"\r\n| \"CmdRechargeBroMsg\"\r\n| \"CmdModifyItemBroMsg\"\r\n| \"CmdBattleBroMsg\"\r\n| \"CmdMallBroMsg\"\r\n| \"CmdEndBroMsg\"\r\n| \"CmdTakeDayChallengeActRewardReqMsg\"\r\n| \"CmdTakeDayChallengeActRewardRspMsg\"\r\n| \"CmdTakeMonthCardActRewardReqMsg\"\r\n| \"CmdTakeMonthCardActRewardRspMsg\"\r\n| \"CmdTakeRechargeGiftActRewardReqMsg\"\r\n| \"CmdTakeRechargeGiftActRewardRspMsg\"\r\n| \"CmdActivityJsonReqMsg\"\r\n| \"CmdActivityJsonRspMsg\"\r\n| \"CmdActivityLimitTimeInfoReqMsg\"\r\n| \"CmdActivityLimitTimeInfoRspMsg\"\r\n| \"CmdActivityLimitTimeTakeReqMsg\"\r\n| \"CmdActivityLimitTimeTakeRspMsg\"\r\n| \"CmdBuySkinReqMsg\"\r\n| \"CmdBuySkinRspMsg\"\r\n| \"CmdActivityLimitSkinInfoReqMsg\"\r\n| \"CmdActivityLimitSkinInfoRspMsg\"\r\n| \"CmdActivityLimitSkinBuyReqMsg\"\r\n| \"CmdActivityLimitSkinBuyRspMsg\"\r\n| \"CmdFriendsApplyReqMsg\"\r\n| \"CmdFriendsApplyRspMsg\"\r\n| \"CmdFriendsAddReqMsg\"\r\n| \"CmdFriendsAddRspMsg\"\r\n| \"CmdFriendsRefuseReqMsg\"\r\n| \"CmdFriendsRefuseRspMsg\"\r\n| \"CmdFriendsRemoveReqMsg\"\r\n| \"CmdFriendsRemoveRspMsg\"\r\n| \"CmdFriendsBlackReqMsg\"\r\n| \"CmdFriendsBlackRspMsg\"\r\n| \"CmdFriendsSearchReqMsg\"\r\n| \"CmdFriendsSearchRspMsg\"\r\n| \"CmdFriendGetEndReqMsg\"\r\n| \"CmdFriendGetEndRspMsg\"\r\n| \"CmdFriendGetEndOneKeyReqMsg\"\r\n| \"CmdFriendGetEndOneKeyRspMsg\"\r\n| \"CmdFriendSendEndReqMsg\"\r\n| \"CmdFriendSendEndRspMsg\"\r\n| \"CmdFriendSendEndOneKeyReqMsg\"\r\n| \"CmdFriendSendEndOneKeyRspMsg\"\r\n| \"CmdFriendAgreeOneKeyReqMsg\"\r\n| \"CmdFriendAgreeOneKeyRspMsg\"\r\n| \"CmdFriendRefuseOneKeyReqMsg\"\r\n| \"CmdFriendRefuseOneKeyRspMsg\"\r\n| \"CmdFriendDetailReqMsg\"\r\n| \"CmdFriendDetailRspMsg\"\r\n| \"CmdFriendLikeReqMsg\"\r\n| \"CmdFriendLikeRspMsg\"\r\n| \"CmdFriendBlackRemoveReqMsg\"\r\n| \"CmdFriendBlackRemoveRspMsg\"\r\n| \"CmdFriendRefreshSuggestReqMsg\"\r\n| \"CmdFriendRefreshSuggestRspMsg\"\r\n| \"CmdHangGetRewardReqMsg\"\r\n| \"CmdHangGetRewardRspMsg\"\r\n| \"CmdHangQuickReqMsg\"\r\n| \"CmdHangQuickRspMsg\"\r\n| \"CmdHangTimeReqMsg\"\r\n| \"CmdHangTimeRspMsg\"\r\n| \"CmdSystemSyncTimeReqMsg\"\r\n| \"CmdSystemSyncTimeRspMsg\"\r\n| \"CmdSystemGmReqMsg\"\r\n| \"CmdSystemGmRspMsg\"\r\n| \"CmdGmListReqMsg\"\r\n| \"CmdGmListRspMsg\"\r\n| \"CmdRecordClientAniReqMsg\"\r\n| \"CmdRecordClientAniRspMsg\"\r\n| \"CmdGetAllPlayClientAniReqMsg\"\r\n| \"CmdGetAllPlayClientAniRspMsg\"\r\n| \"CmdCode\"\r\n| \"CmdCode_Key\"\r\n| \"CmdBreach\"\r\n| \"CmdBreach_Key\"\r\n| \"CmdChangeUsedSkinReqMsg\"\r\n| \"CmdChangeUsedSkinRspMsg\"\r\n| \"CmdRunePutOnReqMsg\"\r\n| \"CmdRunePutOnRspMsg\"\r\n| \"CmdRuneTakeOffReqMsg\"\r\n| \"CmdRuneTakeOffRspMsg\"\r\n| \"CmdRuneReplaceReqMsg\"\r\n| \"CmdRuneReplaceRspMsg\"\r\n| \"CmdRuneSynthesisReqMsg\"\r\n| \"CmdRuneSynthesisRspMsg\"\r\n| \"CmdRuneSynthesisOneKeyReqMsg\"\r\n| \"CmdRuneSynthesisOneKeyRspMsg\"\r\n| \"CmdRedDotCache\"\r\n| \"CmdRedDotCache_Key\"\r\n| \"CmdGoodsRedDot\"\r\n| \"CmdUpdateRedDotReqMsg\"\r\n| \"CmdUpdateRedDotRspMsg\"\r\n| \"CmdGoodsRedDotDispelReqMsg\"\r\n| \"CmdGoodsRedDotDispelRspMsg\"\r\n| \"CmdEquipUpLevelAllReqMsg\"\r\n| \"CmdEquipUpLevelAllRspMsg\"\r\n| \"CmdEquipUpLevelReqMsg\"\r\n| \"CmdEquipUpLevelRspMsg\"\r\n| \"CmdEquipSelectPageReqMsg\"\r\n| \"CmdEquipSelectPageRspMsg\"\r\n| \"CmdEquipChangePageNameReqMsg\"\r\n| \"CmdEquipChangePageNameRspMsg\"\r\n| \"CmdFirstRecharge\"\r\n| \"CmdFirstRecharge_Key\"\r\n| \"CmdFundGetInfoReqMsg\"\r\n| \"CmdFundGetInfoRspMsg\"\r\n| \"CmdFundGetRewardReqMsg\"\r\n| \"CmdFundGetRewardRspMsg\"\r\n| \"CmdSpiritAnimal\"\r\n| \"CmdSpiritAnimal_Key\"\r\n| \"CmdSecretRealm\"\r\n| \"CmdSecretRealm_Key\"\r\n| \"CmdSecretRealmScore\"\r\n| \"CmdShare\"\r\n| \"CmdShare_Key\"\r\n| \"CmdBreachReqMsg\"\r\n| \"CmdBreachRspMsg\"\r\n| \"CmdFirstRechargeRewardReqMsg\"\r\n| \"CmdFirstRechargeRewardRspMsg\"\r\n| \"CmdFirstRechargeOpenReqMsg\"\r\n| \"CmdFirstRechargeOpenRspMsg\"\r\n| \"CmdMallSEquipDrawReqMsg\"\r\n| \"CmdMallSEquipDrawRspMsg\"\r\n| \"CmdMallLuckDrawReqMsg\"\r\n| \"CmdMallLuckDrawRspMsg\"\r\n| \"CmdMallDailyShopBuyReqMsg\"\r\n| \"CmdMallDailyShopBuyRspMsg\"\r\n| \"CmdMallCoinBuyReqMsg\"\r\n| \"CmdMallCoinBuyRspMsg\"\r\n| \"CmdMallGetDataReqMsg\"\r\n| \"CmdMallGetDataRspMsg\"\r\n\r\ndeclare type ReqMsgProtoName =\r\n| \"CmdGuideRecordReqMsg\"\r\n| \"CmdGuideReadReqMsg\"\r\n| \"CmdFuBenEnterReqMsg\"\r\n| \"CmdFuBenDailyGetRewardReqMsg\"\r\n| \"CmdFuBenBuyTimesReqMsg\"\r\n| \"CmdEnterBattleReqMsg\"\r\n| \"CmdContinueNextBattleReqMsg\"\r\n| \"CmdGemRefreshSkillReqMsg\"\r\n| \"CmdBattleDataUploadReqMsg\"\r\n| \"CmdBattleResultReqMsg\"\r\n| \"CmdBattleStatisticsReqMsg\"\r\n| \"CmdTakeStarRewardReqMsg\"\r\n| \"CmdTakeChapterRewardReqMsg\"\r\n| \"CmdBattleSweepReqMsg\"\r\n| \"CmdBattleRandomSkillReqMsg\"\r\n| \"CmdBattleSelectSkillReqMsg\"\r\n| \"CmdBattlePreviewReqMsg\"\r\n| \"CmdBattleCompleteInfoReqMsg\"\r\n| \"CmdShareGetRewardReqMsg\"\r\n| \"CmdShareStateReqMsg\"\r\n| \"CmdSpiritAnimalLevelUpReqMsg\"\r\n| \"CmdSpiritAnimalStepUpReqMsg\"\r\n| \"CmdSpiritAnimalBattleReqMsg\"\r\n| \"CmdSpiritAnimalResetReqMsg\"\r\n| \"CmdSpiritAnimalResetPreviewReqMsg\"\r\n| \"CmdSevenDayInfoReqMsg\"\r\n| \"CmdSevenDayShopBuyReqMsg\"\r\n| \"CmdSevenDayTakeTaskRewardReqMsg\"\r\n| \"CmdSevenDayTakePointRewardReqMsg\"\r\n| \"CmdSigninInfoReqMsg\"\r\n| \"CmdSigninTakeTaskRewardReqMsg\"\r\n| \"CmdEveryDaySignInInfoReqMsg\"\r\n| \"CmdEveryDaySignInReqMsg\"\r\n| \"CmdEveryDayGetRewardsReqMsg\"\r\n| \"CmdChapterRewardReqMsg\"\r\n| \"CmdBuyStaBuyReqMsg\"\r\n| \"CmdBuyStaInfoReqMsg\"\r\n| \"CmdDayWeekMonthGiftGetRewardReqMsg\"\r\n| \"CmdDayWeekMonthGiftInfoReqMsg\"\r\n| \"CmdDayWeekMonthGiftFreeBuyReqMsg\"\r\n| \"CmdBattleTokenOpenReqMsg\"\r\n| \"CmdBattleTokenTakeRewardReqMsg\"\r\n| \"CmdBattleTokenResetReqMsg\"\r\n| \"CmdTakeGameCircleRewardReqMsg\"\r\n| \"CmdCastleSkillUpLevelReqMsg\"\r\n| \"CmdCastleSkillResearchReqMsg\"\r\n| \"CmdCastleWorkShopOpenReqMsg\"\r\n| \"CmdCastleWorkShopUpLevelReqMsg\"\r\n| \"CmdCastleWorkShopSpeedReqMsg\"\r\n| \"CmdCastleWorkShopTakeReqMsg\"\r\n| \"CmdCastleApprenticeUpReqMsg\"\r\n| \"CmdCastleApprenticeUpLevelReqMsg\"\r\n| \"CmdCastleWallUpLevelReqMsg\"\r\n| \"CmdCastleWallQualityUpReqMsg\"\r\n| \"CmdCastleWallSelectReqMsg\"\r\n| \"CmdCastleWallUpSkillLevelReqMsg\"\r\n| \"CmdItemUseReqMsg\"\r\n| \"CmdItemBoxUseReqMsg\"\r\n| \"CmdGemOnReqMsg\"\r\n| \"CmdGemReplaceReqMsg\"\r\n| \"CmdGemDownReqMsg\"\r\n| \"CmdGemRefineReqMsg\"\r\n| \"CmdGemRefineSaveReqMsg\"\r\n| \"CmdGemComposeReqMsg\"\r\n| \"CmdGemLockReqMsg\"\r\n| \"CmdGemComposePreviewReqMsg\"\r\n| \"CmdMailListReqMsg\"\r\n| \"CmdMailReadReqMsg\"\r\n| \"CmdMailDeleteReqMsg\"\r\n| \"CmdMailTakeRewardReqMsg\"\r\n| \"CmdCardBuyReqMsg\"\r\n| \"CmdHeroChangeReqMsg\"\r\n| \"CmdHeroLevelUpReqMsg\"\r\n| \"CmdHeroLevelUpOneKeyReqMsg\"\r\n| \"CmdHeroOpenReqMsg\"\r\n| \"CmdHeroUnlockGetInfoReqMsg\"\r\n| \"CmdHeroUnlockRewardReqMsg\"\r\n| \"CmdTakeLockMonsterBoxReqMsg\"\r\n| \"CmdRoleLoginReqMsg\"\r\n| \"CmdRoleChangeNameReqMsg\"\r\n| \"CmdRoleChangeImgReqMsg\"\r\n| \"CmdRoleUploadPlatformImgReqMsg\"\r\n| \"CmdRoleChangeHeadFrameReqMsg\"\r\n| \"CmdEndBuyInfoReqMsg\"\r\n| \"CmdEndBuyReqMsg\"\r\n| \"CmdRoleEndUpdateReqMsg\"\r\n| \"CmdMiningBuyReqMsg\"\r\n| \"CmdCodeExchangeReqMsg\"\r\n| \"CmdRoleTakeFirstChargeReqMsg\"\r\n| \"CmdRoleWatchAdvertReqMsg\"\r\n| \"CmdRoleInfoViewReqMsg\"\r\n| \"CmdRoleDouYinRewardReqMsg\"\r\n| \"CmdRoleAppletShareReqMsg\"\r\n| \"CmdRoleAppletShareRewardReqMsg\"\r\n| \"CmdRoleGuanKaGiftSelectReqMsg\"\r\n| \"CmdRoleGuanKaGiftBuyReqMsg\"\r\n| \"CmdShopBuyReqMsg\"\r\n| \"CmdContinuityGiftInfoReqMsg\"\r\n| \"CmdContinuityGiftRewardReqMsg\"\r\n| \"CmdContinuityGiftFinishRewardReqMsg\"\r\n| \"CmdTakeSpiritAnimalBoxReqMsg\"\r\n| \"CmdGetRankReqMsg\"\r\n| \"CmdGetHallRoleListReqMsg\"\r\n| \"CmdTakeDayChallengeActRewardReqMsg\"\r\n| \"CmdTakeMonthCardActRewardReqMsg\"\r\n| \"CmdTakeRechargeGiftActRewardReqMsg\"\r\n| \"CmdActivityJsonReqMsg\"\r\n| \"CmdActivityLimitTimeInfoReqMsg\"\r\n| \"CmdActivityLimitTimeTakeReqMsg\"\r\n| \"CmdBuySkinReqMsg\"\r\n| \"CmdActivityLimitSkinInfoReqMsg\"\r\n| \"CmdActivityLimitSkinBuyReqMsg\"\r\n| \"CmdFriendsApplyReqMsg\"\r\n| \"CmdFriendsAddReqMsg\"\r\n| \"CmdFriendsRefuseReqMsg\"\r\n| \"CmdFriendsRemoveReqMsg\"\r\n| \"CmdFriendsBlackReqMsg\"\r\n| \"CmdFriendsSearchReqMsg\"\r\n| \"CmdFriendGetEndReqMsg\"\r\n| \"CmdFriendGetEndOneKeyReqMsg\"\r\n| \"CmdFriendSendEndReqMsg\"\r\n| \"CmdFriendSendEndOneKeyReqMsg\"\r\n| \"CmdFriendAgreeOneKeyReqMsg\"\r\n| \"CmdFriendRefuseOneKeyReqMsg\"\r\n| \"CmdFriendDetailReqMsg\"\r\n| \"CmdFriendLikeReqMsg\"\r\n| \"CmdFriendBlackRemoveReqMsg\"\r\n| \"CmdFriendRefreshSuggestReqMsg\"\r\n| \"CmdHangGetRewardReqMsg\"\r\n| \"CmdHangQuickReqMsg\"\r\n| \"CmdHangTimeReqMsg\"\r\n| \"CmdSystemSyncTimeReqMsg\"\r\n| \"CmdSystemGmReqMsg\"\r\n| \"CmdGmListReqMsg\"\r\n| \"CmdRecordClientAniReqMsg\"\r\n| \"CmdGetAllPlayClientAniReqMsg\"\r\n| \"CmdChangeUsedSkinReqMsg\"\r\n| \"CmdRunePutOnReqMsg\"\r\n| \"CmdRuneTakeOffReqMsg\"\r\n| \"CmdRuneReplaceReqMsg\"\r\n| \"CmdRuneSynthesisReqMsg\"\r\n| \"CmdRuneSynthesisOneKeyReqMsg\"\r\n| \"CmdUpdateRedDotReqMsg\"\r\n| \"CmdGoodsRedDotDispelReqMsg\"\r\n| \"CmdEquipUpLevelAllReqMsg\"\r\n| \"CmdEquipUpLevelReqMsg\"\r\n| \"CmdEquipSelectPageReqMsg\"\r\n| \"CmdEquipChangePageNameReqMsg\"\r\n| \"CmdFundGetInfoReqMsg\"\r\n| \"CmdFundGetRewardReqMsg\"\r\n| \"CmdBreachReqMsg\"\r\n| \"CmdFirstRechargeRewardReqMsg\"\r\n| \"CmdFirstRechargeOpenReqMsg\"\r\n| \"CmdMallSEquipDrawReqMsg\"\r\n| \"CmdMallLuckDrawReqMsg\"\r\n| \"CmdMallDailyShopBuyReqMsg\"\r\n| \"CmdMallCoinBuyReqMsg\"\r\n| \"CmdMallGetDataReqMsg\"\r\n\r\ndeclare interface protoCacheGen{\r\nCmdActivity:{c:CmdActivity, c_k:CmdActivity_Key}\r\nCmdActivitySimple:{c:CmdActivitySimple, c_k:CmdActivitySimple_Key}\r\nCmdApplyFriendCache:{c:CmdApplyFriendCache, c_k:CmdApplyFriendCache_Key}\r\nCmdBattleChapter:{c:CmdBattleChapter, c_k:CmdBattleChapter_Key}\r\nCmdBattleLevel:{c:CmdBattleLevel, c_k:CmdBattleLevel_Key}\r\nCmdBattleToken:{c:CmdBattleToken, c_k:CmdBattleToken_Key}\r\nCmdBlackFriendCache:{c:CmdBlackFriendCache, c_k:CmdBlackFriendCache_Key}\r\nCmdBreach:{c:CmdBreach, c_k:CmdBreach_Key}\r\nCmdCardCache:{c:CmdCardCache, c_k:CmdCardCache_Key}\r\nCmdCastleApprentice:{c:CmdCastleApprentice, c_k:CmdCastleApprentice_Key}\r\nCmdCastleSkill:{c:CmdCastleSkill, c_k:CmdCastleSkill_Key}\r\nCmdCastleWall:{c:CmdCastleWall, c_k:CmdCastleWall_Key}\r\nCmdCastleWallSkill:{c:CmdCastleWallSkill, c_k:CmdCastleWallSkill_Key}\r\nCmdCastleWorkShop:{c:CmdCastleWorkShop, c_k:CmdCastleWorkShop_Key}\r\nCmdChapter:{c:CmdChapter, c_k:CmdChapter_Key}\r\nCmdCode:{c:CmdCode, c_k:CmdCode_Key}\r\nCmdDayWeekMonthGift:{c:CmdDayWeekMonthGift, c_k:CmdDayWeekMonthGift_Key}\r\nCmdEquipPage:{c:CmdEquipPage, c_k:CmdEquipPage_Key}\r\nCmdEveryDaySign:{c:CmdEveryDaySign, c_k:CmdEveryDaySign_Key}\r\nCmdFirstRecharge:{c:CmdFirstRecharge, c_k:CmdFirstRecharge_Key}\r\nCmdFriendCache:{c:CmdFriendCache, c_k:CmdFriendCache_Key}\r\nCmdFuBen:{c:CmdFuBen, c_k:CmdFuBen_Key}\r\nCmdFund:{c:CmdFund, c_k:CmdFund_Key}\r\nCmdGameCircle:{c:CmdGameCircle, c_k:CmdGameCircle_Key}\r\nCmdGem:{c:CmdGem, c_k:CmdGem_Key}\r\nCmdGuanKaGift:{c:CmdGuanKaGift, c_k:CmdGuanKaGift_Key}\r\nCmdGuide:{c:CmdGuide, c_k:CmdGuide_Key}\r\nCmdHang:{c:CmdHang, c_k:CmdHang_Key}\r\nCmdHero:{c:CmdHero, c_k:CmdHero_Key}\r\nCmdItem:{c:CmdItem, c_k:CmdItem_Key}\r\nCmdLockMonster:{c:CmdLockMonster, c_k:CmdLockMonster_Key}\r\nCmdMall:{c:CmdMall, c_k:CmdMall_Key}\r\nCmdOpenCache:{c:CmdOpenCache, c_k:CmdOpenCache_Key}\r\nCmdRankItem:{c:CmdRankItem, c_k:CmdRankItem_Key}\r\nCmdRedDotCache:{c:CmdRedDotCache, c_k:CmdRedDotCache_Key}\r\nCmdRole:{c:CmdRole, c_k:CmdRole_Key}\r\nCmdRune:{c:CmdRune, c_k:CmdRune_Key}\r\nCmdSecretRealm:{c:CmdSecretRealm, c_k:CmdSecretRealm_Key}\r\nCmdShare:{c:CmdShare, c_k:CmdShare_Key}\r\nCmdShop:{c:CmdShop, c_k:CmdShop_Key}\r\nCmdSkin:{c:CmdSkin, c_k:CmdSkin_Key}\r\nCmdSpecialGiftCache:{c:CmdSpecialGiftCache, c_k:CmdSpecialGiftCache_Key}\r\nCmdSpiritAnimal:{c:CmdSpiritAnimal, c_k:CmdSpiritAnimal_Key}\r\nCmdTask:{c:CmdTask, c_k:CmdTask_Key}\r\n}\r\n"]], 0, 0, [], [], []]