window.__require = function e(t, n, r) {
  function s(o, u) {
    if (!n[o]) {
      if (!t[o]) {
        var b = o.split("/");
        b = b[b.length - 1];
        if (!t[b]) {
          var a = "function" == typeof __require && __require;
          if (!u && a) return a(b, !0);
          if (i) return i(b, !0);
          throw new Error("Cannot find module '" + o + "'");
        }
        o = b;
      }
      var f = n[o] = {
        exports: {}
      };
      t[o][0].call(f.exports, function(e) {
        var n = t[o][1][e];
        return s(n || e);
      }, f, f.exports, e, t, n, r);
    }
    return n[o].exports;
  }
  var i = "function" == typeof __require && __require;
  for (var o = 0; o < r.length; o++) s(r[o]);
  return s;
}({
  SdkHuiXin: [ function(require, module, exports) {
    "use strict";
    cc._RF.push(module, "86fc8iEURNINb/LtVwv/aw9", "SdkHuiXin");
    "use strict";
    var __extends = this && this.__extends || function() {
      var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || {
          __proto__: []
        } instanceof Array && function(d, b) {
          d.__proto__ = b;
        } || function(d, b) {
          for (var p in b) Object.prototype.hasOwnProperty.call(b, p) && (d[p] = b[p]);
        };
        return extendStatics(d, b);
      };
      return function(d, b) {
        extendStatics(d, b);
        function __() {
          this.constructor = d;
        }
        d.prototype = null === b ? Object.create(b) : (__.prototype = b.prototype, new __());
      };
    }();
    var __decorate = this && this.__decorate || function(decorators, target, key, desc) {
      var c = arguments.length, r = c < 3 ? target : null === desc ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
      if ("object" === typeof Reflect && "function" === typeof Reflect.decorate) r = Reflect.decorate(decorators, target, key, desc); else for (var i = decorators.length - 1; i >= 0; i--) (d = decorators[i]) && (r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r);
      return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __awaiter = this && this.__awaiter || function(thisArg, _arguments, P, generator) {
      function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
          resolve(value);
        });
      }
      return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
          try {
            step(generator.next(value));
          } catch (e) {
            reject(e);
          }
        }
        function rejected(value) {
          try {
            step(generator["throw"](value));
          } catch (e) {
            reject(e);
          }
        }
        function step(result) {
          result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
      });
    };
    var __generator = this && this.__generator || function(thisArg, body) {
      var _ = {
        label: 0,
        sent: function() {
          if (1 & t[0]) throw t[1];
          return t[1];
        },
        trys: [],
        ops: []
      }, f, y, t, g;
      return g = {
        next: verb(0),
        throw: verb(1),
        return: verb(2)
      }, "function" === typeof Symbol && (g[Symbol.iterator] = function() {
        return this;
      }), g;
      function verb(n) {
        return function(v) {
          return step([ n, v ]);
        };
      }
      function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
          if (f = 1, y && (t = 2 & op[0] ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 
          0) : y.next) && !(t = t.call(y, op[1])).done) return t;
          (y = 0, t) && (op = [ 2 & op[0], t.value ]);
          switch (op[0]) {
           case 0:
           case 1:
            t = op;
            break;

           case 4:
            _.label++;
            return {
              value: op[1],
              done: false
            };

           case 5:
            _.label++;
            y = op[1];
            op = [ 0 ];
            continue;

           case 7:
            op = _.ops.pop();
            _.trys.pop();
            continue;

           default:
            if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (6 === op[0] || 2 === op[0])) {
              _ = 0;
              continue;
            }
            if (3 === op[0] && (!t || op[1] > t[0] && op[1] < t[3])) {
              _.label = op[1];
              break;
            }
            if (6 === op[0] && _.label < t[1]) {
              _.label = t[1];
              t = op;
              break;
            }
            if (t && _.label < t[2]) {
              _.label = t[2];
              _.ops.push(op);
              break;
            }
            t[2] && _.ops.pop();
            _.trys.pop();
            continue;
          }
          op = body.call(thisArg, _);
        } catch (e) {
          op = [ 6, e ];
          y = 0;
        } finally {
          f = t = 0;
        }
        if (5 & op[0]) throw op[1];
        return {
          value: op[0] ? op[1] : void 0,
          done: true
        };
      }
    };
    var __values = this && this.__values || function(o) {
      var s = "function" === typeof Symbol && Symbol.iterator, m = s && o[s], i = 0;
      if (m) return m.call(o);
      if (o && "number" === typeof o.length) return {
        next: function() {
          o && i >= o.length && (o = void 0);
          return {
            value: o && o[i++],
            done: !o
          };
        }
      };
      throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
    };
    var __read = this && this.__read || function(o, n) {
      var m = "function" === typeof Symbol && o[Symbol.iterator];
      if (!m) return o;
      var i = m.call(o), r, ar = [], e;
      try {
        while ((void 0 === n || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
      } catch (error) {
        e = {
          error: error
        };
      } finally {
        try {
          r && !r.done && (m = i["return"]) && m.call(i);
        } finally {
          if (e) throw e.error;
        }
      }
      return ar;
    };
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.SdkHuiXin = void 0;
    var SDKInterface_1 = require("../../app/game/ctrl/sdk/SDKInterface");
    var SDKMgr_1 = require("../../app/game/ctrl/sdk/SDKMgr");
    var EngineMain_1 = require("../../engine/EngineMain");
    var Container_1 = require("../../framework/container/Container");
    var FW_1 = require("../../framework/FW");
    var MD5Util_1 = require("../../libutil/MD5Util");
    var SdkHuiXin = function(_super) {
      __extends(SdkHuiXin, _super);
      function SdkHuiXin() {
        var _this = null !== _super && _super.apply(this, arguments) || this;
        _this.wxAppId = "wx9bbea9f80bb512c5";
        _this.gameId = "2600108";
        _this.gameKey = "0bf009ee39e53810";
        return _this;
      }
      SdkHuiXin.prototype.getUserType = function() {
        return SDKMgr_1.SDK_TYPE.WeiChat;
      };
      Object.defineProperty(SdkHuiXin.prototype, "gameSecret", {
        get: function() {
          var _a;
          return (null === (_a = this._loginInfo) || void 0 === _a ? void 0 : _a.gameSecret) || "";
        },
        enumerable: false,
        configurable: true
      });
      Object.defineProperty(SdkHuiXin.prototype, "gamePaySecret", {
        get: function() {
          var _a;
          return (null === (_a = this._loginInfo) || void 0 === _a ? void 0 : _a.gamePaySecret) || "";
        },
        enumerable: false,
        configurable: true
      });
      SdkHuiXin.prototype.objKeySort = function(obj) {
        var newkey = Object.keys(obj).sort();
        var newObj = {};
        for (var i = 0; i < newkey.length; i++) newObj[newkey[i]] = obj[newkey[i]];
        return newObj;
      };
      SdkHuiXin.prototype.randomString = function(len) {
        len = len || 32;
        var $chars = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678";
        var maxPos = $chars.length;
        var pwd = "";
        for (var i = 0; i < len; i++) pwd += $chars.charAt(Math.floor(Math.random() * maxPos));
        return pwd;
      };
      SdkHuiXin.prototype.makeSign = function(data, secret) {
        this.log(data);
        var d = this.objKeySort(data);
        this.log(d);
        var arr = [], name;
        for (name in d) arr.push(name + "=" + d[name]);
        var str = arr.join("&") + "&" + secret;
        this.log(str);
        return MD5Util_1.MD5Util.md5(str);
      };
      SdkHuiXin.prototype.initAsync = function() {
        return __awaiter(this, void 0, Promise, function() {
          return __generator(this, function(_a) {
            this._sdk = window["wx_huixin_sdk"];
            this._phoneSdk = window["wx_huixin_phone_sdk"];
            if (!this._sdk) return [ 2 ];
            this.log("initAsync");
            return [ 2 ];
          });
        });
      };
      SdkHuiXin.prototype.getExtInfo = function() {
        if (this._gameExtInfo) return this._gameExtInfo;
        var wx = window["wx"];
        if (wx) {
          this._gameExtInfo = wx.getLaunchOptionsSync().query;
          this.log("\u83b7\u53d6SDK\u989d\u5916\u53c2\u6570..", JSON.stringify(this._gameExtInfo));
        }
        this._gameExtInfo || this.error("\u8c03\u7528SDK\u989d\u5916\u53c2\u6570\u63a5\u53e3\u5f02\u5e38!!!");
        return this._gameExtInfo;
      };
      SdkHuiXin.prototype.getServerAddress = function(serverParams) {
        return __awaiter(this, void 0, Promise, function() {
          var arr, gameExtInfo, inviteRoleId, params, host, url, res;
          return __generator(this, function(_a) {
            switch (_a.label) {
             case 0:
              arr = [];
              null != serverParams.type && arr.push("type=" + serverParams.type);
              null != serverParams.platform && arr.push("platform=" + serverParams.platform);
              null != serverParams.code && arr.push("code=" + serverParams.code);
              null != serverParams.token && arr.push("token=" + serverParams.token);
              null != serverParams.time && arr.push("time=" + serverParams.time);
              null != serverParams.isWss && arr.push("isWss=" + serverParams.isWss);
              null != serverParams.otherOpenId && arr.push("otherOpenId=" + serverParams.otherOpenId);
              null != serverParams.selectServerId && arr.push("selectServerId=" + serverParams.selectServerId);
              gameExtInfo = this.getExtInfo();
              if (gameExtInfo) {
                inviteRoleId = gameExtInfo["inviteRoleId"];
                if (inviteRoleId) {
                  arr.push("inviteRoleId=" + inviteRoleId);
                  this.log("inviteRoleId====", inviteRoleId);
                }
              }
              params = arr.join("&");
              host = this.ModelMgr.ModelPublicData.getServerAddressUrl();
              url = host + "?" + params;
               console.log('params', params)
              return [ 4, FW_1.FW.Http.get(url, null, null, true) ];

             case 1:
              res = _a.sent();
              if (res) {
                this._loginInfo = res.content;
                if (res.result) return [ 2, res.content ];
              }
              return [ 2, null ];
            }
          });
        });
      };
      SdkHuiXin.prototype.init = function() {};
      SdkHuiXin.prototype.initSdk = function() {
        var e_1, _a, e_2, _b;
        if (!this._sdkInfo) {
          this.error("sdk\u767b\u5f55\u6570\u636e\u4e3a\u7a7a!!, \u8bf7\u68c0\u67e5");
          return;
        }
        this._adComponentList = [];
        var infos = this._sdkInfo.sdk_wxconf_adcomponent;
        "string" == typeof infos && (infos = JSON.parse(infos));
        if (infos) {
          for (var key in infos) if (Object.prototype.hasOwnProperty.call(infos, key)) {
            var dic = infos[key];
            for (var adKey in dic) if (Object.prototype.hasOwnProperty.call(dic, adKey)) {
              var element = dic[adKey];
              1 == Number(key) && this._adComponentList.push({
                type: Number(key),
                id: element.id,
                intervals: Number(element.intervals)
              });
            }
          }
        } else this.warn("\u6ca1\u6709\u5e7f\u544a\u6587\u6848??\u68c0\u67e5sdk\u6570\u636e", JSON.stringify(this._sdkInfo));
        this._shareFirendList = [];
        infos = this._sdkInfo.sdk_shareconf;
        "string" == typeof infos && (infos = JSON.parse(infos));
        if (infos) try {
          for (var infos_1 = __values(infos), infos_1_1 = infos_1.next(); !infos_1_1.done; infos_1_1 = infos_1.next()) {
            var iterator = infos_1_1.value;
            this._shareFirendList.push({
              share_id: iterator.share_id,
              share_title: iterator.share_title,
              share_image: iterator.share_image
            });
          }
        } catch (e_1_1) {
          e_1 = {
            error: e_1_1
          };
        } finally {
          try {
            infos_1_1 && !infos_1_1.done && (_a = infos_1.return) && _a.call(infos_1);
          } finally {
            if (e_1) throw e_1.error;
          }
        } else this.warn("\u6ca1\u6709\u597d\u53cb\u5206\u4eab\u6587\u6848??\u68c0\u67e5sdk\u6570\u636e", JSON.stringify(this._sdkInfo));
        this._shareLineList = [];
        infos = this._sdkInfo.sdk_shareconf_timeline;
        "string" == typeof infos && (infos = JSON.parse(infos));
        if (infos) try {
          for (var infos_2 = __values(infos), infos_2_1 = infos_2.next(); !infos_2_1.done; infos_2_1 = infos_2.next()) {
            var iterator = infos_2_1.value;
            this._shareLineList.push({
              share_id: iterator.share_id,
              share_title: iterator.share_title,
              share_image: iterator.share_image
            });
          }
        } catch (e_2_1) {
          e_2 = {
            error: e_2_1
          };
        } finally {
          try {
            infos_2_1 && !infos_2_1.done && (_b = infos_2.return) && _b.call(infos_2);
          } finally {
            if (e_2) throw e_2.error;
          }
        } else this.warn("\u6ca1\u6709\u670b\u53cb\u5708\u5206\u4eab\u6587\u6848??\u68c0\u67e5sdk\u6570\u636e", JSON.stringify(this._sdkInfo));
        var info = this.getRandomInfoByList(this._shareFirendList);
        info && this._sdk.shareInit(info.share_id, info.share_title, info.share_image, this.getExtInfo());
        info = this.getRandomInfoByList(this._shareLineList);
        info && this._sdk.shareTimeLineInit(info.share_id, info.share_title, info.share_image, this.getExtInfo());
        var t = this;
        var wx = window["wx"];
        wx.setKeepScreenOn({
          keepScreenOn: true,
          success: function(res) {
            t.log("\u5c4f\u5e55\u5e38\u4eae\u5df2\u5f00\u542f");
          },
          fail: function(res) {
            t.warn("setKeepScreenOn\u8c03\u7528\u5931\u8d25");
          }
        });
      };
      SdkHuiXin.prototype.login = function(username, password) {
        return __awaiter(this, void 0, Promise, function() {
          var userInfo;
          var _this = this;
          return __generator(this, function(_a) {
            switch (_a.label) {
             case 0:
              return [ 4, new Promise(function(resolve, reject) {
                var conf = {
                  game_id: _this.gameId,
                  game_key: _this.gameKey,
                  wx_appid: _this.wxAppId
                };
                _this._sdk.init(conf, function(res) {
                  if (!res) {
                    reject(null);
                    return;
                  }
                  if ("string" == typeof res) {
                    _this.log("\u767b\u5f55\u8fd4\u56de\u6570\u636e\u8f6c\u6362\u4e3aJson");
                    res = JSON.parse(res);
                  }
                  _this.log("\u767b\u5f55\u8fd4\u56de\u6570\u636e:", JSON.stringify(res));
                  if (0 == res.code) {
                    _this._sdkInfo = res.data;
                    var result = {};
                    var exclude = [ "sdk_ext", "sdk_shareconf", "sdk_shareconf_timeline", "sdk_wxconf_adcomponent" ];
                    var sdk_ext = res.data.sdk_ext;
                    for (var key in res.data) if (Object.prototype.hasOwnProperty.call(res.data, key)) {
                      if (exclude.includes(key)) continue;
                      result[key] = res.data[key];
                    }
                    _this.log("token:", result);
                    var openid = void 0;
                    sdk_ext && (openid = sdk_ext.wd);
                    userInfo = {
                      code: _this._sdkInfo.sdk_openid,
                      time: _this.TimeUtil.LocalNow(),
                      token: JSON.stringify(result),
                      openid: openid
                    };
                    _this.initSdk();
                    resolve(userInfo);
                  } else reject(null);
                });
              }) ];

             case 1:
              userInfo = _a.sent();
              this.log("\u767b\u5f55\u6570\u636e:userInfo", JSON.stringify(userInfo));
              return [ 2, [ !!userInfo, userInfo ] ];
            }
          });
        });
      };
      SdkHuiXin.prototype.loginOrRegist = function() {
        return __awaiter(this, void 0, Promise, function() {
          var userInfo, _loop_1, this_1, state_1;
          var _this = this;
          return __generator(this, function(_a) {
            switch (_a.label) {
             case 0:
              _loop_1 = function() {
                var sending, promise;
                return __generator(this, function(_a) {
                  switch (_a.label) {
                   case 0:
                    sending = true;
                    promise = this_1.login("", "");
                    promise && promise.then(function(arg) {
                      var _a = __read(arg, 2), success = _a[0], result = _a[1];
                      _this.log("\u767b\u5f55:" + success);
                      _this.log(JSON.stringify(result));
                      success && result && (userInfo = result);
                      sending = false;
                    }).catch(function(e) {
                      e && _this.error(e);
                      sending = false;
                    });
                    return [ 4, FW_1.FW.Task.waitUntil(function() {
                      return false == sending;
                    }) ];

                   case 1:
                    _a.sent();
                    if (!(null == userInfo)) return [ 3, 3 ];
                    return [ 4, FW_1.FW.Task.delay(2e3) ];

                   case 2:
                    _a.sent();
                    return [ 3, 4 ];

                   case 3:
                    return [ 2, "break" ];

                   case 4:
                    return [ 2 ];
                  }
                });
              };
              this_1 = this;
              _a.label = 1;

             case 1:
              if (!!userInfo) return [ 3, 3 ];
              return [ 5, _loop_1() ];

             case 2:
              state_1 = _a.sent();
              if ("break" === state_1) return [ 3, 3 ];
              return [ 3, 1 ];

             case 3:
              return [ 2, userInfo ];
            }
          });
        });
      };
      SdkHuiXin.prototype.PPP = function(payInfo) {
        return __awaiter(this, void 0, Promise, function() {
          var _this = this;
          return __generator(this, function(_a) {
            new Promise(function(resolve, reject) {
              var timestamp = _this.TimeUtil.LocalNow();
              var role_name = payInfo.userName;
              var p = {
                app_id: _this._sdkInfo.sdk_appid,
                game_id: _this.gameId,
                game_key: _this.gameKey,
                open_id: _this._sdkInfo.sdk_openid,
                total_fee: payInfo.price.toFixed(2),
                cp_orderno: payInfo.orderId,
                object_id: payInfo.orderId,
                object_name: payInfo.orderName,
                object_desc: "",
                timestamp: timestamp,
                nonce: _this.randomString(8),
                currency_type: "RMB",
                cp_callback_url: payInfo.payUrl,
                server_id: payInfo.serverId,
                server_name: payInfo.serverName,
                role_id: payInfo.userId + "",
                role_name: role_name,
                role_level: payInfo.level,
                role_level_reborn: 0,
                role_vip_level: payInfo.vipLevel,
                score: payInfo.power
              };
              p.sign = _this.makeSign(p, _this.gamePaySecret);
              _this._sdk.pay(p, function(res) {
                console.log("ppp:", res);
                resolve(res);
              });
            });
            return [ 2, null ];
          });
        });
      };
      SdkHuiXin.prototype.Share = function(type) {
        return __awaiter(this, void 0, Promise, function() {
          var info, voRoleSelf, query;
          return __generator(this, function(_a) {
            info = this.getRandomInfoByList(this._shareFirendList);
            voRoleSelf = this.ModelMgr.ModelRole.GetRole();
            query = "inviteRoleId=" + voRoleSelf.GetRoleId();
            this.log("query====", query);
            this._sdk.share(info.share_id, info.share_title, info.share_image, query);
            return [ 2, null ];
          });
        });
      };
      SdkHuiXin.prototype.isShowSheQu = function() {
        return true;
      };
      SdkHuiXin.prototype.submitDataUserInfo = function(type, params) {
        var _this = this;
        var roleVo = this.ModelMgr.ModelVO.VORoleBag.GetOne();
        if (!roleVo) {
          this.error("submitDataUserInfo VORoleBag null");
          return;
        }
        var role_id = roleVo.GetRoleId();
        var role_name = roleVo.GetName();
        var role_level = roleVo.GetLevel();
        var power = this.ModelMgr.ModelRole.getRoleAtk();
        var actionlogin = this.ModelMgr.ModelAccount.getRoleInfo();
        var submitInfo = {
          app_id: this._sdkInfo.sdk_appid,
          game_id: this.gameId,
          game_key: this.gameKey,
          open_id: this._sdkInfo.sdk_openid,
          server_id: actionlogin.serverAddress.id,
          server_name: actionlogin.serverAddress.name,
          role_id: role_id + "",
          role_name: role_name,
          role_level: role_level,
          role_vip_level: 0
        };
        var is_finish_newcomer = this.ModelChapterNew.checkLevelPass(1) ? 1 : 0;
        switch (type) {
         case SDKInterface_1.SubmitDataUserInfoType.enterGame:
          submitInfo.score = power;
          submitInfo.role_level_reborn = 0;
          submitInfo.is_finish_newcomer = is_finish_newcomer;
          submitInfo.is_new = params.is_new_create || 0;
          submitInfo.sign = this.makeSign(submitInfo, this.gameSecret);
          this.log("\u4e0a\u62a5sign:", submitInfo.sign);
          this._sdk.enterGameLog(submitInfo, function(res) {
            _this.log("\u4e0a\u62a5\u8fdb\u5165\u6e38\u620f\u4fe1\u606f!!", JSON.stringify(res));
          });
          break;

         case SDKInterface_1.SubmitDataUserInfoType.createRole:
          submitInfo.score = power;
          submitInfo.sign = this.makeSign(submitInfo, this.gameSecret);
          this._sdk.createRoleLog(submitInfo, function(res) {
            _this.log("\u4e0a\u62a5\u521b\u89d2\u65e5\u5fd7!!", JSON.stringify(res));
          });
          break;

         case SDKInterface_1.SubmitDataUserInfoType.upRoleLevel:
          submitInfo.score = power;
          submitInfo.is_finish_newcomer = is_finish_newcomer;
          submitInfo.sign = this.makeSign(submitInfo, this.gameSecret);
          this._sdk.levelUpLog(submitInfo, function(res) {
            _this.log("\u4e0a\u62a5\u5347\u7ea7\u65e5\u5fd7!!", JSON.stringify(res));
          });
          break;

         case SDKInterface_1.SubmitDataUserInfoType.newcomerFinish:
          submitInfo.score = power;
          submitInfo.is_finish_newcomer = 1;
          submitInfo.sign = this.makeSign(submitInfo, this.gameSecret);
          this._sdk.newcomerLog(submitInfo, function(res) {
            _this.log("\u4e0a\u62a5\u65b0\u624b\u65e5\u5fd7!!", JSON.stringify(res));
          });
          break;

         case SDKInterface_1.SubmitDataUserInfoType.wxPromoto:
          submitInfo.wx_promoto_id = params.advertisement_id.toString() || "";
          submitInfo.wx_promoto_type = params.advertisement_type || 0;
          submitInfo.count = params.advertisement_count || 0;
          submitInfo.is_finish = params.advertisement_result || 1;
          submitInfo.sign = this.makeSign(submitInfo, this.gameSecret);
          this._sdk.wxPromotoExposeLog(submitInfo, function(res) {
            _this.log("\u4e0a\u62a5\u5e7f\u544a\u65e5\u5fd7!!", JSON.stringify(res));
          });
        }
      };
      SdkHuiXin.prototype.messageCheck = function(content, isChat) {
        var _this = this;
        this._sdkInfo && 1 == this._sdkInfo.sdk_is_checkmsg ? this._sdk.isMessageNormal(this.gameId, content, function(res) {
          _this.log("\u804a\u5929\u68c0\u6d4b!!", JSON.stringify(res));
        }, isChat ? 0 : 1) : this.log("\u804a\u5929\u68c0\u6d4b\u63a5\u53e3\u672a\u5f00\u542f!!");
      };
      SdkHuiXin.prototype.bindPhone = function(phone, code) {
        return __awaiter(this, void 0, void 0, function() {
          var promise;
          var _this = this;
          return __generator(this, function(_a) {
            switch (_a.label) {
             case 0:
              if (!this.hasPhone()) {
                this.log("SDK\u6ca1\u6709\u7ed1\u5b9a\u624b\u673a\u7684\u63a5\u53e3");
                return [ 2 ];
              }
              return [ 4, new Promise(function(resolve, reject) {
                _this._phoneSdk.bindPhone(_this.gameId, _this._sdkInfo.sdk_openid, phone, code, function(result) {
                  _this.log("bindPhone", JSON.stringify(result));
                  resolve(result);
                });
              }) ];

             case 1:
              promise = _a.sent();
              return [ 2, promise ];
            }
          });
        });
      };
      SdkHuiXin.prototype.getPhoneCode = function(phone) {
        return __awaiter(this, void 0, void 0, function() {
          var _this = this;
          return __generator(this, function(_a) {
            if (!this.hasPhone()) {
              this.log("SDK\u6ca1\u6709\u7ed1\u5b9a\u624b\u673a\u7684\u63a5\u53e3");
              return [ 2 ];
            }
            this._phoneSdk.sendSmsCode(this.gameId, phone, function(result) {
              _this.log("SendPhoneCode", JSON.stringify(result));
            });
            return [ 2 ];
          });
        });
      };
      SdkHuiXin.prototype.hasPhone = function() {
        var _a;
        if (!this._phoneSdk) return false;
        return 1 == (null === (_a = this._sdkInfo) || void 0 === _a ? void 0 : _a.sdk_is_bindphone);
      };
      SdkHuiXin.prototype.getShowPay = function() {
        var _a;
        return 1 == (null === (_a = this._sdkInfo) || void 0 === _a ? void 0 : _a.sdk_is_pay);
      };
      SdkHuiXin.prototype.Advertisement = function(gameItemId) {
        return __awaiter(this, void 0, Promise, function() {
          var t, result, info, wxAdType, wxAdunitId, wxAdIntervals, clearCallback;
          return __generator(this, function(_a) {
            switch (_a.label) {
             case 0:
              t = this;
              result = SDKInterface_1.AdvertisementResult.PULL_FAILED;
              info = t.getRandomInfoByList(t._adComponentList);
              if (!info) {
                t.warn("\u6ca1\u6709\u63a5\u5165\u5e7f\u544a, \u6216\u5e7f\u544a\u6570\u636e\u4e3a\u7a7a, \u8bf7\u524d\u5f80\u68c0\u67e5\u767b\u5f55\u6570\u636e!!");
                return [ 2, result ];
              }
              wxAdType = info.type;
              wxAdunitId = info.id;
              wxAdIntervals = info.intervals;
              clearCallback = null;
              return [ 4, new Promise(function(resolve, reject) {
                var cssStyle = {
                  left: 10,
                  top: 76,
                  width: 320
                };
                t.log("\u8c03\u7528\u5e7f\u544a\u7ec4\u4ef6:", info);
                t._sdk.wxAdComponentCreate(gameItemId, wxAdType, wxAdunitId, wxAdIntervals, cssStyle, function(ret) {
                  var _a;
                  t.log("\u5e7f\u544a\u4fe1\u606f:", ret);
                  var videoAd;
                  0 == (null === ret || void 0 === ret ? void 0 : ret.code) && (videoAd = null === (_a = ret.data) || void 0 === _a ? void 0 : _a.component);
                  if (null == videoAd) {
                    t.error("\u521b\u5efa\u5e7f\u544a\u7ec4\u4ef6\u5931\u8d25");
                    result = SDKInterface_1.AdvertisementResult.PULL_FAILED;
                    resolve(false);
                    return false;
                  }
                  var onLoadFunc = function() {
                    console.log("\u5e7f\u544a\u89c6\u9891 \u62c9\u53d6\u6210\u529f");
                  };
                  videoAd.onLoad(onLoadFunc);
                  var onErrorFunc = function(err) {
                    console.error("\u5e7f\u544a\u89c6\u9891 \u62c9\u53d6\u5931\u8d25");
                    console.error(err);
                  };
                  videoAd.onError(onErrorFunc);
                  var onCloseFunc = null;
                  clearCallback = function() {
                    if (null != onCloseFunc) {
                      videoAd.offLoad(onLoadFunc);
                      videoAd.offError(onErrorFunc);
                      videoAd.offClose(onCloseFunc);
                      onLoadFunc = null;
                      onErrorFunc = null;
                      onCloseFunc = null;
                    }
                    clearCallback = null;
                  };
                  onCloseFunc = function(res) {
                    null != clearCallback && clearCallback();
                    console.log("\u5e7f\u544a\u89c6\u9891 \u5173\u95ed");
                    console.log(res);
                    if (res && res.isEnded || void 0 === res) {
                      var timeAd = t.ModelMgr.ModelRole.GetAdvertTimes(gameItemId);
                      var data = {
                        advertisement_id: wxAdunitId,
                        advertisement_type: wxAdType,
                        advertisement_count: timeAd + 1,
                        advertisement_result: 1
                      };
                      t.submitDataUserInfo(SDKInterface_1.SubmitDataUserInfoType.wxPromoto, data);
                      result = SDKInterface_1.AdvertisementResult.PLAY_FINISH;
                      resolve(result);
                    } else {
                      FW_1.FW.showTip("\u89c6\u9891\u64ad\u653e\u672a\u5b8c\u6210\uff0c\u65e0\u6cd5\u83b7\u5f97\u5956\u52b1");
                      result = SDKInterface_1.AdvertisementResult.PLAY_FAIL;
                      resolve(false);
                    }
                  };
                  videoAd.onClose(onCloseFunc);
                  videoAd.show().then(function() {
                    console.log("\u5e7f\u544a\u89c6\u9891 \u5e7f\u544a\u663e\u793a\u6210\u529f");
                  }).catch(function(err) {
                    console.log("\u5e7f\u544a\u89c6\u9891 \u663e\u793a\u5931\u8d25, \u518d\u6b21\u624b\u52a8\u62c9\u53d6");
                    console.log(null === err || void 0 === err ? void 0 : err.errMsg);
                    videoAd.load().then(function() {
                      console.log("\u5e7f\u544a\u89c6\u9891 \u624b\u52a8\u62c9\u53d6\u6210\u529f load");
                      videoAd.show().then(function() {
                        console.log("\u5e7f\u544a\u89c6\u9891 \u5e7f\u544a\u663e\u793a\u6210\u529f");
                      }).catch(function(err) {
                        FW_1.FW.showTip("\u52a0\u8f7d\u5931\u8d25,\u8bf7\u7a0d\u540e\u518d\u8bd5");
                        console.warn("\u5e7f\u544a\u89c6\u9891 \u5e7f\u544a\u663e\u793a\u5931\u8d25");
                        result = SDKInterface_1.AdvertisementResult.PULL_FAILED;
                        resolve(false);
                      });
                    }).catch(function(err) {
                      FW_1.FW.showTip("\u52a0\u8f7d\u5931\u8d25,\u8bf7\u7a0d\u540e\u518d\u8bd5");
                      console.warn("\u5e7f\u544a\u89c6\u9891 \u624b\u52a8\u62c9\u53d6\u5931\u8d25 load");
                      console.log(null === err || void 0 === err ? void 0 : err.errMsg);
                      result = SDKInterface_1.AdvertisementResult.PULL_FAILED;
                      resolve(false);
                    });
                  });
                });
              }) ];

             case 1:
              _a.sent();
              null != clearCallback && clearCallback();
              return [ 2, result ];
            }
          });
        });
      };
      SdkHuiXin.prototype.HasADVideo = function() {
        return true;
      };
      SdkHuiXin.prototype.getRandomInfoByList = function(list) {
        if (!list || !list.length) return null;
        var len = list.length;
        while (true) {
          var random = Math.floor(Math.random() * len);
          var v = list[random];
          if (null != v) return v;
        }
      };
      SdkHuiXin.prototype.Restart = function() {
        var wx = window["wx"];
        wx && wx.restartMiniProgram && wx.restartMiniProgram();
        return true;
      };
      SdkHuiXin.prototype.checkUpdate = function() {
        return __awaiter(this, void 0, Promise, function() {
          var wx, errMsg, retWxUpdate;
          return __generator(this, function(_a) {
            switch (_a.label) {
             case 0:
              if (!EngineMain_1.EngineMain.isMiniGameWeiXin) return [ 2 ];
              wx = window["wx"];
              if (null == (null === wx || void 0 === wx ? void 0 : wx.getUpdateManager) || "function" != typeof (null === wx || void 0 === wx ? void 0 : wx.getUpdateManager)) {
                console.error("\u7248\u672c\u8fc7\u4f4e,\u5c0f\u6e38\u620f\u65e0\u6cd5\u68c0\u67e5\u66f4\u65b0(\u57fa\u7840\u5e93>=1.9.90)");
                return [ 2 ];
              }
              errMsg = null;
              retWxUpdate = null;
              console.warn("\u5fae\u4fe1\u5c0f\u6e38\u620f,\u68c0\u67e5\u66f4\u65b0");
              return [ 4, new Promise(function(resolve, reject) {
                console.warn("getUpdateManager");
                var updateManager = wx.getUpdateManager();
                console.warn("onCheckForUpdate");
                updateManager.onCheckForUpdate(function(res) {
                  console.warn(res.hasUpdate);
                  if (res.hasUpdate) console.warn("\u5fae\u4fe1\u5c0f\u6e38\u620f\u6709\u66f4\u65b0"); else {
                    retWxUpdate = "";
                    resolve(true);
                  }
                });
                console.warn("onUpdateReady");
                updateManager.onUpdateReady(function() {
                  console.warn("\u5c0f\u6e38\u620f\u6709\u65b0\u7248\u672c,\u5e94\u7528\u65b0\u7248\u672c\u5e76\u91cd\u542f");
                  if (wx.showModal) {
                    console.log("onUpdateReady wx.showModal");
                    wx.showModal({
                      showCancel: true,
                      cancelText: "\u53d6\u6d88",
                      cancelColor: "#000000",
                      confirmText: "\u786e\u5b9a",
                      confirmColor: "#576B95",
                      title: "\u66f4\u65b0\u63d0\u793a",
                      content: "\u65b0\u7248\u672c\u5df2\u7ecf\u51c6\u5907\u597d\uff0c\u662f\u5426\u91cd\u542f\u5e94\u7528?",
                      fail: function() {},
                      complete: function() {},
                      success: function(res) {
                        updateManager.applyUpdate();
                      }
                    });
                  } else updateManager.applyUpdate();
                  retWxUpdate = "fail, need update";
                  resolve(true);
                });
                console.warn("onUpdateFailed");
                updateManager.onUpdateFailed(function() {
                  console.error("\u5c0f\u6e38\u620f\u6709\u65b0\u7248\u672c,\u4e0b\u8f7d\u5931\u8d25");
                  if (wx.showModal) {
                    console.log("onUpdateFailed wx.showModal");
                    wx.showModal({
                      showCancel: true,
                      cancelText: "\u53d6\u6d88",
                      cancelColor: "#000000",
                      confirmText: "\u786e\u5b9a",
                      confirmColor: "#576B95",
                      title: "\u66f4\u65b0\u63d0\u793a",
                      content: "\u65b0\u7248\u672c\u5df2\u7ecf\u4e0a\u7ebf\uff0c\u8bf7\u9000\u51fa\u91cd\u542f!",
                      fail: function() {},
                      complete: function() {},
                      success: function(res) {
                        wx.exitMiniProgram();
                      }
                    });
                  }
                  retWxUpdate = "fail, onUpdateFailed";
                  resolve(true);
                });
                console.warn("onUpdateFailed2");
              }) ];

             case 1:
              _a.sent();
              null != retWxUpdate && "" != retWxUpdate && (errMsg = retWxUpdate);
              return [ 2, errMsg ];
            }
          });
        });
      };
      SdkHuiXin.__cname = "SdkHuiXin";
      __decorate([ Container_1.injectField("AppCustomData") ], SdkHuiXin.prototype, "AppCustomData", void 0);
      __decorate([ Container_1.injectField("ModelMgr") ], SdkHuiXin.prototype, "ModelMgr", void 0);
      __decorate([ Container_1.injectField("TimeUtil") ], SdkHuiXin.prototype, "TimeUtil", void 0);
      __decorate([ Container_1.injectField("ModelChapterNew") ], SdkHuiXin.prototype, "ModelChapterNew", void 0);
      return SdkHuiXin;
    }(SDKInterface_1.SDKInterface);
    exports.SdkHuiXin = SdkHuiXin;
    cc._RF.pop();
  }, {
    "../../app/game/ctrl/sdk/SDKInterface": void 0,
    "../../app/game/ctrl/sdk/SDKMgr": void 0,
    "../../engine/EngineMain": void 0,
    "../../framework/FW": void 0,
    "../../framework/container/Container": void 0,
    "../../libutil/MD5Util": void 0
  } ],
  moduleSdkHuiXin: [ function(require, module, exports) {
    "use strict";
    cc._RF.push(module, "b452bOUA8xJ2oxlYV2miCRB", "moduleSdkHuiXin");
    (function() {
      var _miniSdk = require("./wxmini_game");
      console.log("wxmini_game", _miniSdk);
      window["wx_huixin_sdk"] = _miniSdk;
      var _phoneSkd = require("./wxmini_game_bindphone_interface");
      window["wx_huixin_phone_sdk"] = _phoneSkd;
      console.log("module finish wxmini_game");
    })();
    console.log("module end wxmini_game");
    cc._RF.pop();
  }, {
    "./wxmini_game": "wxmini_game",
    "./wxmini_game_bindphone_interface": "wxmini_game_bindphone_interface"
  } ],
  wxmini_game_bindphone_interface: [ function(require, module, exports) {
    "use strict";
    cc._RF.push(module, "1b144fg3NxDqYwWuKz9XcJb", "wxmini_game_bindphone_interface");
    "use strict";
    function consoleLog(a) {
      var b = getCache("init_sdk_isprint");
      getCache("init_wx_env");
      1 == b && console.log(a);
    }
    function getEnvVersion() {
      var a = __wxConfig.envVersion;
      console.log("env ", a), setCache("init_wx_env", a);
    }
    function formatParams(a) {
      var b, c = [];
      for (b in a) a.hasOwnProperty(b) && c.push(encodeURIComponent(b) + "=" + encodeURIComponent(a[b]));
      return c.join("&");
    }
    function request(a, b, c, d, e) {
      var f = {};
      "undefined" == typeof e && (e = " default "), "undefined" != typeof d && "get" == d.toLowerCase() ? (f.url = a + (a.indexOf("?") > -1 ? "&" : "?") + formatParams(b), 
      f.data = {}) : (f.url = a, f.data = b), consoleLog(a), consoleLog(b), f.header = {
        "content-type": "application/json"
      }, f.success = function(a) {
        consoleLog(e + " wx req suc " + JSON.stringify(a)), "function" == typeof c && c(a.data);
      }, f.fail = function(a) {
        consoleLog(e + " wx req fail " + JSON.stringify(a)), "undefined" != typeof a && "undefined" != typeof a.data && "function" == typeof c ? c(a.data) : "function" == typeof c && c({
          code: 408,
          message: JSON.stringify(a),
          data: []
        });
      }, f.complete = function(a) {}, wx.request(f);
    }
    function getCache(a) {
      try {
        var b = wx.getStorageSync(a);
        return b;
      } catch (c) {
        console.log("get cache error. " + JSON.stringify(c));
      }
    }
    function bindPhone(a, b, c, d, e) {
      if (consoleLog("bindPhone"), !isPhone(c)) return void wx.showToast({
        icon: "none",
        title: "\u8bf7\u8f93\u5165\u6b63\u786e\u7684\u624b\u673a\u53f7!"
      });
      if (!d) return void wx.showToast({
        icon: "none",
        title: "\u8bf7\u8f93\u5165\u9a8c\u8bc1\u7801!"
      });
      var f = sdkDomain + "/phoneSms/userBindPhone", g = {
        app_id: getCache("init_sdk_appid"),
        game_id: a,
        open_id: b,
        phone: c,
        verifycode: d,
        time: Date.parse(new Date()) / 1e3,
        sign: b + "" + c + d
      }, h = function h(a) {
        e(a), 0 != a.code && wx.showToast({
          title: a.message,
          icon: "none",
          duration: 2e3
        });
      };
      request(f, g, h, "post", "bindPhone");
    }
    var sdkDomain = "https://h5api.huixinhuyu.com", sdkBaseUrl = "https://h5api.huixinhuyu.com/h5sdk/wxminigame/", isGetCode = !0, isPhone = function isPhone(a) {
      return null != a && /^1[3456789]\d{9}$/.test(a) && a;
    }, sendSmsCode = function sendSmsCode(a, b, c) {
      if (!isPhone(b)) return void wx.showToast({
        icon: "none",
        title: "\u8bf7\u8f93\u5165\u6b63\u786e\u7684\u624b\u673a\u53f7!"
      });
      var d = {
        game_id: a,
        phone: b,
        time: Date.parse(new Date()) / 1e3
      }, e = sdkDomain + "/phoneSms/getPhoneSmsCode";
      request(e, d, c, "post", "getPhoneSmsCode");
    };
    module.exports.sendSmsCode = sendSmsCode, module.exports.bindPhone = bindPhone;
    cc._RF.pop();
  }, {} ],
  wxmini_game: [ function(require, module, exports) {
    "use strict";
    cc._RF.push(module, "faec14JnKFHaKVit93uUyeT", "wxmini_game");
    "use strict";
    function consoleLog(a) {
      var b = getCache("init_sdk_isprint");
      getCache("init_wx_env");
      1 == b && console.log(a);
    }
    function getEnvVersion() {
      var a = __wxConfig.envVersion;
      console.log("env ", a), setCache("init_wx_env", a);
    }
    function formatParams(a) {
      var b, c = [];
      for (b in a) a.hasOwnProperty(b) && c.push(encodeURIComponent(b) + "=" + encodeURIComponent(a[b]));
      return c.join("&");
    }
    function request(a, b, c, d, e) {
      var f = {};
      "undefined" == typeof e && (e = " default "), "undefined" != typeof d && "get" == d.toLowerCase() ? (f.url = a + (a.indexOf("?") > -1 ? "&" : "?") + formatParams(b), 
      f.data = {}) : (f.url = a, f.data = b), consoleLog(a), consoleLog(b), f.header = {
        "content-type": "application/json"
      }, f.success = function(a) {
        consoleLog(e + " wx req suc " + JSON.stringify(a)), "function" == typeof c && c(a.data);
      }, f.fail = function(a) {
        consoleLog(e + " wx req fail " + JSON.stringify(a)), "undefined" != typeof a && "undefined" != typeof a.data && "function" == typeof c ? c(a.data) : "function" == typeof c && c({
          code: 408,
          message: JSON.stringify(a),
          data: []
        });
      }, f.complete = function(a) {}, wx.request(f);
    }
    function setCache(a, b) {
      try {
        wx.setStorageSync(a, b);
      } catch (c) {
        console.log("set cache error. " + JSON.stringify(c));
      }
    }
    function getCache(a) {
      try {
        var b = wx.getStorageSync(a);
        return b;
      } catch (c) {
        console.log("get cache error. " + JSON.stringify(c));
      }
    }
    function delCache(a) {
      try {
        var b = wx.removeStorageSync(a);
        return b;
      } catch (c) {
        console.log("del cache error. " + JSON.stringify(c));
      }
    }
    function deviceInfo() {
      wx.getSystemInfoSync();
    }
    function clearInitCache() {
      delCache("init_wx_scene"), delCache("init_wx_env"), delCache("init_wx_appid"), delCache("init_sdk_appid"), 
      delCache("init_sdk_adid"), delCache("init_sdk_gameid"), delCache("init_sdk_gamekey"), 
      delCache("init_sdk_openid"), delCache("init_sdk_ext"), delCache("init_sdk_isprint"), 
      delCache("init_wx_device_type"), delCache("init_is_fromshare"), delCache("init_fromshare_id"), 
      delCache("init_fromshare_appid"), delCache("init_fromshare_adid"), delCache("init_fromshare_uid"), 
      delCache("init_referrer_appid"), delCache("init_referrer_data"), delCache("init_wx_query");
    }
    function checkIsForbidScene() {
      var a = getCache("last_wx_scene");
      return 1011 == a || 1030 == a;
    }
    function setInitCahe(a) {
      var b = wx.getLaunchOptionsSync();
      console.log("init loadInfo." + JSON.stringify(b)), setCache("init_wx_scene", b.scene), 
      checkIsForbidScene() || setCache("last_wx_scene", b.scene);
      var c = b.query;
      if (setCache("init_wx_query", JSON.stringify(c)), "undefined" != typeof c.scene) {
        var d = decodeURIComponent(c.scene), e = d.match(new RegExp("(^|&)" + sdkWxminiPrefix + "aid=([^&]*)(&|$)")), f = d.match(new RegExp("(^|&)" + sdkWxminiPrefix + "adid=([^&]*)(&|$)"));
        e && "undefined" != typeof e[2] && e[2] && (c[sdkWxminiPrefix + "aid"] = e[2]), 
        f && "undefined" != typeof f[2] && f[2] && (c[sdkWxminiPrefix + "adid"] = f[2]), 
        console.log(d + "," + JSON.stringify(c));
      }
      var g = a.wx_appid, h = a.game_id, i = a.game_key, j = "undefined" != typeof c[sdkWxminiPrefix + "aid"] ? c[sdkWxminiPrefix + "aid"] : defaultAppId, k = "undefined" != typeof c[sdkWxminiPrefix + "adid"] ? c[sdkWxminiPrefix + "adid"] : defaultAdid;
      j == defaultAppId && "undefined" != typeof c.aid && (j = c.aid), k == defaultAdid && "undefined" != typeof c.adid && (k = c.adid), 
      setCache("init_sdk_appid", j), setCache("init_wx_appid", g), setCache("init_sdk_adid", k), 
      setCache("init_sdk_gameid", h), setCache("init_sdk_gamekey", i), setCache("init_wx_device_type", wx.getSystemInfoSync().platform);
      var l = "undefined" != typeof c[sdkWxminiPrefix + "ifs"] ? c[sdkWxminiPrefix + "ifs"] : 0, m = "undefined" != typeof c[sdkWxminiPrefix + "fsid"] ? c[sdkWxminiPrefix + "fsid"] : 0, n = "undefined" != typeof c[sdkWxminiPrefix + "fsaid"] ? c[sdkWxminiPrefix + "fsaid"] : 0, o = "undefined" != typeof c[sdkWxminiPrefix + "fsadid"] ? c[sdkWxminiPrefix + "fsadid"] : 0, p = "undefined" != typeof c[sdkWxminiPrefix + "fsuid"] ? c[sdkWxminiPrefix + "fsuid"] : 0;
      setCache("init_is_fromshare", l), setCache("init_fromshare_id", m), setCache("init_fromshare_appid", n), 
      setCache("init_fromshare_adid", o), setCache("init_fromshare_uid", p);
      var q = 0, r = "";
      "undefined" != typeof b.referrerInfo && (q = b.referrerInfo.appId, r = b.referrerInfo.extraData), 
      setCache("init_referrer_appid", q), setCache("init_referrer_data", r);
    }
    function getUserInfo(a, b, c, d) {
      var e = sdkBaseUrl + "getUserInfo", f = {
        app_id: getCache("init_sdk_appid"),
        game_id: getCache("init_sdk_gameid"),
        game_key: getCache("init_sdk_gamekey"),
        adid: getCache("init_sdk_adid"),
        wx_appid: getCache("init_wx_appid"),
        wx_scene: getCache("init_wx_scene"),
        wx_refer_appid: getCache("init_referrer_appid"),
        wx_refer_data: getCache("init_referrer_data"),
        wx_device_type: getCache("init_wx_device_type"),
        wx_code: a,
        is_fromshare: getCache("init_is_fromshare"),
        fromshare_id: getCache("init_fromshare_id"),
        fromshare_appid: getCache("init_fromshare_appid"),
        fromshare_uid: getCache("init_fromshare_uid"),
        fromshare_adid: getCache("init_fromshare_adid"),
        wx_res: "string" == typeof d && "undefined" != d ? d : "",
        env: __wxConfig.envVersion,
        wx_query: getCache("init_wx_query")
      }, g = function g(a) {
        if (console.log("init.back: " + JSON.stringify(a)), 0 == a.code) {
          var b = a.data;
          userInfo = b, setCache("init_sdk_appid", b.sdk_appid), setCache("init_sdk_openid", b.sdk_openid), 
          setCache("init_sdk_ext", b.sdk_ext);
          var d = b.sdk_ext;
          setCache("init_sdk_isprint", d.iprt), consoleLog(d);
        }
        c(a);
      };
      console.log("i:" + e + ",d:" + JSON.stringify(f)), request(e, f, g, "post", "getUserInfo");
    }
    function init(a, b, c) {
      console.log(a), clearInitCache(), getEnvVersion(), setInitCahe(a);
      var d = {
        success: function success(d) {
          if (console.log("init succ, " + JSON.stringify(d)), "undefined" != typeof d.code && d.code) getUserInfo(d.code, a, b, c); else {
            console.log("init fail wx login sble, " + JSON.stringify(d));
            var e = {
              code: 408,
              message: "wx login sble"
            };
            b(e);
          }
        },
        fail: function fail(a) {
          console.log("init fail, " + JSON.stringify(a));
          var c = {
            code: 408,
            message: JSON.stringify(a)
          };
          b(c);
        },
        complete: function complete(a) {}
      };
      wx.login(d);
    }
    function checkWxUserInfo(a, b) {
      var c = sdkBaseUrl + "checkWxUserInfo", d = {
        i: "undefined" != typeof userInfo.sdk_ext.i ? userInfo.sdk_ext.i : "",
        wx_appid: userInfo.wx_appid,
        wx_res: a
      }, e = function e(a) {
        b(a);
      };
      request(c, d, e, "post", "checkWxUserInfo");
    }
    function pay(a, b) {
      consoleLog("pay " + JSON.stringify(a));
      var c = sdkBaseUrl + "getPayType", d = {
        app_id: userInfo.sdk_appid,
        game_id: userInfo.sdk_gameid,
        open_id: userInfo.sdk_openid,
        wx_appid: userInfo.wx_appid,
        wx_device_type: getCache("init_wx_device_type"),
        wx_scene: getCache("init_wx_scene"),
        payment_amount: a.total_fee
      }, e = function e(c) {
        "undefined" != typeof c && 0 == c.code ? doPay(c.data, a, b) : (consoleLog("get type error. defualt midas"), 
        doPay(0, a, b));
      };
      request(c, d, e, "post", "getTypeCallback");
    }
    function doPay(a, b, c) {
      consoleLog("doPay " + a);
      var d = sdkPayDomain + "/userOrder/preparepay";
      b.wx_appid = userInfo.wx_appid, b.wx_device_type = getCache("init_wx_device_type"), 
      0 == a ? payMidas(d, b, c) : 1 == a ? payMiniPro(d, b, c) : 2 == a ? payKefu(d, b, c) : 3 == a ? payQRCode(d, b, c) : console.log("error pay.");
    }
    function payMidas(a, b, c) {
      b.payname = "pay_weixin_minigame_midas";
      var d = function d(a) {
        if (console.log("getp " + JSON.stringify(a)), 0 == a.code) {
          console.log("midas...");
          var b = a.data, d = function d(b) {
            var c = b;
            return c.app_id = userInfo.sdk_appid, c.game_id = userInfo.sdk_gameid, c.user_id = userInfo.sdk_ext.i, 
            c.orderno = a.orderno, c;
          };
          b.success = function(a) {
            consoleLog("pback suc." + JSON.stringify(a));
            var b = sdkBaseUrl + "payCallbackFront", c = d(a);
            c.is_success = 1;
            var e = function e(a) {
              console.log("suc. payCallback." + JSON.stringify(a));
            };
            request(b, c, e, "post", "payCallbackFront");
          }, b.fail = function(a) {
            consoleLog("pback fail." + JSON.stringify(a));
            var b = sdkBaseUrl + "addMidasFrontPayCallbackLog", c = d(a);
            c.is_success = 0;
            var e = function e(a) {
              console.log("fail payCallback." + JSON.stringify(a));
            };
            request(b, c, e, "post", "addMidasFrontPayCallbackLog");
          }, b.complete = function(a) {
            console.log("pback complete." + JSON.stringify(a));
          }, wx.requestMidasPayment(b);
        } else console.log(a), c(a);
      };
      request(a, b, d, "post", "payMidas");
    }
    function payMiniPro(a, b, c) {
      b.payname = "pay_weixin_pub_jump_minipro";
      var d = function d(a) {
        if (console.log("getpayMiniPro. " + JSON.stringify(a)), 0 == a.code) {
          var d = a.data, e = a.orderno, f = function f(a) {
            var c = {
              game_id: b.game_id,
              orderno: e,
              wx_app_id: d,
              device_type: wx.getSystemInfoSync().platform,
              open_id: b.open_id,
              is_accept: a
            }, f = sdkBaseUrl + "jump_mp_pay_log", g = function g(a) {};
            request(f, c, g, "post", "addJumpMpPayLog");
          }, g = {};
          g.appId = a.data, g.path = "pages/index/index?orderno=" + e, g.success = function(a) {
            f(1);
          }, g.fail = function(a) {
            f(0);
          }, g.complete = function(a) {
            consoleLog("jump complete." + JSON.stringify(a));
          }, wx.navigateToMiniProgram(g);
        } else console.log(a), c(a);
      };
      request(a, b, d, "post", "payMiniPro");
    }
    function payKefu(a, b, c) {
      b.payname = "pay_weixin_pub_kefu";
      var d = function d(a) {
        if (0 == a.code) {
          var b = sdkCdnDomain + "/gc/main/images/pay/pay_kefu_cz.gif";
          consoleLog(b), wx.showModal({
            title: "\u5145\u503c\u6559\u7a0b",
            content: "\u5373\u5c06\u8df3\u8f6c\u5b98\u65b9\u3010\u5ba2\u670d\u4f1a\u8bdd\u3011\u5145\u503c\n\u7ed9\u5ba2\u670d[\u56de\u590d\u5145\u503c]\u83b7\u53d6\u5145\u503c\u94fe\u63a5",
            showCancel: !1,
            confirmText: "\u5ba2\u670d\u5145\u503c",
            success: function success(a) {
              var c = {
                showMessageCard: !0,
                sendMessageTitle: "\u56de\u590d\u4efb\u610f\u6d88\u606f\u7ee7\u7eed",
                sendMessageImg: b
              };
              wx.openCustomerServiceConversation(c);
            }
          });
        } else console.log(a), c(a);
      };
      request(a, b, d, "post", "payKefu");
    }
    function payQRCode(a, b, c) {
      b.payname = "pay_weixin_qr";
      var d = function d(a) {
        if (console.log("payQRCode " + JSON.stringify(a)), 0 == a.code) {
          if (void 0 != a.data) {
            var b = {
              urls: [ a.data ]
            };
            wx.previewImage(b);
          }
        } else c(a);
      };
      request(a, b, d, "post", "payQRCode");
    }
    function share(a, b, c, d) {
      var e = userInfo.sdk_ext, f = e.f + "&" + sdkWxminiPrefix + "fsid=" + a + "&" + sdkWxminiPrefix + "adid=" + userInfo.sdk_adid;
      "undefined" != typeof d && "" != d && (f = f + "&" + d);
      var g = {
        title: b,
        imageUrl: c,
        query: f
      };
      consoleLog("share.." + JSON.stringify(g)), wxShareLog(a, 1), wx.shareAppMessage(g);
    }
    function shareInit(a, b, c, d) {
      var e = userInfo.sdk_ext, f = e.f + "&fsid=" + a + "&adid=" + userInfo.sdk_adid;
      "undefined" != typeof d && "" != d && (f = f + "&" + d), wx.showShareMenu();
      var g = {
        title: b,
        imageUrl: c,
        query: f
      };
      consoleLog("shari.." + JSON.stringify(g));
      var h = function h() {
        return console.log("shareinit.."), wxShareLog(a, 2), g;
      };
      wx.onShareAppMessage(h);
    }
    function shareTimeLineInit(a, b, c, d) {
      var e = userInfo.sdk_ext, f = e.f + "&fsid=" + a + "&adid=" + userInfo.sdk_adid;
      "undefined" != typeof d && "" != d && (f = f + "&" + d), wx.showShareMenu({
        withShareTicket: !0,
        menus: [ "shareAppMessage", "shareTimeline" ]
      });
      var g = {
        title: b,
        imageUrl: c,
        query: f
      };
      consoleLog("sharti.." + JSON.stringify(g));
      var h = function h() {
        return wxShareLog(a, 3), g;
      };
      wx.onShareTimeline(h);
    }
    function wxShareLog(a, b) {
      var c = sdkLogUrl + "wxShareLog", d = {
        app_id: userInfo.sdk_appid,
        open_id: userInfo.sdk_openid,
        game_id: userInfo.sdk_gameid,
        game_key: userInfo.sdk_gamekey,
        adid: userInfo.sdk_adid,
        share_type: b,
        share_id: a,
        wx_appid: userInfo.wx_appid,
        wx_scene: getCache("init_wx_scene"),
        wx_refer_appid: getCache("init_referrer_appid"),
        wx_refer_data: getCache("init_referrer_data"),
        wx_device_type: getCache("init_wx_device_type"),
        is_fromshare: getCache("init_is_fromshare"),
        fromshare_id: getCache("init_fromshare_id"),
        fromshare_appid: getCache("init_fromshare_appid"),
        fromshare_uid: getCache("init_fromshare_uid"),
        fromshare_adid: getCache("init_fromshare_adid"),
        sign: userInfo.sdk_ext.ss
      }, e = function e() {};
      request(c, d, e, "post", "wxShareLog");
    }
    function isMessageNormal(a, b, c, d) {
      consoleLog("isMessageNormal " + a + " " + b);
      var e = __wxConfig.envVersion;
      if (0 == userInfo.sdk_is_checkmsg && "release" == e) {
        var f = {
          code: 0,
          data: 1
        };
        c(f);
      } else {
        var g = sdkBaseUrl + "isMessageNormal", h = {
          app_id: userInfo.sdk_appid,
          adid: userInfo.sdk_adid,
          open_id: userInfo.sdk_openid,
          game_id: a,
          is_chat: "undefined" == typeof d ? 1 : 0,
          content: b,
          wx_scene: getCache("init_wx_scene"),
          env: e
        }, i = function i(a) {
          c(a);
        };
        request(g, h, i, "post", "isMessageNormal");
      }
    }
    function serverChooseLog(a, b) {
      reportLog("serverChooseLog", a, b);
    }
    function enterGameLog(a, b) {
      reportLog("enterGameLog", a, b);
    }
    function newcomerLog(a, b) {
      reportLog("newcomerLog", a, b);
    }
    function createRoleLog(a, b) {
      reportLog("createRoleLog", a, b);
    }
    function levelUpLog(a, b) {
      reportLog("levelUpLog", a, b);
    }
    function guanqiaStartLog(a, b) {
      a.wx_appid = userInfo.wx_appid, a.wx_scene = getCache("init_wx_scene"), reportLog("guanqiaStartLog", a, b);
    }
    function guanqiaEndLog(a, b) {
      a.wx_appid = userInfo.wx_appid, a.wx_scene = getCache("init_wx_scene"), reportLog("guanqiaEndLog", a, b);
    }
    function reportLog(a, b, c) {
      consoleLog(a);
      var d = function d() {
        var d = sdkLogUrl + a;
        b.adid = userInfo.sdk_adid, b.is_fromshare = getCache("init_is_fromshare"), b.wx_device_type = getCache("init_wx_device_type"), 
        request(d, b, c, "post", a);
      };
      switch (a) {
       case "createRoleLog":
       case "enterGameLog":
       case "levelUpLog":
       case "newcomerLog":
       case "guanqiaStartLog":
       case "guanqiaEndLog":
        d();
      }
    }
    function getPromoteList(a, b) {
      var c = sdkBaseUrl + "getPromoteList", d = {
        type: a,
        last_wx_scene: getCache("last_wx_scene"),
        wx_device_type: getCache("init_wx_device_type"),
        game_id: getCache("init_sdk_gameid")
      };
      request(c, d, b, "post", "getPromoteList");
    }
    function promotoClickLog(a, b) {
      var c = sdkLogUrl + "promotoClickLog";
      a.wx_appid = userInfo.wx_appid, a.wx_scene = getCache("init_wx_scene"), a.wx_device_type = getCache("init_wx_device_type"), 
      request(c, a, b, "post", "promotoClickLog");
    }
    function wxPromotoExposeLog(a, b) {
      var c = sdkLogUrl + "wxPromotoExposeLog";
      a.wx_appid = userInfo.wx_appid, a.wx_scene = getCache("init_wx_scene"), a.wx_device_type = getCache("init_wx_device_type"), 
      request(c, a, b, "post", "wxPromotoExposeLog");
    }
    function wxAdComponentCreate(a, b, c, d, e, f) {
      var g = userInfo.sdk_wxconf_adcomponent, h = {
        code: 0,
        message: "success",
        data: {
          component: ""
        }
      };
      if ("" != typeof g && "undefined" != typeof g[b] && "undefined" != typeof g[b][a]) {
        var i = "";
        if (g[b][a].id == c) {
          if (d = Math.max(d, g[b][a].intervals), 1 == b) i = wxAdComponentCreateRewardVideo(c); else if (2 == b) i = wxAdComponentCreateBanner(c, d, e); else if (3 == b) i = wxAdComponentCreateInterstitial(c); else if (4 == b) i = wxAdComponentCreateCustom(c, d, e); else if (5 == b) i = wxAdComponentCreateGrid(c, d, e); else {
            var j = "type error, not exists: " + b;
            h.code = 400, h.message = j;
          }
          h.data.component = i;
        } else {
          var j = "id error, not exists: " + c;
          h.code = 400, h.message = j;
        }
      } else {
        var j = "para loss: " + b + ", " + a;
        h.code = 400, h.message = j;
      }
      f(h);
    }
    function wxAdComponentCreateRewardVideo(a) {
      var b = {
        adUnitId: a
      };
      return wx.createRewardedVideoAd(b);
    }
    function wxAdComponentCreateBanner(a, b, c) {
      var d = {
        adUnitId: a,
        adIntervals: b,
        style: c
      };
      return wx.createBannerAd(d);
    }
    function wxAdComponentCreateInterstitial(a) {
      var b = {
        adUnitId: a
      };
      return wx.createInterstitialAd(b);
    }
    function wxAdComponentCreateCustom(a, b, c) {
      var d = {
        adUnitId: a,
        adIntervals: b,
        style: c
      };
      return wx.createCustomAd(d);
    }
    function wxAdComponentCreateGrid(a, b, c) {
      var d = {
        adUnitId: a,
        adIntervals: b,
        style: c,
        adTheme: "black",
        gridCount: 5
      };
      return wx.createGridAd(d);
    }
    var sdkDomain = "https://h5api.huixinhuyu.com", sdkPayDomain = "https://h5pay.huixinhuyu.com", sdkCdnDomain = "https://h5cdn.huixinhuyu.com", sdkLogUrl = "https://h5log.huixinhuyu.com/log/", sdkBaseUrl = "https://h5api.huixinhuyu.com/h5sdk/wxminigame/", sdkWxminiPrefix = "hxhy_", defaultAppId = 2600002, defaultAdid = 1e3, userInfo = {
      wx_appid: 0,
      sdk_appid: 0,
      sdk_gameid: 0,
      sdk_gamekey: "",
      sdk_adid: 0,
      sdk_openid: "",
      sdk_is_focus: 0,
      sdk_focus_wx_appid: "",
      sdk_is_share: 0,
      sdk_is_realverify: 0,
      sdk_is_bindphone: 0,
      sdk_is_fcm: 0,
      sdk_is_pay: 1,
      sdk_is_checkmsg: 0,
      sdk_ext: "",
      sdk_sign: "",
      sdk_wxconf_adcomponent: ""
    };
    module.exports = {
      init: init,
      checkWxUserInfo: checkWxUserInfo,
      pay: pay,
      share: share,
      shareInit: shareInit,
      shareTimeLineInit: shareTimeLineInit,
      isMessageNormal: isMessageNormal,
      wxAdComponentCreate: wxAdComponentCreate,
      serverChooseLog: serverChooseLog,
      enterGameLog: enterGameLog,
      newcomerLog: newcomerLog,
      createRoleLog: createRoleLog,
      levelUpLog: levelUpLog,
      guanqiaStartLog: guanqiaStartLog,
      guanqiaEndLog: guanqiaEndLog,
      getPromoteList: getPromoteList,
      promotoClickLog: promotoClickLog,
      wxPromotoExposeLog: wxPromotoExposeLog,
      checkIsForbidScene: checkIsForbidScene
    };
    cc._RF.pop();
  }, {} ]
}, {}, [ "SdkHuiXin", "moduleSdkHuiXin", "wxmini_game", "wxmini_game_bindphone_interface" ]);