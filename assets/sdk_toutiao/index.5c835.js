window.__require = function e(t, n, r) {
  function s(o, u) {
    if (!n[o]) {
      if (!t[o]) {
        var b = o.split("/");
        b = b[b.length - 1];
        if (!t[b]) {
          var a = "function" == typeof __require && __require;
          if (!u && a) return a(b, !0);
          if (i) return i(b, !0);
          throw new Error("Cannot find module '" + o + "'");
        }
        o = b;
      }
      var f = n[o] = {
        exports: {}
      };
      t[o][0].call(f.exports, function(e) {
        var n = t[o][1][e];
        return s(n || e);
      }, f, f.exports, e, t, n, r);
    }
    return n[o].exports;
  }
  var i = "function" == typeof __require && __require;
  for (var o = 0; o < r.length; o++) s(r[o]);
  return s;
}({
  ModuleTTSDK: [ function(require, module, exports) {
    "use strict";
    cc._RF.push(module, "9d701TVuKFA1ryg01kJmCua", "ModuleTTSDK");
    (function() {
      var _miniSdk = require("./ttmini_game");
      window["tt_sdk"] = _miniSdk;
      console.log("module finish ttmini_game");
    })();
    cc._RF.pop();
  }, {
    "./ttmini_game": "ttmini_game"
  } ],
  TTSDKMiniGame: [ function(require, module, exports) {
    "use strict";
    cc._RF.push(module, "28ad5ZkYBlLjaIlEh+2WFgJ", "TTSDKMiniGame");
    "use strict";
    var __extends = this && this.__extends || function() {
      var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || {
          __proto__: []
        } instanceof Array && function(d, b) {
          d.__proto__ = b;
        } || function(d, b) {
          for (var p in b) Object.prototype.hasOwnProperty.call(b, p) && (d[p] = b[p]);
        };
        return extendStatics(d, b);
      };
      return function(d, b) {
        extendStatics(d, b);
        function __() {
          this.constructor = d;
        }
        d.prototype = null === b ? Object.create(b) : (__.prototype = b.prototype, new __());
      };
    }();
    var __decorate = this && this.__decorate || function(decorators, target, key, desc) {
      var c = arguments.length, r = c < 3 ? target : null === desc ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
      if ("object" === typeof Reflect && "function" === typeof Reflect.decorate) r = Reflect.decorate(decorators, target, key, desc); else for (var i = decorators.length - 1; i >= 0; i--) (d = decorators[i]) && (r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r);
      return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __awaiter = this && this.__awaiter || function(thisArg, _arguments, P, generator) {
      function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
          resolve(value);
        });
      }
      return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
          try {
            step(generator.next(value));
          } catch (e) {
            reject(e);
          }
        }
        function rejected(value) {
          try {
            step(generator["throw"](value));
          } catch (e) {
            reject(e);
          }
        }
        function step(result) {
          result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
      });
    };
    var __generator = this && this.__generator || function(thisArg, body) {
      var _ = {
        label: 0,
        sent: function() {
          if (1 & t[0]) throw t[1];
          return t[1];
        },
        trys: [],
        ops: []
      }, f, y, t, g;
      return g = {
        next: verb(0),
        throw: verb(1),
        return: verb(2)
      }, "function" === typeof Symbol && (g[Symbol.iterator] = function() {
        return this;
      }), g;
      function verb(n) {
        return function(v) {
          return step([ n, v ]);
        };
      }
      function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
          if (f = 1, y && (t = 2 & op[0] ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 
          0) : y.next) && !(t = t.call(y, op[1])).done) return t;
          (y = 0, t) && (op = [ 2 & op[0], t.value ]);
          switch (op[0]) {
           case 0:
           case 1:
            t = op;
            break;

           case 4:
            _.label++;
            return {
              value: op[1],
              done: false
            };

           case 5:
            _.label++;
            y = op[1];
            op = [ 0 ];
            continue;

           case 7:
            op = _.ops.pop();
            _.trys.pop();
            continue;

           default:
            if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (6 === op[0] || 2 === op[0])) {
              _ = 0;
              continue;
            }
            if (3 === op[0] && (!t || op[1] > t[0] && op[1] < t[3])) {
              _.label = op[1];
              break;
            }
            if (6 === op[0] && _.label < t[1]) {
              _.label = t[1];
              t = op;
              break;
            }
            if (t && _.label < t[2]) {
              _.label = t[2];
              _.ops.push(op);
              break;
            }
            t[2] && _.ops.pop();
            _.trys.pop();
            continue;
          }
          op = body.call(thisArg, _);
        } catch (e) {
          op = [ 6, e ];
          y = 0;
        } finally {
          f = t = 0;
        }
        if (5 & op[0]) throw op[1];
        return {
          value: op[0] ? op[1] : void 0,
          done: true
        };
      }
    };
    var __values = this && this.__values || function(o) {
      var s = "function" === typeof Symbol && Symbol.iterator, m = s && o[s], i = 0;
      if (m) return m.call(o);
      if (o && "number" === typeof o.length) return {
        next: function() {
          o && i >= o.length && (o = void 0);
          return {
            value: o && o[i++],
            done: !o
          };
        }
      };
      throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
    };
    var __read = this && this.__read || function(o, n) {
      var m = "function" === typeof Symbol && o[Symbol.iterator];
      if (!m) return o;
      var i = m.call(o), r, ar = [], e;
      try {
        while ((void 0 === n || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
      } catch (error) {
        e = {
          error: error
        };
      } finally {
        try {
          r && !r.done && (m = i["return"]) && m.call(i);
        } finally {
          if (e) throw e.error;
        }
      }
      return ar;
    };
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.TTSDKMiniGame = void 0;
    var SDKInterface_1 = require("../../app/game/ctrl/sdk/SDKInterface");
    var SDKMgr_1 = require("../../app/game/ctrl/sdk/SDKMgr");
    var EngineMain_1 = require("../../engine/EngineMain");
    var Container_1 = require("../../framework/container/Container");
    var FW_1 = require("../../framework/FW");
    var MD5Util_1 = require("../../libutil/MD5Util");
    var TTSDKMiniGame = function(_super) {
      __extends(TTSDKMiniGame, _super);
      function TTSDKMiniGame() {
        var _this = null !== _super && _super.apply(this, arguments) || this;
        _this.wxAppId = "tta52ed3933a1e089d02";
        _this.gameId = "2600107";
        _this.gameKey = "0ebf0106467ac207";
        _this._button = [];
        _this.recorderStatus = 0;
        _this.gameRecorder = null;
        _this._getJumpReward = false;
        return _this;
      }
      TTSDKMiniGame.prototype.getUserType = function() {
        return SDKMgr_1.SDK_TYPE.TouTiao;
      };
      Object.defineProperty(TTSDKMiniGame.prototype, "gameSecret", {
        get: function() {
          var _a;
          return (null === (_a = this._loginInfo) || void 0 === _a ? void 0 : _a.gameSecret) || "89255443be87af9b0b21dd7c87befa2a";
        },
        enumerable: false,
        configurable: true
      });
      Object.defineProperty(TTSDKMiniGame.prototype, "gamePaySecret", {
        get: function() {
          var _a;
          return (null === (_a = this._loginInfo) || void 0 === _a ? void 0 : _a.gamePaySecret) || "612e5b767093b5857cb085fc7d778961";
        },
        enumerable: false,
        configurable: true
      });
      TTSDKMiniGame.prototype.objKeySort = function(obj) {
        var newkey = Object.keys(obj).sort();
        var newObj = {};
        for (var i = 0; i < newkey.length; i++) newObj[newkey[i]] = obj[newkey[i]];
        return newObj;
      };
      TTSDKMiniGame.prototype.randomString = function(len) {
        len = len || 32;
        var $chars = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678";
        var maxPos = $chars.length;
        var pwd = "";
        for (var i = 0; i < len; i++) pwd += $chars.charAt(Math.floor(Math.random() * maxPos));
        return pwd;
      };
      TTSDKMiniGame.prototype.makeSign = function(data, secret) {
        this.log(data);
        var d = this.objKeySort(data);
        this.log(d);
        var arr = [], name;
        for (name in d) arr.push(name + "=" + d[name]);
        var str = arr.join("&") + "&" + secret;
        this.log(str);
        return MD5Util_1.MD5Util.md5(str);
      };
      TTSDKMiniGame.prototype.initAsync = function() {
        return __awaiter(this, void 0, Promise, function() {
          return __generator(this, function(_a) {
            this._sdk = window["tt_sdk"];
            if (!this._sdk) return [ 2 ];
            this.log("initAsync");
            return [ 2 ];
          });
        });
      };
      TTSDKMiniGame.prototype.getExtInfo = function() {
        if (this._gameExtInfo) return this._gameExtInfo;
        var wx = window["tt"];
        if (wx) {
          this._gameExtInfo = wx.getLaunchOptionsSync().query;
          this.log("\u83b7\u53d6SDK\u989d\u5916\u53c2\u6570..", JSON.stringify(this._gameExtInfo));
        }
        this._gameExtInfo || this.error("\u8c03\u7528SDK\u989d\u5916\u53c2\u6570\u63a5\u53e3\u5f02\u5e38!!!");
        return this._gameExtInfo;
      };
      TTSDKMiniGame.prototype.getServerAddress = function(serverParams) {
        return __awaiter(this, void 0, Promise, function() {
          var arr, gameExtInfo, inviteRoleId, params, host, url, res;
          return __generator(this, function(_a) {
            switch (_a.label) {
             case 0:
              arr = [];
              null != serverParams.type && arr.push("type=" + serverParams.type);
              null != serverParams.platform && arr.push("platform=" + serverParams.platform);
              null != serverParams.code && arr.push("code=" + serverParams.code);
              null != serverParams.token && arr.push("token=" + serverParams.token);
              null != serverParams.time && arr.push("time=" + serverParams.time);
              null != serverParams.isWss && arr.push("isWss=" + serverParams.isWss);
              null != serverParams.otherOpenId && arr.push("otherOpenId=" + serverParams.otherOpenId);
              null != serverParams.selectServerId && arr.push("selectServerId=" + serverParams.selectServerId);
              gameExtInfo = this.getExtInfo();
              if (gameExtInfo) {
                inviteRoleId = gameExtInfo["inviteRoleId"];
                if (inviteRoleId) {
                  arr.push("inviteRoleId=" + inviteRoleId);
                  this.log("inviteRoleId====", inviteRoleId);
                }
              }
              params = arr.join("&");
              host = this.ModelMgr.ModelPublicData.getServerAddressUrl();
              url = host + "?" + params;
              return [ 4, FW_1.FW.Http.get(url, null, null, true) ];

             case 1:
              res = _a.sent();
              if (res) {
                this._loginInfo = res.content;
                if (res.result) return [ 2, res.content ];
              }
              return [ 2, null ];
            }
          });
        });
      };
      TTSDKMiniGame.prototype.init = function() {
        this.log("ooo");
      };
      TTSDKMiniGame.prototype.initSdk = function() {
        var e_1, _a, e_2, _b;
        if (!this._sdkInfo) {
          this.error("sdk\u767b\u5f55\u6570\u636e\u4e3a\u7a7a!!, \u8bf7\u68c0\u67e5");
          return;
        }
        this._adComponentList = [];
        var infos = this._sdkInfo.sdk_adcomponent;
        "string" == typeof infos && (infos = JSON.parse(infos));
        if (infos) {
          for (var key in infos) if (Object.prototype.hasOwnProperty.call(infos, key)) {
            var dic = infos[key];
            for (var adKey in dic) if (Object.prototype.hasOwnProperty.call(dic, adKey)) {
              var element = dic[adKey];
              1 == Number(key) && this._adComponentList.push({
                type: Number(key),
                id: element.id,
                intervals: Number(element.intervals)
              });
            }
          }
        } else this.warn("\u6ca1\u6709\u5e7f\u544a\u6587\u6848??\u68c0\u67e5sdk\u6570\u636e", JSON.stringify(this._sdkInfo));
        this._shareFirendList = [];
        infos = this._sdkInfo.sdk_shareconf;
        "string" == typeof infos && (infos = JSON.parse(infos));
        if (infos) {
          console.log("\u5206\u4eabinfos:", infos);
          try {
            for (var infos_1 = __values(infos), infos_1_1 = infos_1.next(); !infos_1_1.done; infos_1_1 = infos_1.next()) {
              var iterator = infos_1_1.value;
              this._shareFirendList.push({
                share_id: iterator.share_id,
                share_ticket: iterator.share_ticket,
                share_title: iterator.share_title,
                share_image: iterator.share_image
              });
            }
          } catch (e_1_1) {
            e_1 = {
              error: e_1_1
            };
          } finally {
            try {
              infos_1_1 && !infos_1_1.done && (_a = infos_1.return) && _a.call(infos_1);
            } finally {
              if (e_1) throw e_1.error;
            }
          }
        } else this.warn("\u6ca1\u6709\u597d\u53cb\u5206\u4eab\u6587\u6848??\u68c0\u67e5sdk\u6570\u636e", JSON.stringify(this._sdkInfo));
        this._shareLineList = [];
        infos = null;
        "string" == typeof infos && (infos = JSON.parse(infos));
        if (infos) try {
          for (var infos_2 = __values(infos), infos_2_1 = infos_2.next(); !infos_2_1.done; infos_2_1 = infos_2.next()) {
            var iterator = infos_2_1.value;
            this._shareLineList.push({
              share_id: iterator.share_id,
              share_title: iterator.share_title,
              share_image: iterator.share_image
            });
          }
        } catch (e_2_1) {
          e_2 = {
            error: e_2_1
          };
        } finally {
          try {
            infos_2_1 && !infos_2_1.done && (_b = infos_2.return) && _b.call(infos_2);
          } finally {
            if (e_2) throw e_2.error;
          }
        } else this.warn("\u6ca1\u6709\u670b\u53cb\u5708\u5206\u4eab\u6587\u6848??\u68c0\u67e5sdk\u6570\u636e", JSON.stringify(this._sdkInfo));
        var info = this.getRandomInfoByList(this._shareFirendList);
        info && this._sdk.shareInit({
          channel: null,
          templateId: info.share_id,
          title: info.share_title,
          desc: "",
          imageUrl: info.share_image,
          extra: "",
          gameExtInfo: ""
        });
        var t = this;
        var wx = window["tt"];
        wx.setKeepScreenOn({
          keepScreenOn: true,
          success: function(res) {
            t.log("\u5c4f\u5e55\u5e38\u4eae\u5df2\u5f00\u542f");
          },
          fail: function(res) {
            t.warn("setKeepScreenOn\u8c03\u7528\u5931\u8d25");
          }
        });
      };
      TTSDKMiniGame.prototype.login = function(username, password) {
        return __awaiter(this, void 0, Promise, function() {
          var userInfo;
          var _this = this;
          return __generator(this, function(_a) {
            switch (_a.label) {
             case 0:
              this.log("\u767b\u5f55\u6570\u636e:", this.gameId, this.gameKey, this.wxAppId);
              return [ 4, new Promise(function(resolve, reject) {
                var conf = {
                  game_id: _this.gameId,
                  game_key: _this.gameKey,
                  wx_appid: _this.wxAppId
                };
                _this._sdk.init(conf, function(res) {
                  if (!res) {
                    reject(null);
                    return;
                  }
                  if ("string" == typeof res) {
                    _this.log("\u767b\u5f55\u8fd4\u56de\u6570\u636e\u8f6c\u6362\u4e3aJson");
                    res = JSON.parse(res);
                  }
                  _this.log("\u767b\u5f55\u8fd4\u56de\u6570\u636e:", JSON.stringify(res));
                  if (0 == res.code) {
                    _this._sdkInfo = res.data;
                    var result = {};
                    var exclude = [ "sdk_ext", "sdk_shareconf", "sdk_adcomponent" ];
                    var sdk_ext = res.data.sdk_ext;
                    for (var key in res.data) if (Object.prototype.hasOwnProperty.call(res.data, key)) {
                      if (exclude.includes(key)) continue;
                      result[key] = res.data[key];
                    }
                    _this.log("token:", result);
                    var openid = void 0;
                    sdk_ext && (openid = sdk_ext.wd);
                    userInfo = {
                      code: _this._sdkInfo.sdk_openid,
                      time: _this.TimeUtil.LocalNow(),
                      token: JSON.stringify(result),
                      openid: openid
                    };
                    _this.initSdk();
                    resolve(userInfo);
                  } else reject(null);
                });
              }) ];

             case 1:
              userInfo = _a.sent();
              this.log("\u767b\u5f55\u6570\u636e:userInfo", JSON.stringify(userInfo));
              return [ 2, [ !!userInfo, userInfo ] ];
            }
          });
        });
      };
      TTSDKMiniGame.prototype.loginOrRegist = function() {
        return __awaiter(this, void 0, Promise, function() {
          var userInfo, _loop_1, this_1, state_1;
          var _this = this;
          return __generator(this, function(_a) {
            switch (_a.label) {
             case 0:
              _loop_1 = function() {
                var sending, promise;
                return __generator(this, function(_a) {
                  switch (_a.label) {
                   case 0:
                    sending = true;
                    promise = this_1.login("", "");
                    promise && promise.then(function(arg) {
                      var _a = __read(arg, 2), success = _a[0], result = _a[1];
                      _this.log("\u767b\u5f55:" + success);
                      _this.log(JSON.stringify(result));
                      success && result && (userInfo = result);
                      sending = false;
                    }).catch(function(e) {
                      e && _this.error(e);
                      sending = false;
                    });
                    return [ 4, FW_1.FW.Task.waitUntil(function() {
                      return false == sending;
                    }) ];

                   case 1:
                    _a.sent();
                    if (!(null == userInfo)) return [ 3, 3 ];
                    return [ 4, FW_1.FW.Task.delay(2e3) ];

                   case 2:
                    _a.sent();
                    return [ 3, 4 ];

                   case 3:
                    return [ 2, "break" ];

                   case 4:
                    return [ 2 ];
                  }
                });
              };
              this_1 = this;
              _a.label = 1;

             case 1:
              if (!!userInfo) return [ 3, 3 ];
              return [ 5, _loop_1() ];

             case 2:
              state_1 = _a.sent();
              if ("break" === state_1) return [ 3, 3 ];
              return [ 3, 1 ];

             case 3:
              return [ 2, userInfo ];
            }
          });
        });
      };
      TTSDKMiniGame.prototype.PPP = function(payInfo) {
        return __awaiter(this, void 0, Promise, function() {
          var _this = this;
          return __generator(this, function(_a) {
            new Promise(function(resolve, reject) {
              var timestamp = _this.TimeUtil.LocalNow();
              var p = {
                app_id: _this._sdkInfo.sdk_appid,
                game_id: _this.gameId,
                game_key: _this.gameKey,
                open_id: _this._sdkInfo.sdk_openid,
                total_fee: payInfo.price.toFixed(2),
                cp_orderno: payInfo.orderId,
                object_id: payInfo.orderId,
                object_name: payInfo.orderName,
                object_desc: "",
                timestamp: timestamp,
                nonce: _this.randomString(8),
                currency_type: "RMB",
                cp_callback_url: payInfo.payUrl,
                server_id: payInfo.serverId,
                server_name: payInfo.serverName,
                role_id: payInfo.userId + "",
                role_name: payInfo.userName,
                role_level: payInfo.level,
                role_level_reborn: 0,
                role_vip_level: payInfo.vipLevel,
                score: payInfo.power
              };
              p.sign = _this.makeSign(p, _this.gamePaySecret);
              _this._sdk.pay(p, function(res) {
                console.log("payback." + JSON.stringify(res));
                resolve(res);
              });
            });
            return [ 2, null ];
          });
        });
      };
      TTSDKMiniGame.prototype.Share = function(type) {
        return __awaiter(this, void 0, Promise, function() {
          var info, voRoleSelf, query;
          return __generator(this, function(_a) {
            info = this.getRandomInfoByList(this._shareFirendList);
            console.log("info:", info);
            voRoleSelf = this.ModelMgr.ModelRole.GetRole();
            query = "inviteRoleId=" + voRoleSelf.GetRoleId();
            this.log("query====", query);
            this._sdk.share(info.share_id, "", info.share_ticket, info.share_title, "", info.share_image, {}, query, function() {});
            return [ 2, null ];
          });
        });
      };
      TTSDKMiniGame.prototype.submitDataUserInfo = function(type, params) {
        var _this = this;
        var roleVo = this.ModelMgr.ModelVO.VORoleBag.GetOne();
        if (!roleVo) {
          this.error("submitDataUserInfo VORoleBag null");
          return;
        }
        var role_id = roleVo.GetRoleId();
        var role_name = roleVo.GetName();
        var role_level = roleVo.GetLevel();
        var power = this.ModelMgr.ModelRole.getRoleAtk();
        var actionlogin = this.ModelMgr.ModelAccount.getRoleInfo();
        var submitInfo = {
          app_id: this._sdkInfo.sdk_appid,
          game_id: this.gameId,
          game_key: this.gameKey,
          open_id: this._sdkInfo.sdk_openid,
          server_id: actionlogin.serverAddress.id,
          server_name: actionlogin.serverAddress.name,
          role_id: role_id + "",
          role_name: role_name,
          role_level: role_level,
          role_vip_level: 0
        };
        var is_finish_newcomer = this.ModelChapterNew.checkLevelPass(1) ? 1 : 0;
        switch (type) {
         case SDKInterface_1.SubmitDataUserInfoType.enterGame:
          submitInfo.score = power;
          submitInfo.role_level_reborn = 0;
          submitInfo.is_finish_newcomer = is_finish_newcomer;
          submitInfo.is_new = params.is_new_create || 0;
          submitInfo.sign = this.makeSign(submitInfo, this.gameSecret);
          this.log("\u4e0a\u62a5sign:", submitInfo.sign);
          this._sdk.enterGameLog(submitInfo, function(res) {
            _this.log("\u4e0a\u62a5\u8fdb\u5165\u6e38\u620f\u4fe1\u606f!!", JSON.stringify(res));
          });
          break;

         case SDKInterface_1.SubmitDataUserInfoType.createRole:
          submitInfo.score = power;
          submitInfo.sign = this.makeSign(submitInfo, this.gameSecret);
          this._sdk.createRoleLog(submitInfo, function(res) {
            _this.log("\u4e0a\u62a5\u521b\u89d2\u65e5\u5fd7!!", JSON.stringify(res));
          });
          break;

         case SDKInterface_1.SubmitDataUserInfoType.upRoleLevel:
          submitInfo.score = power;
          submitInfo.is_finish_newcomer = is_finish_newcomer;
          submitInfo.sign = this.makeSign(submitInfo, this.gameSecret);
          this._sdk.levelUpLog(submitInfo, function(res) {
            _this.log("\u4e0a\u62a5\u5347\u7ea7\u65e5\u5fd7!!", JSON.stringify(res));
          });
          break;

         case SDKInterface_1.SubmitDataUserInfoType.newcomerFinish:
          submitInfo.score = power;
          submitInfo.is_finish_newcomer = 1;
          submitInfo.sign = this.makeSign(submitInfo, this.gameSecret);
          this._sdk.newcomerLog(submitInfo, function(res) {
            _this.log("\u4e0a\u62a5\u65b0\u624b\u65e5\u5fd7!!", JSON.stringify(res));
          });
          break;

         case SDKInterface_1.SubmitDataUserInfoType.wxPromoto:
          submitInfo.promoto_id = params.advertisement_id.toString() || "";
          submitInfo.promoto_type = params.advertisement_type || 0;
          submitInfo.count = params.advertisement_count || 0;
          submitInfo.is_finish = params.advertisement_result || 1;
          submitInfo.sign = this.makeSign(submitInfo, this.gameSecret);
          this._sdk.promotoExposeLog(submitInfo, function(res) {
            _this.log("\u4e0a\u62a5\u5e7f\u544a\u65e5\u5fd7!!", JSON.stringify(res));
          });
        }
      };
      TTSDKMiniGame.prototype.messageCheck = function(content, isChat) {
        var _this = this;
        this._sdkInfo && 1 == this._sdkInfo.sdk_is_checkmsg ? this._sdk.isMessageNormal(this.gameId, content, function(res) {
          _this.log("\u804a\u5929\u68c0\u6d4b!!", JSON.stringify(res));
        }, isChat ? 0 : 1) : this.log("\u804a\u5929\u68c0\u6d4b\u63a5\u53e3\u672a\u5f00\u542f!!");
      };
      TTSDKMiniGame.prototype.getShowPay = function() {
        var _a;
        return 1 == (null === (_a = this._sdkInfo) || void 0 === _a ? void 0 : _a.sdk_is_pay);
      };
      TTSDKMiniGame.prototype.Advertisement = function(gameItemId) {
        return __awaiter(this, void 0, Promise, function() {
          var t, result, info, wxAdType, wxAdunitId, wxAdIntervals, clearCallback;
          return __generator(this, function(_a) {
            switch (_a.label) {
             case 0:
              t = this;
              result = SDKInterface_1.AdvertisementResult.PULL_FAILED;
              info = t.getRandomInfoByList(t._adComponentList);
              if (!info) {
                t.warn("\u6ca1\u6709\u63a5\u5165\u5e7f\u544a, \u6216\u5e7f\u544a\u6570\u636e\u4e3a\u7a7a, \u8bf7\u524d\u5f80\u68c0\u67e5\u767b\u5f55\u6570\u636e!!");
                return [ 2, result ];
              }
              wxAdType = info.type;
              wxAdunitId = info.id;
              wxAdIntervals = info.intervals;
              clearCallback = null;
              return [ 4, new Promise(function(resolve, reject) {
                var cssStyle = {
                  left: 10,
                  top: 76,
                  width: 320
                };
                t.log("\u8c03\u7528\u5e7f\u544a\u7ec4\u4ef6:", info);
                t._sdk.adComponentCreate(gameItemId, wxAdType, wxAdunitId, wxAdIntervals, cssStyle, function(ret) {
                  var _a;
                  t.log("\u5e7f\u544a\u4fe1\u606f:", ret);
                  var videoAd;
                  0 == (null === ret || void 0 === ret ? void 0 : ret.code) && (videoAd = null === (_a = ret.data) || void 0 === _a ? void 0 : _a.component);
                  if (null == videoAd) {
                    t.error("\u521b\u5efa\u5e7f\u544a\u7ec4\u4ef6\u5931\u8d25");
                    result = SDKInterface_1.AdvertisementResult.PULL_FAILED;
                    resolve(false);
                    return false;
                  }
                  var onLoadFunc = function() {
                    console.log("\u5e7f\u544a\u89c6\u9891 \u62c9\u53d6\u6210\u529f");
                  };
                  videoAd.onLoad(onLoadFunc);
                  var onErrorFunc = function(err) {
                    console.error("\u5e7f\u544a\u89c6\u9891 \u62c9\u53d6\u5931\u8d25");
                    console.error(err);
                  };
                  videoAd.onError(onErrorFunc);
                  var onCloseFunc = null;
                  clearCallback = function() {
                    if (null != onCloseFunc) {
                      videoAd.offLoad(onLoadFunc);
                      videoAd.offError(onErrorFunc);
                      videoAd.offClose(onCloseFunc);
                      onLoadFunc = null;
                      onErrorFunc = null;
                      onCloseFunc = null;
                    }
                    clearCallback = null;
                  };
                  onCloseFunc = function(res) {
                    null != clearCallback && clearCallback();
                    console.log("\u5e7f\u544a\u89c6\u9891 \u5173\u95ed");
                    console.log(res);
                    if (res && res.isEnded || void 0 === res) {
                      var timeAd = t.ModelMgr.ModelRole.GetAdvertTimes(gameItemId);
                      var data = {
                        advertisement_id: wxAdunitId,
                        advertisement_type: wxAdType,
                        advertisement_count: timeAd + 1,
                        advertisement_result: 1
                      };
                      t.submitDataUserInfo(SDKInterface_1.SubmitDataUserInfoType.wxPromoto, data);
                      result = SDKInterface_1.AdvertisementResult.PLAY_FINISH;
                      resolve(result);
                    } else {
                      FW_1.FW.showTip("\u89c6\u9891\u64ad\u653e\u672a\u5b8c\u6210\uff0c\u65e0\u6cd5\u83b7\u5f97\u5956\u52b1");
                      result = SDKInterface_1.AdvertisementResult.PLAY_FAIL;
                      resolve(false);
                    }
                  };
                  videoAd.onClose(onCloseFunc);
                  videoAd.show().then(function() {
                    console.log("\u5e7f\u544a\u89c6\u9891 \u5e7f\u544a\u663e\u793a\u6210\u529f");
                  }).catch(function(err) {
                    console.log("\u5e7f\u544a\u89c6\u9891 \u663e\u793a\u5931\u8d25, \u518d\u6b21\u624b\u52a8\u62c9\u53d6");
                    console.log(null === err || void 0 === err ? void 0 : err.errMsg);
                    videoAd.load().then(function() {
                      console.log("\u5e7f\u544a\u89c6\u9891 \u624b\u52a8\u62c9\u53d6\u6210\u529f load");
                      videoAd.show().then(function() {
                        console.log("\u5e7f\u544a\u89c6\u9891 \u5e7f\u544a\u663e\u793a\u6210\u529f");
                      }).catch(function(err) {
                        FW_1.FW.showTip("\u52a0\u8f7d\u5931\u8d25,\u8bf7\u7a0d\u540e\u518d\u8bd5");
                        console.warn("\u5e7f\u544a\u89c6\u9891 \u5e7f\u544a\u663e\u793a\u5931\u8d25");
                        result = SDKInterface_1.AdvertisementResult.PULL_FAILED;
                        resolve(false);
                      });
                    }).catch(function(err) {
                      FW_1.FW.showTip("\u52a0\u8f7d\u5931\u8d25,\u8bf7\u7a0d\u540e\u518d\u8bd5");
                      console.warn("\u5e7f\u544a\u89c6\u9891 \u624b\u52a8\u62c9\u53d6\u5931\u8d25 load");
                      console.log(null === err || void 0 === err ? void 0 : err.errMsg);
                      result = SDKInterface_1.AdvertisementResult.PULL_FAILED;
                      resolve(false);
                    });
                  });
                });
              }) ];

             case 1:
              _a.sent();
              null != clearCallback && clearCallback();
              return [ 2, result ];
            }
          });
        });
      };
      TTSDKMiniGame.prototype.HasADVideo = function() {
        return true;
      };
      TTSDKMiniGame.prototype.getRandomInfoByList = function(list) {
        if (!list || !list.length) return null;
        var len = list.length;
        while (true) {
          var random = Math.floor(Math.random() * len);
          if (list[random]) return list[random];
        }
      };
      TTSDKMiniGame.prototype.Restart = function() {
        var wx = window["tt"];
        wx && wx.restartMiniProgram ? wx.restartMiniProgram() : wx && wx.restartMiniProgramSync && wx.restartMiniProgramSync();
        return true;
      };
      TTSDKMiniGame.prototype.hasCustomerService = function() {
        var tt = window["tt"];
        if (!(null === tt || void 0 === tt ? void 0 : tt.openCustomerServiceConversation)) return false;
        return true;
      };
      TTSDKMiniGame.prototype.createContactButton = function(url, width, height, x, y, callback, caller) {
        void 0 === url && (url = "");
        void 0 === callback && (callback = null);
        void 0 === caller && (caller = null);
        if (!EngineMain_1.EngineMain.isMiniGameDouyin) return;
        var wx = window["tt"];
        if (null == (null === wx || void 0 === wx ? void 0 : wx.createContactButton)) {
          console.warn("\u6ca1\u6709\u5ba2\u670d\u6309\u94ae");
          return;
        }
        var nx = x;
        var ny = y;
        if (this._button.length > 0) {
          nx = this._button[0];
          ny = this._button[1];
        } else {
          this._button[0] = nx;
          this._button[1] = ny;
        }
        console.log("createContactButton \u5ba2\u670dxy:", nx, ny, width, height);
        var button = wx.createContactButton({
          type: "image",
          image: url,
          style: {
            left: nx,
            top: ny,
            width: width,
            height: height,
            lineHeight: 60,
            backgroundColor: "#ffffff",
            textAlign: "center",
            fontSize: 16,
            borderRadius: 4,
            borderColor: "#ffffff",
            borderWidth: 0,
            textColor: "#ffffff"
          },
          success: function(res) {
            console.log("createContactButton success", res);
          },
          fail: function(res) {
            console.log("createContactButton fail", res);
          },
          complete: function(res) {
            console.log("createContactButton complete", res);
          }
        });
        var listener = function(res) {
          console.log("createContactButton onTap:");
          console.log(res);
          var userData = res;
          callback && (caller ? callback.call(caller, userData) : callback(userData));
        };
        button.onTap(listener);
        var hide = function() {
          button && button.hide();
        };
        var show = function() {
          button && button.show();
        };
        var destroyCallback = function() {
          if (button) {
            button.offTap(listener);
            button.destroy();
            button = null;
          }
        };
        function handleError(res) {
          console.log(res.errMsg);
        }
        button.onError(handleError);
        button.offError(handleError);
        return {
          button: button,
          destroy: destroyCallback,
          hide: hide,
          show: show,
          x: x,
          y: y
        };
      };
      TTSDKMiniGame.prototype.openCustomerService = function() {
        var tt = window["tt"];
        if (!tt) return;
        if (null == (null === tt || void 0 === tt ? void 0 : tt.openCustomerServiceConversation)) {
          console.warn("\u6ca1\u6709\u5ba2\u670d\u4e2d\u5fc3");
          return;
        }
        tt.openCustomerServiceConversation({
          sendMessageTitle: FW_1.FW.G_W("TTSDKMiniGame_1"),
          success: function(rsp) {
            console.warn("\u5ba2\u670d\u4f1a\u8bdd\u6210\u529f", rsp);
          },
          fail: function(rsp) {
            console.warn("\u5ba2\u670d\u4f1a\u8bdd\u5931\u8d25", rsp);
          }
        });
      };
      TTSDKMiniGame.prototype.checkUpdate = function() {
        return __awaiter(this, void 0, Promise, function() {
          var wx, errMsg, retWxUpdate;
          return __generator(this, function(_a) {
            switch (_a.label) {
             case 0:
              if (!EngineMain_1.EngineMain.isMiniGameDouyin) return [ 2 ];
              wx = window["tt"] || window["wx"];
              if (null == (null === wx || void 0 === wx ? void 0 : wx.getUpdateManager) || "function" != typeof (null === wx || void 0 === wx ? void 0 : wx.getUpdateManager)) {
                console.error("\u7248\u672c\u8fc7\u4f4e,\u5c0f\u6e38\u620f\u65e0\u6cd5\u68c0\u67e5\u66f4\u65b0(\u57fa\u7840\u5e93>=1.9.90)");
                return [ 2 ];
              }
              errMsg = null;
              retWxUpdate = null;
              console.warn("\u6296\u97f3\u5c0f\u6e38\u620f,\u68c0\u67e5\u66f4\u65b0");
              return [ 4, new Promise(function(resolve, reject) {
                var updateManager = wx.getUpdateManager();
                updateManager.onCheckForUpdate(function(res) {
                  console.log("onCheckForUpdate");
                  console.warn(res.hasUpdate);
                  if (res.hasUpdate) {
                    console.warn("\u5fae\u4fe1\u5c0f\u6e38\u620f\u6709\u66f4\u65b0");
                    tt.showToast({
                      title: "\u6709\u53ef\u7528\u66f4\u65b0\u7248\u672c",
                      icon: "none"
                    });
                  } else {
                    retWxUpdate = "";
                    resolve(true);
                  }
                });
                updateManager.onUpdateReady(function() {
                  console.warn("\u5c0f\u6e38\u620f\u6709\u65b0\u7248\u672c,\u5e94\u7528\u65b0\u7248\u672c\u5e76\u91cd\u542f");
                  tt.showToast({
                    title: "\u65b0\u7248\u672c\u4e0b\u8f7d\u5b8c\u6210",
                    icon: "none"
                  });
                  wx.showModal({
                    showCancel: true,
                    cancelText: "\u53d6\u6d88",
                    cancelColor: "#000000",
                    confirmText: "\u786e\u5b9a",
                    confirmColor: "#576B95",
                    title: "\u66f4\u65b0\u63d0\u793a",
                    content: "\u65b0\u7248\u672c\u5df2\u7ecf\u51c6\u5907\u597d\uff0c\u662f\u5426\u91cd\u542f\u5e94\u7528?",
                    fail: function() {},
                    complete: function() {},
                    success: function(res) {
                      updateManager.applyUpdate();
                    }
                  });
                  retWxUpdate = "fail, need update";
                  resolve(true);
                });
                updateManager.onUpdateFailed(function(err) {
                  console.error("\u5c0f\u6e38\u620f\u6709\u65b0\u7248\u672c,\u4e0b\u8f7d\u5931\u8d25", err);
                  tt.showToast({
                    title: "\u7248\u672c\u66f4\u65b0\u5931\u8d25\uff0c\u8bf7\u91cd\u8bd5",
                    icon: "none"
                  });
                  wx.showModal({
                    showCancel: true,
                    cancelText: "\u53d6\u6d88",
                    cancelColor: "#000000",
                    confirmText: "\u786e\u5b9a",
                    confirmColor: "#576B95",
                    title: "\u66f4\u65b0\u63d0\u793a",
                    content: "\u65b0\u7248\u672c\u5df2\u7ecf\u4e0a\u7ebf\uff0c\u8bf7\u9000\u51fa\u91cd\u542f!",
                    fail: function() {},
                    complete: function() {},
                    success: function(res) {
                      cc.game.end();
                    }
                  });
                  retWxUpdate = "fail, onUpdateFailed";
                  resolve(true);
                });
                resolve(true);
              }) ];

             case 1:
              _a.sent();
              null != retWxUpdate && "" != retWxUpdate && (errMsg = retWxUpdate);
              return [ 2, errMsg ];
            }
          });
        });
      };
      TTSDKMiniGame.prototype.hasRecorder = function() {
        var tt = window["tt"];
        if (!(null === tt || void 0 === tt ? void 0 : tt.getGameRecorderManager)) return false;
        return true;
      };
      TTSDKMiniGame.prototype.gameRecorderAuto = function(callback, caller) {
        void 0 === callback && (callback = null);
        void 0 === caller && (caller = null);
        this.gameRecorderStart(callback, caller);
      };
      TTSDKMiniGame.prototype.runRecorderCallback = function(userData) {
        var callback = this.recorderCallback;
        var caller = this.recorderCaller;
        callback && (caller ? callback.call(caller, userData) : callback(userData));
      };
      TTSDKMiniGame.prototype.gameRecorderStart = function(callback, caller) {
        var _this = this;
        void 0 === callback && (callback = null);
        void 0 === caller && (caller = null);
        if (!this.hasRecorder()) return;
        var tt = window["tt"];
        this.recorderCallback = callback;
        this.recorderCaller = caller;
        var recorder = this.gameRecorder;
        if (!recorder) {
          recorder = tt.getGameRecorderManager();
          recorder.onStart(function(res) {
            console.log("\u5f55\u5c4f\u5f00\u59cb");
            _this.recorderStatus = 1;
            _this.runRecorderCallback(_this.recorderStatus);
          });
          recorder.onStop(function(res) {
            if (_this.gameRecorder) {
              _this.recorderStatus = -1;
              _this.runRecorderCallback(_this.recorderStatus);
              var videoPath_1 = res.videoPath;
              var touchEndHelper_1 = function(res) {
                _this.gameRecorderShare(videoPath_1);
                tt.offTouchEnd(touchEndHelper_1);
              };
              tt.onTouchEnd(touchEndHelper_1);
            }
            tt.showModal({
              title: "\u5f55\u5c4f\u7ed3\u675f",
              content: "\u5f55\u5c4f\u65f6\u95f4\u8d85\u8fc75\u5206\u949f\u81ea\u52a8\u7ed3\u675f,\u5173\u95ed\u540e\u518d\u6b21\u70b9\u51fb\u5c4f\u5e55\u53ef\u5206\u4eab"
            });
          });
          recorder.onPause(function(res) {
            console.log("\u5f55\u5c4f\u6682\u505c");
            _this.recorderStatus = 2;
            _this.runRecorderCallback(_this.recorderStatus);
          });
          recorder.onResume(function(res) {
            console.log("\u5f55\u5c4f\u7ee7\u7eed");
            _this.recorderStatus = 1;
            _this.runRecorderCallback(_this.recorderStatus);
          });
          recorder.onError(function(res) {
            console.log("\u5f55\u5c4f\u9519\u8bef\u7684\u4fe1\u606f");
            console.log(res);
          });
          recorder.onInterruptionBegin(function() {
            console.log("\u5f55\u5c4f\u4e2d\u65ad\u5f00\u59cb");
          });
          recorder.onInterruptionEnd(function() {
            console.log("\u5f55\u5c4f\u4e2d\u65ad\u7ed3\u675f");
          });
          this.gameRecorder = recorder;
        }
        if (this.recorderStatus <= 0) recorder.start({
          duration: 300
        }); else if (2 == this.recorderStatus) this.gameRecorderResume(); else if (this.recorderStatus > 0) {
          recorder.onStop(function(res) {
            console.log(res);
            console.log("\u5f55\u5c4f\u7ed3\u675f:", res.videoPath);
            if (_this.gameRecorder) {
              _this.recorderStatus = -1;
              _this.runRecorderCallback(_this.recorderStatus);
              _this.gameRecorderShare(res.videoPath);
            } else console.log("\u4e0d\u5206\u4eab");
          });
          this.gameRecorderStop();
        }
      };
      TTSDKMiniGame.prototype.destroyGmeRecorder = function() {
        if (this.gameRecorder) {
          this.recorderCallback = null;
          this.recorderCaller = null;
          this.gameRecorder = null;
          this.recorderStatus = 0;
          this.gameRecorderStop(false);
        }
      };
      TTSDKMiniGame.prototype.gameRecorderStop = function(share) {
        void 0 === share && (share = true);
        if ("function" != typeof wx.getGameRecorderManager) return;
        var recorder = tt.getGameRecorderManager();
        !share && this.gameRecorder && recorder.onStop(function(res) {
          console.log(res);
          console.log("\u5f55\u5c4f\u7ed3\u675f\u4e0d\u5206\u4eab:", res.videoPath);
        });
        recorder.stop();
      };
      TTSDKMiniGame.prototype.gameRecorderShare = function(videoPath) {
        console.log("gameRecorderShare:", videoPath);
        if (!videoPath) return;
        tt.shareAppMessage({
          title: "\u89c6\u9891\u5206\u4eab",
          channel: "video",
          extra: {
            videoPath: videoPath,
            withVideoId: true
          },
          success: function(res) {
            console.log(res);
            res && tt.showModal({
              title: "\u5206\u4eab\u6210\u529f",
              content: "\u89c6\u9891\u5206\u4eab\u6210\u529f"
            });
          },
          fail: function(e) {
            console.warn(e);
            if (e) {
              console.log(JSON.stringify(e));
              var errNo = e.errNo;
              21105 == errNo ? tt.showModal({
                title: "\u5206\u4eab\u5931\u8d25",
                content: "\u5f55\u5c4f\u5931\u8d25\uff1a\u5f55\u5c4f\u65f6\u957f\u4f4e\u4e8e 3 \u79d2"
              }) : 10502 == errNo ? console.log("\u5df2\u53d6\u6d88\u5206\u4eab") : tt.showModal({
                title: "\u5206\u4eab\u5931\u8d25",
                content: "\u89c6\u9891\u5206\u4eab\u5931\u8d25" + (errNo || "")
              });
            }
          }
        });
      };
      TTSDKMiniGame.prototype.gameRecorderPause = function() {
        if ("function" != typeof wx.getGameRecorderManager) return;
        var recorder = tt.getGameRecorderManager();
        recorder.pause();
      };
      TTSDKMiniGame.prototype.gameRecorderResume = function() {
        if ("function" != typeof wx.getGameRecorderManager) return;
        var recorder = tt.getGameRecorderManager();
        recorder.resume();
      };
      TTSDKMiniGame.prototype.gameRecorderClipVideo = function(res, time) {
        if ("function" != typeof wx.getGameRecorderManager) return;
        var videoPath = res.videoPath;
        var recorder = tt.getGameRecorderManager();
        recorder.clipVideo({
          path: videoPath,
          timeRange: [ time, 0 ],
          success: function(res) {
            console.log(videoPath);
          },
          fail: function(e) {
            console.error(e);
          }
        });
      };
      TTSDKMiniGame.prototype.checkCeBianLan = function() {
        return __awaiter(this, void 0, void 0, function() {
          var result, tt;
          return __generator(this, function(_a) {
            switch (_a.label) {
             case 0:
              result = false;
              tt = window["tt"];
              if (!(null === tt || void 0 === tt ? void 0 : tt.checkScene)) return [ 3, 2 ];
              return [ 4, new Promise(function(resolve, reject) {
                tt.checkScene({
                  scene: "sidebar",
                  success: function(res) {
                    console.log("check scene success: ", res.isExist);
                    result = res.isExist;
                    resolve(true);
                  },
                  fail: function(res) {
                    console.log("check scene fail:", res);
                    reject(false);
                  }
                });
              }) ];

             case 1:
              _a.sent();
              _a.label = 2;

             case 2:
              return [ 2, result ];
            }
          });
        });
      };
      TTSDKMiniGame.prototype.listenGameShow = function() {
        var _this = this;
        var tt = window["tt"];
        if (tt) {
          console.log("\u76d1\u542c\u5934\u6761onShow");
          tt.onShow(function(res) {
            console.log("\u542f\u52a8\u53c2\u6570\uff1a", res.query);
            console.log("\u6765\u6e90\u4fe1\u606f\uff1a", res.refererInfo);
            console.log("\u573a\u666f\u503c\uff1a", res.scene);
            console.log("\u542f\u52a8\u573a\u666f\u5b57\u6bb5\uff1a", res.launch_from, ", ", res.location);
            res.launch_from && "sidebar_card" == res.location && (_this._getJumpReward = true);
          });
        }
      };
      TTSDKMiniGame.prototype.getJumpReward = function() {
        return this._getJumpReward;
      };
      TTSDKMiniGame.prototype.jumpToSlideBar = function() {
        return __awaiter(this, void 0, void 0, function() {
          var tt, result;
          return __generator(this, function(_a) {
            switch (_a.label) {
             case 0:
              tt = window["tt"];
              result = false;
              if (!tt) return [ 3, 2 ];
              return [ 4, new Promise(function(resolve, reject) {
                tt.navigateToScene({
                  scene: "sidebar",
                  fail: function(msg) {
                    FW_1.FW.showTip("\u8df3\u8f6c\u5931\u8d25");
                    console.log("\u8df3\u8f6c\u5931\u8d25:", msg);
                    result = false;
                    reject();
                  },
                  success: function() {
                    console.log("\u8df3\u8f6c\u6210\u529f");
                    result = true;
                    resolve(true);
                  }
                });
              }) ];

             case 1:
              _a.sent();
              _a.label = 2;

             case 2:
              return [ 2, result ];
            }
          });
        });
      };
      TTSDKMiniGame.__cname = "TTSDKMiniGame";
      __decorate([ Container_1.injectField("AppCustomData") ], TTSDKMiniGame.prototype, "AppCustomData", void 0);
      __decorate([ Container_1.injectField("ModelMgr") ], TTSDKMiniGame.prototype, "ModelMgr", void 0);
      __decorate([ Container_1.injectField("TimeUtil") ], TTSDKMiniGame.prototype, "TimeUtil", void 0);
      __decorate([ Container_1.injectField("ModelChapterNew") ], TTSDKMiniGame.prototype, "ModelChapterNew", void 0);
      return TTSDKMiniGame;
    }(SDKInterface_1.SDKInterface);
    exports.TTSDKMiniGame = TTSDKMiniGame;
    cc._RF.pop();
  }, {
    "../../app/game/ctrl/sdk/SDKInterface": void 0,
    "../../app/game/ctrl/sdk/SDKMgr": void 0,
    "../../engine/EngineMain": void 0,
    "../../framework/FW": void 0,
    "../../framework/container/Container": void 0,
    "../../libutil/MD5Util": void 0
  } ],
  ttmini_game: [ function(require, module, exports) {
    "use strict";
    cc._RF.push(module, "7b66a/L2x5DnpwscTIIG6vb", "ttmini_game");
    "use strict";
    function consoleLog(a) {
      var b = getCache("init_sdk_isprint");
      getCache("init_wx_env");
      b = 1, 1 == b && console.log(a);
    }
    function getEnvVersion() {
      var a = tt.env.VERSION;
      console.log("env ", a), setCache("init_wx_env", a);
    }
    function formatParams(a) {
      var b, c = [];
      for (b in a) a.hasOwnProperty(b) && c.push(encodeURIComponent(b) + "=" + encodeURIComponent(a[b]));
      return c.join("&");
    }
    function request(a, b, c, d, e) {
      var f = {};
      "undefined" == typeof e && (e = " default "), "undefined" != typeof d && "get" == d.toLowerCase() ? (f.url = a + (a.indexOf("?") > -1 ? "&" : "?") + formatParams(b), 
      f.data = {}) : (f.url = a, f.data = b), consoleLog(a), consoleLog(b), f.header = {
        "content-type": "application/json"
      }, f.success = function(a) {
        consoleLog(e + " wx req suc " + JSON.stringify(a)), "function" == typeof c && c(a.data);
      }, f.fail = function(a) {
        consoleLog(e + " wx req fail " + JSON.stringify(a)), "function" == typeof c && c(a.data);
      }, f.complete = function(a) {}, tt.request(f);
    }
    function setCache(a, b) {
      try {
        tt.setStorageSync(a, b);
      } catch (c) {
        console.log("set cache error. " + JSON.stringify(c));
      }
    }
    function getCache(a) {
      try {
        var b = tt.getStorageSync(a);
        return b;
      } catch (c) {
        console.log("get cache error. " + JSON.stringify(c));
      }
    }
    function delCache(a) {
      try {
        var b = tt.removeStorageSync(a);
        return b;
      } catch (c) {
        console.log("del cache error. " + JSON.stringify(c));
      }
    }
    function deviceInfo() {
      tt.getSystemInfoSync();
    }
    function clearInitCache() {
      delCache("init_wx_scene"), delCache("init_wx_env"), delCache("init_wx_appid"), delCache("init_sdk_appid"), 
      delCache("init_sdk_adid"), delCache("init_sdk_gameid"), delCache("init_sdk_gamekey"), 
      delCache("init_sdk_openid"), delCache("init_sdk_ext"), delCache("init_sdk_isprint"), 
      delCache("init_wx_device_type"), delCache("init_is_fromshare"), delCache("init_fromshare_id"), 
      delCache("init_fromshare_appid"), delCache("init_fromshare_adid"), delCache("init_fromshare_uid"), 
      delCache("init_promot_callback"), delCache("init_referrer_appid"), delCache("init_referrer_data");
    }
    function setInitCahe(a) {
      var b = tt.getLaunchOptionsSync();
      console.log("init loadInfo." + JSON.stringify(b)), setCache("init_wx_scene", b.scene);
      var c = b.query;
      if ("undefined" != typeof c.scene) {
        var d = decodeURIComponent(c.scene), e = d.match(new RegExp("(^|&)" + sdkWxminiPrefix + "aid=([^&]*)(&|$)")), f = d.match(new RegExp("(^|&)" + sdkWxminiPrefix + "adid=([^&]*)(&|$)"));
        e && "undefined" != typeof e[2] && e[2] && (c.aid2 = e[2]), f && "undefined" != typeof f[2] && f[2] && (c.adid = f[2]), 
        e && "undefined" != typeof e[2] && e[2] && (c[sdkWxminiPrefix + "aid"] = e[2]), 
        f && "undefined" != typeof f[2] && f[2] && (c[sdkWxminiPrefix + "adid"] = f[2]), 
        console.log(d + "," + JSON.stringify(c));
      }
      var g = a.wx_appid, h = a.game_id, i = a.game_key, j = "undefined" != typeof c[sdkWxminiPrefix + "aid"] ? c[sdkWxminiPrefix + "aid"] : defaultAppId, k = "undefined" != typeof c[sdkWxminiPrefix + "adid"] ? c[sdkWxminiPrefix + "adid"] : defaultAdid;
      setCache("init_sdk_appid", j), setCache("init_wx_appid", g), setCache("init_sdk_adid", k), 
      setCache("init_sdk_gameid", h), setCache("init_sdk_gamekey", i), setCache("init_wx_device_type", tt.getSystemInfoSync().platform);
      var l = "undefined" != typeof c.ifs ? c.ifs : 0, m = "undefined" != typeof c.fsid ? c.fsid : 0, n = "undefined" != typeof c.fsaid ? c.fsaid : 0, o = "undefined" != typeof c.fsadid ? c.fsadid : 0, p = "undefined" != typeof c.fsuid ? c.fsuid : 0;
      setCache("init_is_fromshare", l), setCache("init_fromshare_id", m), setCache("init_fromshare_appid", n), 
      setCache("init_fromshare_adid", o), setCache("init_fromshare_uid", p);
      var q = "undefined" != typeof c.cp_callback_url ? c.cp_callback_url : "";
      setCache("init_promot_callback", q);
      var r = 0, s = "";
      "undefined" != typeof b.referrerInfo && (r = b.referrerInfo.appId, s = b.referrerInfo.extraData), 
      setCache("init_referrer_appid", r), setCache("init_referrer_data", s);
    }
    function getUserInfo(a, b, c, d, e) {
      var f = tt.getLaunchOptionsSync().query;
      console.log(f);
      var g = tt.getSystemInfoSync();
      console.log("------\uff1a" + g.appName);
      var h = sdkBaseUrl + "getUserInfo", i = {
        app_id: getCache("init_sdk_appid"),
        game_id: getCache("init_sdk_gameid"),
        game_key: getCache("init_sdk_gamekey"),
        adid: getCache("init_sdk_adid"),
        wx_appid: getCache("init_wx_appid"),
        wx_scene: getCache("init_wx_scene"),
        wx_refer_appid: getCache("init_referrer_appid"),
        wx_refer_data: getCache("init_referrer_data"),
        wx_device_type: getCache("init_wx_device_type"),
        wx_code: a,
        wx_anonymousCode: b,
        is_fromshare: getCache("init_is_fromshare"),
        fromshare_id: getCache("init_fromshare_id"),
        fromshare_appid: getCache("init_fromshare_appid"),
        fromshare_uid: getCache("init_fromshare_uid"),
        fromshare_adid: getCache("init_fromshare_adid"),
        promoto_callback: getCache("init_promot_callback"),
        wx_res: "undefined" != typeof e ? e : "",
        wx_query: JSON.stringify(f),
        environment: g.appName
      }, j = function j(a) {
        if (console.log("init.back: " + JSON.stringify(a)), 0 == a.code) {
          var b = a.data;
          userInfo = b, setCache("init_sdk_appid", b.sdk_appid), setCache("init_sdk_openid", b.sdk_openid), 
          setCache("init_sdk_ext", b.sdk_ext);
          var c = b.sdk_ext;
          setCache("init_sdk_isprint", c.iprt), consoleLog(c);
        }
        d(a);
      };
      console.log("i:" + h + ",d:" + JSON.stringify(i)), request(h, i, j, "post", "getUserInfo");
    }
    function init(a, b, c) {
      console.log("ttv.**********"), console.log(a), clearInitCache(), getEnvVersion(), 
      setInitCahe(a);
      var d = {
        force: !1,
        success: function success(d) {
          console.log("init succ, " + JSON.stringify(d)), setCache("tt_isLogin", d.isLogin), 
          d.isLogin ? (d.anonymousCode || d.code) && getUserInfo(d.code, d.anonymousCode, a, b, c) : tt.showModal({
            title: "\u6e29\u99a8\u63d0\u793a",
            content: "\u5f53\u524d\u662f\u6e38\u5ba2\u8d26\u53f7\uff0c\u4e3a\u4e86\u60a8\u7684\u8d26\u53f7\u5b89\u5168\uff0c\u8bf7\u5148\u767b\u5f55\uff01",
            success: function success(e) {
              e.confirm ? tt.login({
                success: function success(a) {
                  var c = {
                    code: 408,
                    message: JSON.stringify(d)
                  };
                  b(c);
                },
                fail: function fail(e) {
                  console.log("init login cancel, " + JSON.stringify(e)), (d.anonymousCode || d.code) && getUserInfo(d.code, d.anonymousCode, a, b, c);
                }
              }) : e.cancel && (d.anonymousCode || d.code) && getUserInfo(d.code, d.anonymousCode, a, b, c);
            }
          });
        },
        fail: function fail(a) {
          console.log("init fail, " + JSON.stringify(a));
          var c = {
            code: 408,
            message: JSON.stringify(a)
          };
          b(c);
        },
        complete: function complete(a) {
          console.log("init complete, " + JSON.stringify(a));
        }
      };
      tt.login(d);
    }
    function pay(a, b) {
      consoleLog("pay " + JSON.stringify(a));
      var c = getCache("tt_isLogin");
      if (!c) return void tt.showModal({
        title: "\u6e29\u99a8\u63d0\u793a",
        content: "\u5f53\u524d\u662f\u6e38\u5ba2\u8d26\u53f7\u65e0\u6cd5\u8fdb\u884c\u5145\u503c\uff0c\u8bf7\u5148\u767b\u5f55\uff01",
        success: function success(a) {
          a.confirm && tt.login({
            success: function success(a) {
              tt.exitMiniProgram();
            }
          });
        }
      });
      console.log("getp here");
      var d = sdkBaseUrl + "getPayType", e = {
        game_id: getCache("init_sdk_gameid"),
        wx_appid: getCache("init_wx_appid"),
        wx_device_type: getCache("init_wx_device_type")
      }, f = function f(c) {
        console.log("getCp " + JSON.stringify(c)), 0 == c.code && ("undefined" == typeof c.paytype || null == c.paytype ? changlePayType(0, a, b) : changlePayType(c.paytype, a, b));
      };
      request(d, e, f, "post", "changlePayType");
    }
    function changlePayType(a, b, c) {
      var d = sdkBaseUrl + "userOrder/preparepay";
      b.wx_appid = userInfo.wx_appid, b.environment = tt.getSystemInfoSync().appName, 
      0 == a ? (b.payname = "pay_zj_minigame", b.adid = userInfo.sdk_adid, payMidas(d, b, c)) : 1 == a ? (b.payname = "pay_weixin_pub_kefu", 
      b.adid = userInfo.sdk_adid, payWithPic(d, b, c)) : 2 == a ? (b.payname = "pay_weixin_pub_kefu", 
      b.adid = userInfo.sdk_adid, payWithShowModal(d, b, c)) : console.log("error pay.");
    }
    function payMidas(a, b, c) {
      var d = function d(a) {
        if (console.log("getp " + JSON.stringify(a)), 0 == a.code) {
          console.log("midas...");
          var b = a.data, d = function d(b) {
            var c = b;
            return c.app_id = userInfo.sdk_appid, c.game_id = userInfo.sdk_gameid, c.user_id = userInfo.sdk_ext.i, 
            c.orderno = a.orderno, c;
          };
          b.success = function(a) {
            consoleLog("pback suc." + JSON.stringify(a));
            var b = sdkBaseUrl + "payCallbackFront", c = d(a);
            c.is_success = 1;
            var e = function e(a) {
              console.log("suc. payCallback." + JSON.stringify(a));
            };
            request(b, c, e, "post", "payCallbackFront");
          }, b.fail = function(a) {
            consoleLog("pback fail." + JSON.stringify(a)), consoleLog("pback fail2." + a.code), 
            consoleLog("pback fail3." + a.message);
            var b = sdkBaseUrl + "addMidasFrontPayCallbackLog", c = d(a);
            c.is_success = 0;
            var e = function e(a) {
              console.log("fail payCallback." + JSON.stringify(a));
            };
            request(b, c, e, "post", "addMidasFrontPayCallbackLog");
          }, b.complete = function(a) {
            console.log("pback complete." + JSON.stringify(a));
          }, console.log("\u6253\u5370\u652f\u4ed8\u8bf7\u6c42\u53c2\u6570"), console.log(b), 
          tt.requestGamePayment(b);
        } else console.log(a), c(a);
      };
      request(a, b, d, "post", "preparepay");
    }
    function payWithPic(a, b, c) {
      var d = function d(a) {
        console.log("getpayMiniPro. " + JSON.stringify(a)), 0 == a.code ? (console.log("minipro..."), 
        void 0 != a.data && (console.log(a), a.data ? tt.previewImage({
          urls: [ a.data ]
        }) : tt.showToast({
          title: "\u8ba2\u5355\u83b7\u53d6\u5931\u8d25",
          icon: "loading",
          duration: 1e3
        }))) : (console.log(a), c(a));
      };
      request(a, b, d, "post", "h5Propreparepay");
    }
    function payWithShowModal(a, b, c) {
      var e = function e(a) {
        console.log("getpayMiniPro. " + JSON.stringify(a)), 0 == a.code ? (console.log("minipro..."), 
        void 0 != d.data && tt.showModal({
          title: "\u6e29\u99a8\u63d0\u793a",
          content: "\u70b9\u51fb[\u786e\u8ba4]\u6309\u94ae\u540e\u4fbf\u53ef\u4ee5\u590d\u5236\u5145\u503c\u94fe\u63a5\uff0c\u7136\u540e\u624b\u52a8\u53bb\u6d4f\u89c8\u5668\u6253\u5f00\u94fe\u63a5\u8fdb\u884c\u5145\u503c\u3002",
          success: function success(a) {
            a.confirm ? (console.log("confirm, continued"), tt.setClipboardData({
              data: d.data,
              success: function success(a) {},
              fail: function fail(a) {}
            })) : a.cancel && console.log("cancel, cold");
          },
          fail: function fail(a) {}
        })) : (console.log(a), c(a));
      };
      request(a, b, e, "post", "h5Propreparepay");
    }
    function payKefu(a, b, c) {
      var d = function d(a) {
        console.log("getpayMiniPro " + JSON.stringify(a)), 0 == a.code ? (console.log("payKefu..."), 
        tt.showModal({
          title: "\u6e29\u99a8\u63d0\u793a",
          content: "\u56de\u590d1\uff0c\u8fdb\u884c\u5145\u503c",
          showCancel: !1,
          confirmText: "\u786e\u5b9a",
          success: function success(a) {
            var b = "https://wxmjb-h5cdn.51qimiao.com/h5site/p/19/09/20/p3_e3dfbd9b818d55fdc9b854ffccafd53c.gif", c = {
              showMessageCard: !0,
              sendMessageImg: b
            };
            tt.openCustomerServiceConversation(c);
          }
        })) : (console.log(a), c(a));
      };
      request(a, b, d, "post", "miniPropreparepay");
    }
    function share(a, b, c, d, e, f, g, h, i) {
      var j = userInfo.sdk_ext, k = j.f + "&templateId=" + c + "&adid=" + userInfo.sdk_adid;
      "undefined" != typeof h && "" != h && (k = k + "&" + h), "undefined" != typeof a && "" != a && (k = k + "&fsid=" + a);
      var l = {
        channel: b,
        templateId: c,
        title: d,
        desc: e,
        query: k,
        imageUrl: f,
        extra: g,
        success: function success() {
          i(1);
        },
        fail: function fail(a) {
          i(a);
        }
      };
      consoleLog("share.." + JSON.stringify(l)), tt.shareAppMessage(l);
    }
    function shareInit(a, b, c, d, e, f, g, h) {
      var i = userInfo.sdk_ext, j = i.f + "&templateId=" + b + "&adid=" + userInfo.sdk_adid;
      "undefined" != typeof g && "" != g && (j = j + "&" + g), tt.showShareMenu();
      var k = {
        channel: a,
        templateId: b,
        title: c,
        desc: d,
        query: j,
        imageUrl: e,
        extra: f,
        success: function success() {
          h(1);
        },
        fail: function fail(a) {
          h(a);
        }
      };
      consoleLog("shari.." + JSON.stringify(k));
      var l = function l() {
        return k;
      };
      tt.onShareAppMessage(l);
    }
    function bindPhone(a, b) {
      var c = sdkBaseUrl + "bindPhone", d = {
        app_id: userInfo.sdk_appid,
        open_id: userInfo.sdk_openid,
        phone: a
      };
      request(c, d, b, "post", "bindPhone");
    }
    function realVerify(a, b, c) {
      var d = sdkBaseUrl + "realVerify", e = {
        app_id: userInfo.sdk_appid,
        open_id: userInfo.sdk_openid,
        idname: a,
        idcard: b
      };
      request(d, e, c, "post", "realVerify");
    }
    function serverChooseLog(a, b) {
      reportLog("serverChooseLog", a, b);
    }
    function enterGameLog(a, b) {
      reportLog("enterGameLog", a, b);
    }
    function newcomerLog(a, b) {
      reportLog("newcomerLog", a, b);
    }
    function createRoleLog(a, b) {
      reportLog("createRoleLog", a, b);
    }
    function levelUpLog(a, b) {
      reportLog("levelUpLog", a, b);
    }
    function guanqiaStartLog(a, b) {
      a.wx_appid = userInfo.wx_appid, a.wx_scene = getCache("init_wx_scene"), reportLog("guanqiaStartLog", a, b);
    }
    function guanqiaEndLog(a, b) {
      a.wx_appid = userInfo.wx_appid, a.wx_scene = getCache("init_wx_scene"), reportLog("guanqiaEndLog", a, b);
    }
    function reportLog(a, b, c) {
      consoleLog(a);
      var d = function d() {
        var d = sdkLogUrl + a;
        b.adid = userInfo.sdk_adid, b.is_fromshare = getCache("init_is_fromshare"), b.wx_device_type = getCache("init_wx_device_type"), 
        request(d, b, c, "post", a);
      };
      switch (a) {
       case "createRoleLog":
       case "enterGameLog":
       case "levelUpLog":
       case "newcomerLog":
       case "guanqiaStartLog":
       case "guanqiaEndLog":
        d();
      }
    }
    function getPromoteList(a, b) {
      var c = sdkBaseUrl + "getPromoteList", d = {
        type: a,
        wx_device_type: getCache("init_wx_device_type"),
        game_id: getCache("init_sdk_gameid")
      };
      request(c, d, b, "post", "getPromoteList");
    }
    function promotoClickLog(a, b) {
      var c = sdkBaseUrl + "promotoClickLog";
      a.wx_appid = userInfo.wx_appid, a.wx_scene = getCache("init_wx_scene"), a.wx_device_type = getCache("init_wx_device_type"), 
      request(c, a, b, "post", "promotoClickLog");
    }
    function promotoExposeLog(a, b) {
      var c = sdkLogUrl + "promotoExposeLog";
      a.wx_appid = userInfo.wx_appid, a.wx_scene = getCache("init_wx_scene"), a.wx_device_type = getCache("init_wx_device_type"), 
      request(c, a, b, "post", "promotoExposeLog");
    }
    function isMessageNormal(a, b, c) {
      var d = {
        game_id: a,
        content: b
      };
      tt.request({
        url: sdkBaseUrl + "msgSecCheck",
        data: d,
        success: function success(a) {
          c(a.data);
        }
      });
    }
    function adComponentCreate(a, b, c, d, e, f) {
      var g = userInfo.sdk_adcomponent, h = {
        code: 0,
        message: "success",
        data: {
          component: ""
        }
      };
      if ("" != typeof g && "undefined" != typeof g[b] && "undefined" != typeof g[b][a]) {
        var i = "";
        if (g[b][a].id == c) {
          if (d = Math.max(d, g[b][a].intervals), 1 == b) i = tt.createRewardedVideoAd({
            adUnitId: c
          }); else if (2 == b) i = tt.createBannerAd({
            adUnitId: c,
            adIntervals: d,
            style: e
          }); else if (3 == b) i = tt.createInterstitialAd({
            adUnitId: c
          }); else {
            var j = "type error, not exists: " + b;
            h.code = 400, h.message = j;
          }
          h.data.component = i;
        } else {
          var j = "id error, not exists: " + c;
          h.code = 400, h.message = j;
        }
      } else {
        var j = "para loss: " + b + ", " + a;
        h.code = 400, h.message = j;
      }
      f(h);
    }
    var sdkLogUrl = "https://h5api.huixinhuyu.com/go/zijie/log/", sdkBaseUrl = "https://h5api.huixinhuyu.com/go/zijie/", sdkWxminiPrefix = "hxhy_", defaultAppId = 2600005, defaultAdid = 1e3, userInfo = {
      wx_appid: 0,
      sdk_appid: 0,
      sdk_gameid: 0,
      sdk_gamekey: "",
      sdk_adid: 0,
      sdk_openid: "",
      sdk_is_focus: 0,
      sdk_focus_wx_appid: "",
      sdk_is_share: 0,
      sdk_is_realverify: 0,
      sdk_is_bindphone: 0,
      sdk_is_fcm: 0,
      sdk_shareconf: "",
      sdk_ext: "",
      sdk_sign: "",
      sdk_adcomponent: ""
    };
    module.exports = {
      init: init,
      pay: pay,
      share: share,
      shareInit: shareInit,
      bindPhone: bindPhone,
      realVerify: realVerify,
      enterGameLog: enterGameLog,
      createRoleLog: createRoleLog,
      levelUpLog: levelUpLog,
      newcomerLog: newcomerLog,
      guanqiaStartLog: guanqiaStartLog,
      guanqiaEndLog: guanqiaEndLog,
      isMessageNormal: isMessageNormal,
      adComponentCreate: adComponentCreate,
      promotoExposeLog: promotoExposeLog
    };
    cc._RF.pop();
  }, {} ]
}, {}, [ "ModuleTTSDK", "TTSDKMiniGame", "ttmini_game" ]);